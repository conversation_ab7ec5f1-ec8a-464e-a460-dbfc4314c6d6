import 'dart:io';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../inventory_service.dart';

// Provider pour le service CSV
final csvServiceProvider = Provider<CSVService>((ref) {
  final inventoryService = ref.watch(inventoryServiceProvider);
  return CSVService(inventoryService);
});

class CSVService {
  final InventoryService _inventoryService;

  CSVService(this._inventoryService);

  // Exporter les produits au format CSV
  Future<String?> exportProducts() async {
    try {
      final products = await _inventoryService.getProducts();

      if (products.isEmpty) {
        return null;
      }

      // Créer les en-têtes
      final headers = [
        'ID',
        'Nom',
        'Catégorie ID',
        'Catégorie',
        'Prix',
        'Quantité',
        'Description',
        'Image URL',
        'Créé le',
        'Mis à jour le',
      ];

      // Créer les lignes
      final rows =
          products
              .map(
                (product) => [
                  product.id,
                  product.name,
                  product.categoryId,
                  product.categoryName,
                  product.price,
                  product.quantity,
                  product.description ?? '',
                  product.imageUrl ?? '',
                  product.createdAt.toIso8601String(),
                  product.updatedAt.toIso8601String(),
                ],
              )
              .toList();

      // Ajouter les en-têtes au début
      rows.insert(0, headers);

      // Convertir en CSV
      final csv = const ListToCsvConverter().convert(rows);

      // Sauvegarder le fichier
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/products_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File(path);
      await file.writeAsString(csv);

      return path;
    } catch (e) {
      debugPrint('Erreur lors de l\'exportation des produits: $e');
      return null;
    }
  }

  // Exporter les catégories au format CSV
  Future<String?> exportCategories() async {
    try {
      final categories = await _inventoryService.getCategories();

      if (categories.isEmpty) {
        return null;
      }

      // Créer les en-têtes
      final headers = [
        'ID',
        'Nom',
        'Prix',
        'Description',
        'Créé le',
        'Mis à jour le',
      ];

      // Créer les lignes
      final rows =
          categories
              .map(
                (category) => [
                  category.id,
                  category.name,
                  category.price,
                  category.description ?? '',
                  category.createdAt.toIso8601String(),
                  category.updatedAt.toIso8601String(),
                ],
              )
              .toList();

      // Ajouter les en-têtes au début
      rows.insert(0, headers);

      // Convertir en CSV
      final csv = const ListToCsvConverter().convert(rows);

      // Sauvegarder le fichier
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/categories_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File(path);
      await file.writeAsString(csv);

      return path;
    } catch (e) {
      debugPrint('Erreur lors de l\'exportation des catégories: $e');
      return null;
    }
  }

  // Importer des produits depuis un fichier CSV
  Future<int> importProducts() async {
    try {
      // Sélectionner le fichier
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        return 0;
      }

      final file = File(result.files.first.path!);
      final content = await file.readAsString();

      // Convertir le CSV en liste
      final rows = const CsvToListConverter().convert(content);

      if (rows.isEmpty || rows.length <= 1) {
        return 0;
      }

      // Récupérer les en-têtes
      final headers = rows.first.map((e) => e.toString()).toList();

      // Vérifier les en-têtes minimaux requis
      final requiredHeaders = ['Nom', 'Catégorie ID', 'Prix', 'Quantité'];
      for (final header in requiredHeaders) {
        if (!headers.contains(header)) {
          throw Exception('En-tête manquant: $header');
        }
      }

      // Récupérer les index des colonnes
      final nameIndex = headers.indexOf('Nom');
      final categoryIdIndex = headers.indexOf('Catégorie ID');
      final categoryNameIndex = headers.indexOf('Catégorie');
      final priceIndex = headers.indexOf('Prix');
      final quantityIndex = headers.indexOf('Quantité');
      final descriptionIndex = headers.indexOf('Description');
      final imageUrlIndex = headers.indexOf('Image URL');

      // Importer les produits
      int importedCount = 0;
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.length < headers.length) {
          continue; // Ignorer les lignes incomplètes
        }

        final product = Product(
          id: const Uuid().v4(),
          name: row[nameIndex].toString(),
          categoryId: row[categoryIdIndex].toString(),
          categoryName:
              categoryNameIndex >= 0 ? row[categoryNameIndex].toString() : '',
          price: double.tryParse(row[priceIndex].toString()) ?? 0,
          quantity: int.tryParse(row[quantityIndex].toString()) ?? 0,
          description:
              descriptionIndex >= 0 ? row[descriptionIndex].toString() : null,
          imageUrl: imageUrlIndex >= 0 ? row[imageUrlIndex].toString() : null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final success = await _inventoryService.addProduct(product);
        if (success) {
          importedCount++;
        }
      }

      return importedCount;
    } catch (e) {
      debugPrint('Erreur lors de l\'importation des produits: $e');
      rethrow;
    }
  }

  // Importer des catégories depuis un fichier CSV
  Future<int> importCategories() async {
    try {
      // Sélectionner le fichier
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        return 0;
      }

      final file = File(result.files.first.path!);
      final content = await file.readAsString();

      // Convertir le CSV en liste
      final rows = const CsvToListConverter().convert(content);

      if (rows.isEmpty || rows.length <= 1) {
        return 0;
      }

      // Récupérer les en-têtes
      final headers = rows.first.map((e) => e.toString()).toList();

      // Vérifier les en-têtes minimaux requis
      final requiredHeaders = ['Nom', 'Prix'];
      for (final header in requiredHeaders) {
        if (!headers.contains(header)) {
          throw Exception('En-tête manquant: $header');
        }
      }

      // Récupérer les index des colonnes
      final nameIndex = headers.indexOf('Nom');
      final priceIndex = headers.indexOf('Prix');
      final descriptionIndex = headers.indexOf('Description');

      // Importer les catégories
      int importedCount = 0;
      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];

        if (row.length < headers.length) {
          continue; // Ignorer les lignes incomplètes
        }

        final category = ProductCategory(
          id: const Uuid().v4(),
          name: row[nameIndex].toString(),
          price: double.tryParse(row[priceIndex].toString()) ?? 0,
          description:
              descriptionIndex >= 0 ? row[descriptionIndex].toString() : null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final success = await _inventoryService.addCategory(category);
        if (success) {
          importedCount++;
        }
      }

      return importedCount;
    } catch (e) {
      debugPrint('Erreur lors de l\'importation des catégories: $e');
      rethrow;
    }
  }

  // Partager un fichier CSV
  Future<void> shareFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)], text: 'Export de données');
    } catch (e) {
      debugPrint('Erreur lors du partage du fichier: $e');
      rethrow;
    }
  }
}
