import 'package:flutter/foundation.dart';
import 'lib/services/ai/invoice_text_analyzer_service.dart';

void main() async {
  // Test de l'analyseur de texte de facture
  await testInvoiceAnalyzer();
}

Future<void> testInvoiceAnalyzer() async {
  // final analyzer = InvoiceTextAnalyzerService(); // Remove this line

  // Exemple de texte de facture
  const String invoiceText = """
  *Nom et Numéro* :
  <PERSON>
  T<PERSON>lé<PERSON> : 06 12 34 56 78
  
  Articles :
  - Produit A x2 - 50€
  - Produit B x1 - 30€
  
  Total : 80€
  Avance : 20€
  Reste à payer : 60€
  
  Adresse de livraison :
  123 Rue de la Paix, 75001 Paris
  
  Conditions spéciales :
  Livraison sous 48h
  """;

  debugPrint('=== Test d\'analyse de facture ===');
  debugPrint('Texte à analyser :');
  debugPrint(invoiceText);
  debugPrint('\n=== Résultats ===');

  // Correction: await the Future before accessing properties
  final Map<String, dynamic> result =
      await InvoiceTextAnalyzerService.analyzeText(invoiceText);

  debugPrint(
    'Nom du client: ${result['analysis']['customerName'] ?? "Non trouvé"}',
  );
  debugPrint(
    'Téléphone: ${result['analysis']['customerPhone'] ?? "Non trouvé"}',
  );
  debugPrint('Articles: ${result['analysis']['items'] ?? "Non trouvé"}');
  if (result['analysis']['items'] != null) {
    for (var item in result['analysis']['items']!) {
      debugPrint(
        '  - ${item['name']} x${item['quantity']} - ${item['price']}€',
      );
    }
  }
  debugPrint('Total: ${result['analysis']['totalAmount'] ?? "Non trouvé"}€');
  debugPrint(
    'Avance: ${result['analysis']['advancePayment'] ?? "Non trouvé"}€',
  );
  debugPrint(
    'Reste à payer: ${result['analysis']['remainingAmount'] ?? "Non trouvé"}€',
  );
  debugPrint(
    'Adresse de livraison: ${result['analysis']['deliveryAddress'] ?? "Non trouvé"}',
  );
  debugPrint(
    'Conditions spéciales: ${result['analysis']['specialConditions'] ?? "Non trouvé"}',
  );

  // Afficher le statut du succès
  debugPrint('Succès de l\'analyse: ${result['success']}');

  // Afficher les erreurs s'il y en a
  if (result.containsKey('errors') &&
      result['errors'] != null &&
      (result['errors'] as List).isNotEmpty) {
    debugPrint('Erreurs d\'analyse:');
    for (var error in result['errors']) {
      debugPrint('  - $error');
    }
  }
}
