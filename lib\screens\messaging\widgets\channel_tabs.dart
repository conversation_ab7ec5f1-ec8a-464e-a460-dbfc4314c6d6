import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../models/messaging/contact.dart';
import '../../../models/messaging/message.dart';
import '../../../services/messaging/messaging_service.dart';
import '../../../theme/neon_theme.dart';

class ChannelTabs extends ConsumerWidget {
  final Contact contact;
  final Function(MessageChannel) onChannelSelected;

  const ChannelTabs({
    super.key,
    required this.contact,
    required this.onChannelSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedChannel = ref.watch(selectedChannelProvider);

    // Filtrer les canaux disponibles pour ce contact
    final availableChannels =
        contact.channels.entries
            .where((entry) => entry.value)
            .map((entry) => entry.key)
            .toList();

    // Si aucun canal n'est disponible, afficher un message
    if (availableChannels.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Aucun canal de communication disponible pour ce contact',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    // Si aucun canal n'est sélectionné, sélectionner le premier disponible
    if (selectedChannel == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        onChannelSelected(availableChannels.first);
      });
    }

    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A2E),
        border: Border(bottom: BorderSide(color: Color(0xFF2A2A3E), width: 1)),
      ),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          // Onglet "Tous les canaux"
          _buildChannelTab(
            context,
            null,
            'Tous',
            NeonTheme.neonCyan,
            FontAwesomeIcons.comments,
            selectedChannel == null,
            ref,
          ),

          // Onglets pour chaque canal disponible
          for (final channel in availableChannels)
            _buildChannelTab(
              context,
              channel,
              _getChannelName(channel),
              _getChannelColor(channel),
              _getChannelIcon(channel),
              selectedChannel == channel,
              ref,
            ),
        ],
      ),
    );
  }

  Widget _buildChannelTab(
    BuildContext context,
    MessageChannel? channel,
    String label,
    Color color,
    IconData icon,
    bool isSelected,
    WidgetRef ref,
  ) {
    // Obtenir le nombre de messages non lus pour ce canal
    int unreadCount = 0;
    if (channel != null) {
      final messages = ref.watch(messagesProvider);
      unreadCount =
          messages
              .where(
                (message) =>
                    message.contactId == contact.id &&
                    message.channel == channel &&
                    !message.isUser &&
                    message.status != MessageStatus.read,
              )
              .length;
    }

    return GestureDetector(
      onTap: () {
        if (channel != null) {
          onChannelSelected(channel);
        } else {
          // Si "Tous les canaux" est sélectionné, définir selectedChannel sur null
          ref.read(selectedChannelProvider.notifier).state = null;
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected ? color : Colors.transparent,
              width: 3,
            ),
          ),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? color : Colors.white.withAlpha(150),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? color : Colors.white.withAlpha(150),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              if (unreadCount > 0)
                Container(
                  margin: const EdgeInsets.only(left: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: NeonTheme.neonPink,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _getChannelName(MessageChannel channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return 'WhatsApp';
      case MessageChannel.facebook:
        return 'Facebook';
      case MessageChannel.sms:
        return 'SMS';
      case MessageChannel.email:
        return 'Email';
      case MessageChannel.app:
        return 'App';
      case MessageChannel.instagram:
        return 'Instagram';
      case MessageChannel.internal:
        return 'Interne';
      case MessageChannel.telegram:
        return 'Telegram';
    }
  }

  IconData _getChannelIcon(MessageChannel channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return FontAwesomeIcons.whatsapp;
      case MessageChannel.facebook:
        return FontAwesomeIcons.facebookMessenger;
      case MessageChannel.sms:
        return FontAwesomeIcons.commentSms;
      case MessageChannel.email:
        return FontAwesomeIcons.envelope;
      case MessageChannel.app:
        return FontAwesomeIcons.message;
      case MessageChannel.instagram:
        return FontAwesomeIcons.instagram;
      case MessageChannel.internal:
        return FontAwesomeIcons.comment;
      case MessageChannel.telegram:
        return FontAwesomeIcons.telegram;
    }
  }

  Color _getChannelColor(MessageChannel channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return Colors.green;
      case MessageChannel.facebook:
        return Colors.blue;
      case MessageChannel.sms:
        return Colors.orange;
      case MessageChannel.email:
        return Colors.red;
      case MessageChannel.app:
        return NeonTheme.neonPurple;
      case MessageChannel.instagram:
        return Colors.purple;
      case MessageChannel.internal:
        return Colors.grey;
      case MessageChannel.telegram:
        return Colors.lightBlue;
    }
  }
}
