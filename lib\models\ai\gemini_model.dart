/// Énumération des modèles Gemini disponibles
enum GeminiModel {
  /// Gemini Pro - Modèle de base pour le texte
  geminiPro,
  
  /// Gemini Pro Vision - Modèle pour le texte et les images
  geminiProVision,
  
  /// Gemini 1.5 Pro - Version améliorée avec contexte étendu
  gemini15Pro,
  
  /// Gemini 1.5 Flash - Version plus rapide et économique
  gemini15Flash,
}

/// Extension pour obtenir la chaîne de caractères correspondant au modèle
extension GeminiModelExtension on GeminiModel {
  /// Obtenir la chaîne de caractères pour l'API
  String get apiString {
    switch (this) {
      case GeminiModel.geminiPro:
        return 'gemini-pro';
      case GeminiModel.geminiProVision:
        return 'gemini-pro-vision';
      case GeminiModel.gemini15Pro:
        return 'gemini-1.5-pro';
      case GeminiModel.gemini15Flash:
        return 'gemini-1.5-flash';
    }
  }
  
  /// Obtenir le nom d'affichage
  String get displayName {
    switch (this) {
      case GeminiModel.geminiPro:
        return 'Gemini Pro';
      case GeminiModel.geminiProVision:
        return 'Gemini Pro Vision';
      case GeminiModel.gemini15Pro:
        return 'Gemini 1.5 Pro';
      case GeminiModel.gemini15Flash:
        return 'Gemini 1.5 Flash';
    }
  }
  
  /// Obtenir la description du modèle
  String get description {
    switch (this) {
      case GeminiModel.geminiPro:
        return 'Modèle polyvalent pour les tâches textuelles.';
      case GeminiModel.geminiProVision:
        return 'Modèle multimodal pour le texte et les images.';
      case GeminiModel.gemini15Pro:
        return 'Version améliorée avec contexte étendu et meilleures performances.';
      case GeminiModel.gemini15Flash:
        return 'Version plus rapide et économique de Gemini 1.5.';
    }
  }
}

/// Obtenir le modèle Gemini à partir d'une chaîne de caractères
GeminiModel geminiModelFromString(String modelString) {
  switch (modelString) {
    case 'gemini-pro':
      return GeminiModel.geminiPro;
    case 'gemini-pro-vision':
      return GeminiModel.geminiProVision;
    case 'gemini-1.5-pro':
      return GeminiModel.gemini15Pro;
    case 'gemini-1.5-flash':
      return GeminiModel.gemini15Flash;
    default:
      return GeminiModel.geminiPro; // Valeur par défaut
  }
}
