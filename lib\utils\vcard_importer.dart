import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import '../models/customer/customer.dart';

class VCardImporter {
  /// Importe des contacts à partir d'un fichier vCard
  static Future<List<Customer>> importFromFile() async {
    try {
      // Sélectionner un fichier vCard
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['vcf'],
      );

      if (result == null || result.files.isEmpty) {
        return [];
      }

      // Lire le contenu du fichier
      final file = result.files.first;
      String content;
      
      if (file.bytes != null) {
        // Web ou fichier en mémoire
        content = String.fromCharCodes(file.bytes!);
      } else if (file.path != null) {
        // Fichier sur le disque
        content = await File(file.path!).readAsString();
      } else {
        return [];
      }

      // Parser le contenu vCard
      return _parseVCardContent(content);
    } catch (e) {
      debugPrint('Erreur lors de l\'importation du fichier vCard: $e');
      return [];
    }
  }

  /// Parse le contenu d'un fichier vCard
  static List<Customer> _parseVCardContent(String content) {
    final List<Customer> customers = [];
    
    // Diviser le contenu en cartes individuelles
    final cards = content.split('BEGIN:VCARD');
    
    for (var card in cards) {
      if (card.trim().isEmpty) continue;
      
      // S'assurer que la carte est complète
      if (!card.contains('END:VCARD')) continue;
      
      // Extraire les informations de la carte
      final name = _extractValue(card, 'FN:');
      final phone = _extractValue(card, 'TEL');
      final email = _extractValue(card, 'EMAIL');
      final address = _extractValue(card, 'ADR');
      final company = _extractValue(card, 'ORG');
      
      // Créer un client à partir des informations extraites
      if (name.isNotEmpty) {
        customers.add(Customer(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          phone: phone.isNotEmpty ? phone : null,
          email: email.isNotEmpty ? email : null,
          address: address.isNotEmpty ? address : null,
          company: company.isNotEmpty ? company : null,
          createdAt: DateTime.now(),
          lastContact: DateTime.now(),
          status: CustomerStatus.lead,
          tags: ['importé', 'vcard'],
        ));
      }
    }
    
    return customers;
  }

  /// Extrait une valeur d'un champ vCard
  static String _extractValue(String card, String field) {
    try {
      // Rechercher le champ dans la carte
      final regex = RegExp('$field[^\\n]*');
      final match = regex.firstMatch(card);
      
      if (match == null) return '';
      
      // Extraire la valeur
      var value = match.group(0) ?? '';
      
      // Supprimer le nom du champ
      if (value.contains(':')) {
        value = value.split(':')[1];
      } else if (value.contains(';')) {
        value = value.split(';').last;
      }
      
      // Nettoyer la valeur
      return value.trim();
    } catch (e) {
      debugPrint('Erreur lors de l\'extraction du champ $field: $e');
      return '';
    }
  }
}
