import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/task/task.dart';
import '../../services/task/task_service.dart';
import '../../providers/theme_provider.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import 'widgets/task_list_view.dart';
import 'widgets/task_calendar_view.dart';

// Provider pour le mode d'affichage des tâches
final taskViewModeProvider = StateProvider<TaskViewMode>(
  (ref) => TaskViewMode.list,
);

// Provider pour le filtre de statut des tâches
final taskStatusFilterProvider = StateProvider<TaskStatus?>((ref) => null);

// Provider pour le filtre de priorité des tâches
final taskPriorityFilterProvider = StateProvider<TaskPriority?>((ref) => null);

// Provider pour le filtre d'assignation des tâches
final taskAssigneeFilterProvider = StateProvider<String?>((ref) => null);

// Enum pour le mode d'affichage des tâches
enum TaskViewMode { list, calendar }

class TasksScreen extends ConsumerStatefulWidget {
  const TasksScreen({super.key});

  @override
  ConsumerState<TasksScreen> createState() => _TasksScreenState();
}

class _TasksScreenState extends ConsumerState<TasksScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Charger les tâches
      await ref.read(tasksProvider.notifier).loadTasks();

      // Charger les membres de l'équipe
      await ref.read(teamMembersProvider.notifier).loadTeamMembers();

      // Initialiser le service de notifications
      // Commenté car NotificationService n'a pas de provider encore
      // final notificationService = ref.read(notificationServiceProvider);
      // await notificationService.initialize();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final viewMode = ref.watch(taskViewModeProvider);
    final statusFilter = ref.watch(taskStatusFilterProvider);
    // Ces filtres sont utilisés dans _buildTaskView
    ref.watch(taskPriorityFilterProvider);
    ref.watch(taskAssigneeFilterProvider);
    final appTheme = ref.watch(themeProvider);

    final allTasks = ref.watch(tasksProvider);
    final overdueTasksCount = ref.watch(overdueTasksProvider).length;
    final todayTasksCount = ref.watch(todayTasksProvider).length;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des tâches'),
        backgroundColor: appTheme.primaryColor,
        foregroundColor: ref.watch(primaryTextColorProvider),
        actions: [
          // Bouton pour basculer entre les vues liste et calendrier
          IconButton(
            icon: Icon(
              viewMode == TaskViewMode.list
                  ? FontAwesomeIcons.calendarDays
                  : FontAwesomeIcons.list,
            ),
            onPressed: () {
              ref.read(taskViewModeProvider.notifier).state =
                  viewMode == TaskViewMode.list
                      ? TaskViewMode.calendar
                      : TaskViewMode.list;
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: appTheme.primaryColor,
          tabs: [
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Toutes'),
                  if (allTasks.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: appTheme.primaryColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${allTasks.length}',
                        style: TextStyle(
                          color: ref.watch(primaryTextColorProvider),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('En retard'),
                  if (overdueTasksCount > 0)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: NeonTheme.neonRed,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '$overdueTasksCount',
                        style: TextStyle(
                          color: ref.watch(primaryTextColorProvider),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Tab(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Aujourd\'hui'),
                  if (todayTasksCount > 0)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: NeonTheme.neonOrange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '$todayTasksCount',
                        style: TextStyle(
                          color: ref.watch(primaryTextColorProvider),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const Tab(text: 'Terminées'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Container(
                decoration: BoxDecoration(gradient: appTheme.sidebarGradient),
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Toutes les tâches
                    _buildTaskView(
                      ref.watch(filteredTasksProvider(statusFilter)),
                      viewMode,
                    ),

                    // Tâches en retard
                    _buildTaskView(ref.watch(overdueTasksProvider), viewMode),

                    // Tâches d'aujourd'hui
                    _buildTaskView(ref.watch(todayTasksProvider), viewMode),

                    // Tâches terminées
                    _buildTaskView(
                      ref.watch(filteredTasksProvider(TaskStatus.completed)),
                      viewMode,
                    ),
                  ],
                ),
              ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: appTheme.primaryColor,
        onPressed: () {
          context.push('/tasks/add');
        },
        child: Icon(Icons.add, color: ref.watch(primaryTextColorProvider)),
      ),
    );
  }

  Widget _buildTaskView(List<Task> tasks, TaskViewMode viewMode) {
    final priorityFilter = ref.watch(taskPriorityFilterProvider);
    final assigneeFilter = ref.watch(taskAssigneeFilterProvider);

    // Appliquer les filtres supplémentaires
    var filteredTasks = tasks;

    if (priorityFilter != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.priority == priorityFilter)
              .toList();
    }

    if (assigneeFilter != null) {
      filteredTasks =
          filteredTasks
              .where((task) => task.assignedToId == assigneeFilter)
              .toList();
    }

    // Trier les tâches par date d'échéance
    filteredTasks.sort((a, b) => a.dueDate.compareTo(b.dueDate));

    return viewMode == TaskViewMode.list
        ? TaskListView(tasks: filteredTasks)
        : TaskCalendarView(tasks: filteredTasks);
  }

  void _showFilterDialog(BuildContext context) {
    final appTheme = ref.watch(themeProvider);
    final statusFilter = ref.read(taskStatusFilterProvider);
    final priorityFilter = ref.read(taskPriorityFilterProvider);
    final assigneeFilter = ref.read(taskAssigneeFilterProvider);
    final teamMembers = ref.read(teamMembersProvider);

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Theme.of(context).colorScheme.surface,
              title: Text(
                'Filtrer les tâches',
                style: TextStyle(color: ref.watch(primaryTextColorProvider)),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Statut',
                      style: TextStyle(
                        color: ref.watch(primaryTextColorProvider),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildFilterChip(
                          label: 'Toutes',
                          selected: statusFilter == null,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'À faire',
                          selected: statusFilter == TaskStatus.todo,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = TaskStatus.todo;
                            } else {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'En cours',
                          selected: statusFilter == TaskStatus.inProgress,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = TaskStatus.inProgress;
                            } else {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'Terminées',
                          selected: statusFilter == TaskStatus.completed,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = TaskStatus.completed;
                            } else {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'Annulées',
                          selected: statusFilter == TaskStatus.cancelled,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = TaskStatus.cancelled;
                            } else {
                              ref
                                  .read(taskStatusFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Priorité',
                      style: TextStyle(
                        color: ref.watch(primaryTextColorProvider),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildFilterChip(
                          label: 'Toutes',
                          selected: priorityFilter == null,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'Haute',
                          selected: priorityFilter == TaskPriority.high,
                          color: NeonTheme.neonRed,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = TaskPriority.high;
                            } else {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'Moyenne',
                          selected: priorityFilter == TaskPriority.medium,
                          color: NeonTheme.neonOrange,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = TaskPriority.medium;
                            } else {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        _buildFilterChip(
                          label: 'Basse',
                          selected: priorityFilter == TaskPriority.low,
                          color: NeonTheme.neonGreen,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = TaskPriority.low;
                            } else {
                              ref
                                  .read(taskPriorityFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Assigné à',
                      style: TextStyle(
                        color: ref.watch(primaryTextColorProvider),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildFilterChip(
                          label: 'Tous',
                          selected: assigneeFilter == null,
                          onSelected: (selected) {
                            if (selected) {
                              ref
                                  .read(taskAssigneeFilterProvider.notifier)
                                  .state = null;
                            }
                          },
                        ),
                        ...teamMembers.map((member) {
                          return _buildFilterChip(
                            label: member.name,
                            selected: assigneeFilter == member.id,
                            onSelected: (selected) {
                              if (selected) {
                                ref
                                    .read(taskAssigneeFilterProvider.notifier)
                                    .state = member.id;
                              } else {
                                ref
                                    .read(taskAssigneeFilterProvider.notifier)
                                    .state = null;
                              }
                            },
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // Réinitialiser tous les filtres
                    ref.read(taskStatusFilterProvider.notifier).state = null;
                    ref.read(taskPriorityFilterProvider.notifier).state = null;
                    ref.read(taskAssigneeFilterProvider.notifier).state = null;
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    'Réinitialiser',
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                    ),
                  ),
                ),
                NeonButton(
                  text: 'Appliquer',
                  color: appTheme.primaryColor,
                  small: true,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool selected,
    required Function(bool) onSelected,
    Color? color,
  }) {
    final appTheme = ref.watch(themeProvider);
    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: onSelected,
      backgroundColor: Theme.of(
        context,
      ).colorScheme.surface.withValues(alpha: 77),
      selectedColor: color ?? appTheme.primaryColor.withValues(alpha: 77),
      checkmarkColor: color ?? appTheme.primaryColor,
      labelStyle: TextStyle(
        color:
            selected
                ? (color ?? appTheme.primaryColor)
                : ref.watch(primaryTextColorProvider),
      ),
      side: BorderSide(
        color:
            selected
                ? (color ?? appTheme.primaryColor)
                : ref.watch(primaryTextColorProvider).withValues(alpha: 77),
      ),
    );
  }
}
