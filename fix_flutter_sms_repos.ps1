Write-Host "Fixing flutter_sms repositories..."

$gradleFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\build.gradle"

if (-not (Test-Path $gradleFile)) {
    Write-Host "File not found: $gradleFile"
    exit 1
}

Write-Host "Original build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Updating repositories in build.gradle..."

$content = Get-Content $gradleFile -Raw
$updatedContent = $content -replace "jcenter\(\)", "mavenCentral()"

$updatedContent | Set-Content $gradleFile

Write-Host ""
Write-Host "Modified build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Fix completed!"
