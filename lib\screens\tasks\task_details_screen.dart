import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import '../../models/task/task.dart';
import '../../services/task/task_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class TaskDetailsScreen extends ConsumerStatefulWidget {
  final String taskId;

  const TaskDetailsScreen({super.key, required this.taskId});

  @override
  ConsumerState<TaskDetailsScreen> createState() => _TaskDetailsScreenState();
}

class _TaskDetailsScreenState extends ConsumerState<TaskDetailsScreen> {
  final TextEditingController _commentController = TextEditingController();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tasks = ref.watch(tasksProvider);
    final task = tasks.firstWhere(
      (task) => task.id == widget.taskId,
      orElse:
          () => Task(
            id: '',
            title: 'Tâche introuvable',
            description: '',
            createdAt: DateTime.now(),
            dueDate: DateTime.now(),
            priority: TaskPriority.medium,
            status: TaskStatus.todo,
            tags: [],
            hasReminder: false,
            isRecurring: false,
          ),
    );

    // Si la tâche n'existe pas, rediriger vers la liste des tâches
    if (task.id.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.go('/tasks');
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // Trouver les sous-tâches
    final subtasks =
        task.subtaskIds != null
            ? tasks.where((t) => task.subtaskIds!.contains(t.id)).toList()
            : <Task>[];

    // Trouver la tâche parente
    final parentTask =
        task.parentTaskId != null
            ? tasks.firstWhere(
              (t) => t.id == task.parentTaskId,
              orElse:
                  () => Task(
                    id: '',
                    title: '',
                    description: '',
                    createdAt: DateTime.now(),
                    dueDate: DateTime.now(),
                    priority: TaskPriority.medium,
                    status: TaskStatus.todo,
                    tags: [],
                    hasReminder: false,
                    isRecurring: false,
                  ),
            )
            : null;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails de la tâche'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.push('/tasks/edit/${task.id}');
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              _showDeleteConfirmation(context, task);
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête de la tâche
              _buildTaskHeader(task),
              const SizedBox(height: 24),

              // Description
              const Text(
                'Description',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  task.description,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              const SizedBox(height: 24),

              // Informations supplémentaires
              const Text(
                'Informations',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildInfoRow(
                      'Créée le',
                      DateFormat('dd/MM/yyyy à HH:mm').format(task.createdAt),
                      Icons.calendar_today,
                    ),
                    if (task.updatedAt != null)
                      _buildInfoRow(
                        'Mise à jour le',
                        DateFormat(
                          'dd/MM/yyyy à HH:mm',
                        ).format(task.updatedAt!),
                        Icons.update,
                      ),
                    _buildInfoRow(
                      'Échéance',
                      DateFormat('dd/MM/yyyy à HH:mm').format(task.dueDate),
                      Icons.event,
                      task.isOverdue ? Colors.red : null,
                    ),
                    _buildInfoRow(
                      'Priorité',
                      _getPriorityText(task.priority),
                      Icons.flag,
                      _getPriorityColor(task.priority),
                    ),
                    _buildInfoRow(
                      'Statut',
                      _getStatusText(task.status),
                      Icons.check_circle,
                      _getStatusColor(task.status),
                    ),
                    if (task.assignedToName != null)
                      _buildInfoRow(
                        'Assignée à',
                        task.assignedToName!,
                        Icons.person,
                      ),
                    if (task.hasReminder && task.reminderTime != null)
                      _buildInfoRow(
                        'Rappel',
                        DateFormat(
                          'dd/MM/yyyy à HH:mm',
                        ).format(task.reminderTime!),
                        Icons.notifications,
                      ),
                    if (task.isRecurring && task.recurrencePattern != null)
                      _buildInfoRow(
                        'Récurrence',
                        _getRecurrenceText(task.recurrencePattern!),
                        Icons.repeat,
                      ),
                    if (parentTask != null && parentTask.id.isNotEmpty)
                      _buildInfoRow(
                        'Tâche parente',
                        parentTask.title,
                        Icons.account_tree,
                        null,
                        () {
                          context.push('/tasks/details/${parentTask.id}');
                        },
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Tags
              if (task.tags.isNotEmpty) ...[
                const Text(
                  'Tags',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      task.tags.map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(
                              alpha: 77,
                            ), // 0.3 * 255 ≈ 77
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: NeonTheme.neonPurple.withValues(
                                alpha: 128,
                              ), // 0.5 * 255 ≈ 128
                            ),
                          ),
                          child: Text(
                            '#$tag',
                            style: const TextStyle(color: Colors.white),
                          ),
                        );
                      }).toList(),
                ),
                const SizedBox(height: 24),
              ],

              // Sous-tâches
              if (subtasks.isNotEmpty) ...[
                Row(
                  children: [
                    const Text(
                      'Sous-tâches',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.add, color: Colors.white),
                      onPressed: () {
                        context.push('/tasks/create?parentId=${task.id}');
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: subtasks.length,
                  itemBuilder: (context, index) {
                    final subtask = subtasks[index];
                    return _buildSubtaskItem(subtask);
                  },
                ),
                const SizedBox(height: 24),
              ] else ...[
                Row(
                  children: [
                    const Text(
                      'Sous-tâches',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.add, color: Colors.white),
                      onPressed: () {
                        context.push('/tasks/create?parentId=${task.id}');
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Text(
                      'Aucune sous-tâche',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Commentaires
              const Text(
                'Commentaires',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              if (task.comments != null && task.comments!.isNotEmpty)
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: task.comments!.length,
                  itemBuilder: (context, index) {
                    final comment = task.comments![index];
                    return _buildCommentItem(comment);
                  },
                )
              else
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Center(
                    child: Text(
                      'Aucun commentaire',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              const SizedBox(height: 16),

              // Ajouter un commentaire
              Row(
                children: [
                  Expanded(
                    child: NeonTextField(
                      controller: _commentController,
                      labelText: 'Ajouter un commentaire',
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.send, color: NeonTheme.neonPurple),
                    onPressed: () => _addComment(task),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: _getStatusColor(task.status),
        onPressed: () {
          _showStatusChangeDialog(context, task);
        },
        child: const Icon(Icons.edit_attributes, color: Colors.white),
      ),
    );
  }

  Widget _buildTaskHeader(Task task) {
    final Color priorityColor = _getPriorityColor(task.priority);
    final bool isOverdue = task.isOverdue;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isOverdue ? Colors.red : priorityColor,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: _getStatusColor(task.status),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  task.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                color: isOverdue ? Colors.red : Colors.white,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                DateFormat('dd/MM/yyyy à HH:mm').format(task.dueDate),
                style: TextStyle(
                  color: isOverdue ? Colors.red : Colors.white,
                  fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      task.priority == TaskPriority.high
                          ? Icons.arrow_upward
                          : task.priority == TaskPriority.medium
                          ? Icons.remove
                          : Icons.arrow_downward,
                      color: priorityColor,
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getPriorityText(task.priority),
                      style: TextStyle(color: priorityColor),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon, [
    Color? color,
    VoidCallback? onTap,
  ]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            Icon(icon, color: color ?? Colors.white, size: 16),
            const SizedBox(width: 8),
            Text(
              '$label:',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                value,
                style: TextStyle(
                  color: color ?? Colors.white,
                  decoration: onTap != null ? TextDecoration.underline : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubtaskItem(Task subtask) {
    final Color priorityColor = _getPriorityColor(subtask.priority);
    final bool isOverdue = subtask.isOverdue;

    return Card(
      color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isOverdue ? Colors.red : priorityColor,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          context.push('/tasks/details/${subtask.id}');
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: _getStatusColor(subtask.status),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subtask.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        decoration:
                            subtask.status == TaskStatus.completed
                                ? TextDecoration.lineThrough
                                : null,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color: isOverdue ? Colors.red : Colors.white,
                          size: 12,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          DateFormat('dd/MM/yyyy').format(subtask.dueDate),
                          style: TextStyle(
                            color: isOverdue ? Colors.red : Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Colors.white.withValues(alpha: 128), // 0.5 * 255 ≈ 128
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentItem(TaskComment comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: Colors.white, size: 14),
              const SizedBox(width: 4),
              Text(
                comment.authorName,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                DateFormat('dd/MM/yyyy HH:mm').format(comment.createdAt),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(comment.content, style: const TextStyle(color: Colors.white)),
        ],
      ),
    );
  }

  void _addComment(Task task) {
    if (_commentController.text.trim().isEmpty) return;

    final comment = TaskComment(
      id: const Uuid().v4(),
      content: _commentController.text.trim(),
      createdAt: DateTime.now(),
      authorId: 'current_user', // Remplacer par l'ID de l'utilisateur actuel
      authorName:
          'Utilisateur actuel', // Remplacer par le nom de l'utilisateur actuel
    );

    ref.read(tasksProvider.notifier).addCommentToTask(task.id, comment);
    _commentController.clear();
  }

  void _showStatusChangeDialog(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Changer le statut',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatusOption(context, 'À faire', TaskStatus.todo, task),
              _buildStatusOption(
                context,
                'En cours',
                TaskStatus.inProgress,
                task,
              ),
              _buildStatusOption(
                context,
                'Terminée',
                TaskStatus.completed,
                task,
              ),
              _buildStatusOption(
                context,
                'Annulée',
                TaskStatus.cancelled,
                task,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusOption(
    BuildContext context,
    String label,
    TaskStatus status,
    Task task,
  ) {
    return ListTile(
      leading: Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          color: _getStatusColor(status),
          shape: BoxShape.circle,
        ),
      ),
      title: Text(label, style: const TextStyle(color: Colors.white)),
      selected: task.status == status,
      selectedTileColor: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
      onTap: () {
        ref.read(tasksProvider.notifier).updateTaskStatus(task.id, status);
        Navigator.of(context).pop();
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Supprimer la tâche',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la tâche "${task.title}" ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            NeonButton(
              text: 'Supprimer',
              color: Colors.red,
              small: true,
              onPressed: () {
                ref.read(tasksProvider.notifier).deleteTask(task.id);
                Navigator.of(context).pop();
                context.go('/tasks');
              },
            ),
          ],
        );
      },
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.low:
        return Colors.green;
    }
  }

  String _getPriorityText(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return 'Haute';
      case TaskPriority.medium:
        return 'Moyenne';
      case TaskPriority.low:
        return 'Basse';
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return 'À faire';
      case TaskStatus.inProgress:
        return 'En cours';
      case TaskStatus.completed:
        return 'Terminée';
      case TaskStatus.cancelled:
        return 'Annulée';
    }
  }

  String _getRecurrenceText(String pattern) {
    switch (pattern) {
      case 'daily':
        return 'Quotidienne';
      case 'weekly':
        return 'Hebdomadaire';
      case 'biweekly':
        return 'Bi-hebdomadaire';
      case 'monthly':
        return 'Mensuelle';
      case 'quarterly':
        return 'Trimestrielle';
      case 'yearly':
        return 'Annuelle';
      default:
        return pattern;
    }
  }
}
