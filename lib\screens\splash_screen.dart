import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';
import '../theme/neon_theme.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  Future<void> _checkAuth() async {
    // Simuler un délai pour afficher l'écran de démarrage
    await Future.delayed(const Duration(seconds: 2));

    // Vérifier si le widget est toujours monté avant d'utiliser le contexte
    if (!mounted) return;

    final isLoggedIn = ref.read(authServiceProvider).isLoggedIn;
    if (isLoggedIn) {
      context.go('/dashboard');
    } else {
      context.go('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.mainGradient),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: NeonTheme.primaryAccent.withAlpha(
                        204,
                      ), // 0.8 * 255 ≈ 204
                      blurRadius: 12.0,
                    ),
                    BoxShadow(
                      color: NeonTheme.primaryAccent.withAlpha(
                        128,
                      ), // 0.5 * 255 ≈ 128
                      blurRadius: 24.0,
                    ),
                  ],
                ),
                child: Image.asset(
                  'assets/images/logo/logo.png',
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 24),

              // Titre
              Text(
                'HCP-DESIGN CRM',
                style: NeonTheme.neonTextStyle(
                  NeonTheme.primaryAccent,
                ).copyWith(fontSize: 32, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),

              // Sous-titre
              Text(
                'Votre solution de gestion de la relation client',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 204), // 0.8 * 255 ≈ 204
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),

              // Indicateur de chargement
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  NeonTheme.primaryAccent,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
