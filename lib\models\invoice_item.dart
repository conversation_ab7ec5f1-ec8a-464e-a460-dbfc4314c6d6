import 'dart:convert';

class InvoiceItem {
  final String id;
  final String productId;
  final String productName;
  final String categoryId;
  final String categoryName;
  final double price;
  final int quantity;

  InvoiceItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.categoryId,
    required this.categoryName,
    required this.price,
    required this.quantity,
  });

  // Calculer le total pour cet élément
  double get total => price * quantity;

  InvoiceItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? categoryId,
    String? categoryName,
    double? price,
    int? quantity,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'price': price,
      'quantity': quantity,
    };
  }

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      categoryId: map['categoryId'] ?? '',
      categoryName: map['categoryName'] ?? '',
      price: map['price']?.toDouble() ?? 0.0,
      quantity: map['quantity']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory InvoiceItem.fromJson(String source) => InvoiceItem.fromMap(json.decode(source));

  @override
  String toString() {
    return 'InvoiceItem(id: $id, productId: $productId, productName: $productName, categoryId: $categoryId, categoryName: $categoryName, price: $price, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is InvoiceItem &&
      other.id == id &&
      other.productId == productId &&
      other.productName == productName &&
      other.categoryId == categoryId &&
      other.categoryName == categoryName &&
      other.price == price &&
      other.quantity == quantity;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      productId.hashCode ^
      productName.hashCode ^
      categoryId.hashCode ^
      categoryName.hashCode ^
      price.hashCode ^
      quantity.hashCode;
  }
}
