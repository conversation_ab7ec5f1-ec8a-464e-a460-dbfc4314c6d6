import 'package:flutter/material.dart';
import 'invoice_text_analyzer_service.dart';

/// Exemple d'utilisation du service d'analyse de texte basé sur des règles
class TextAnalysisExample {
  static Future<void> testAnalysis() async {
    // final analyzer = InvoiceTextAnalyzerService(); // Remove this line

    // Exemple de texte de commande structuré
    const String sampleText = '''
Client: <PERSON>
Téléphone: +225 07 08 09 10 11
Article: Ordinateur portable
2 x Souris sans fil 15000 FCFA
1 x Clavier mécanique 25000 FCFA
Total: 55000 FCFA
Avance: 20000 FCFA
Reste: 35000 FCFA
Livraison: Abidjan, Cocody Angré
Message: Livraison urgente avant vendredi
''';

    debugPrint('=== Test d\'analyse de texte ===');
    debugPrint('Texte à analyser:');
    debugPrint(sampleText);

    try {
      final result = await InvoiceTextAnalyzerService.analyzeText(sampleText);

      debugPrint('\n=== Résultats de l\'analyse ===');
      debugPrint('Nom du client: ${result['analysis']['customerName']}');
      debugPrint('Téléphone: ${result['analysis']['customerPhone']}');
      debugPrint('Articles: ${result['analysis']['items']}');
      debugPrint('Montant total: ${result['analysis']['totalAmount']} FCFA');
      debugPrint('Avance: ${result['analysis']['advancePayment']} FCFA');
      debugPrint(
        'Reste à payer: ${result['analysis']['remainingAmount']} FCFA',
      );
      debugPrint(
        'Adresse de livraison: ${result['analysis']['deliveryAddress']}',
      );
      debugPrint(
        'Conditions spéciales: ${result['analysis']['specialConditions']}',
      );
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse: $e');
    }
  }

  /// Formats de texte supportés par l'analyseur
  static const List<String> supportedFormats = [
    'Client: [Nom du client]',
    'Téléphone: [Numéro]',
    'Article: [Nom de l\'article]',
    '[Quantité] x [Nom] [Prix] FCFA',
    'Total: [Montant] FCFA',
    'Avance: [Montant] FCFA',
    'Reste: [Montant] FCFA',
    'Livraison: [Adresse]',
    'Message: [Conditions spéciales]',
  ];

  /// Exemples de textes valides
  static const List<String> validExamples = [
    '''
Client: Marie Martin
Téléphone: 01 23 45 67 89
3 x Produit A 5000 FCFA
Total: 15000 FCFA
Livraison: Paris 15ème
''',
    '''
Nom: Pierre Durand
Phone: +33 6 12 34 56 78
Article: Service de consultation
Montant total: 50000 FCFA
Avance: 10000 FCFA
Adresse: Lyon, Bellecour
''',
  ];
}
