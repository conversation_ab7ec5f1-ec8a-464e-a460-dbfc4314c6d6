@echo off
echo Fixing flutter_sms build.gradle file...

set GRADLE_FILE=%LOCALAPPDATA%\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\build.gradle

if not exist "%GRADLE_FILE%" (
    echo File not found: %GRADLE_FILE%
    exit /b 1
)

echo Original build.gradle content:
type "%GRADLE_FILE%"

echo.
echo Adding namespace to build.gradle...

powershell -Command "(Get-Content '%GRADLE_FILE%') -replace 'apply plugin: \"com.android.library\"', 'apply plugin: \"com.android.library\"\n\nandroid {\n    namespace \"com.babariviere.sms\"\n}' | Set-Content '%GRADLE_FILE%'"

echo.
echo Modified build.gradle content:
type "%GRADLE_FILE%"

echo.
echo Fix completed!
