import 'dart:convert';

class TelegramConfig {
  final String apiToken;
  final int lastUpdateId;
  final bool enabled;
  final String? webhookUrl;
  final String? botUsername;
  final String? botName;

  TelegramConfig({
    required this.apiToken,
    required this.lastUpdateId,
    required this.enabled,
    this.webhookUrl,
    this.botUsername,
    this.botName,
  });

  // Créer une configuration vide
  factory TelegramConfig.empty() {
    return TelegramConfig(apiToken: '', lastUpdateId: 0, enabled: false);
  }

  // Créer une configuration à partir d'un Map
  factory TelegramConfig.fromMap(Map<String, dynamic> map) {
    return TelegramConfig(
      apiToken: map['apiToken'] ?? '',
      lastUpdateId: map['lastUpdateId'] ?? 0,
      enabled: map['enabled'] ?? false,
      webhookUrl: map['webhookUrl'],
      botUsername: map['botUsername'],
      botName: map['botName'],
    );
  }

  // Créer une configuration à partir d'un JSON
  factory TelegramConfig.fromJson(String source) =>
      TelegramConfig.fromMap(json.decode(source));

  // Convertir la configuration en Map
  Map<String, dynamic> toMap() {
    return {
      'apiToken': apiToken,
      'lastUpdateId': lastUpdateId,
      'enabled': enabled,
      if (webhookUrl != null) 'webhookUrl': webhookUrl,
      if (botUsername != null) 'botUsername': botUsername,
      if (botName != null) 'botName': botName,
    };
  }

  // Convertir la configuration en JSON
  String toJson() => json.encode(toMap());

  // Créer une copie de la configuration avec des modifications
  TelegramConfig copyWith({
    String? apiToken,
    int? lastUpdateId,
    bool? enabled,
    String? webhookUrl,
    String? botUsername,
    String? botName,
  }) {
    return TelegramConfig(
      apiToken: apiToken ?? this.apiToken,
      lastUpdateId: lastUpdateId ?? this.lastUpdateId,
      enabled: enabled ?? this.enabled,
      webhookUrl: webhookUrl ?? this.webhookUrl,
      botUsername: botUsername ?? this.botUsername,
      botName: botName ?? this.botName,
    );
  }

  @override
  String toString() {
    return 'TelegramConfig(apiToken: ${apiToken.isNotEmpty ? '***' : 'non défini'}, lastUpdateId: $lastUpdateId, enabled: $enabled)';
  }
}
