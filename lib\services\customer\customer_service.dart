import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/customer/customer.dart';

// Provider pour le service client
final customerServiceProvider = Provider<CustomerService>((ref) {
  return CustomerService();
});

// Provider pour la liste des clients
final customersProvider = FutureProvider<List<Customer>>((ref) async {
  final service = ref.watch(customerServiceProvider);
  return service.getCustomers();
});

// Provider pour les leads (prospects)
final leadsProvider = FutureProvider<List<Customer>>((ref) async {
  final service = ref.watch(customerServiceProvider);
  return service.getLeads();
});

// Provider pour les clients VIP
final vipCustomersProvider = FutureProvider<List<Customer>>((ref) async {
  final service = ref.watch(customerServiceProvider);
  return service.getVipCustomers();
});

class CustomerService {
  // Liste en mémoire des clients (simulant une base de données)
  final List<Customer> _customers = [];

  CustomerService() {
    // Ajouter quelques clients de test
    _addTestCustomers();
  }

  // Créer une instance vide pour l'initialisation
  static CustomerService empty() {
    return CustomerService();
  }

  // Méthode pour récupérer tous les clients
  Future<List<Customer>> getCustomers() async {
    // Simuler un délai de chargement
    await Future.delayed(const Duration(milliseconds: 800));

    // Retourner une copie de la liste pour éviter les modifications directes
    return [..._customers];
  }

  // Méthode pour récupérer un client par son ID
  Future<Customer?> getCustomerById(String id) async {
    try {
      return _customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour récupérer un client par son ID de contact
  Future<Customer?> getCustomerByContactId(String contactId) async {
    try {
      return _customers.firstWhere(
        (customer) => customer.contactId == contactId,
      );
    } catch (e) {
      return null;
    }
  }

  // Méthode pour récupérer un client par son email
  Future<Customer?> getCustomerByEmail(String email) async {
    try {
      return _customers.firstWhere((customer) => customer.email == email);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour récupérer un client par son téléphone
  Future<Customer?> getCustomerByPhone(String phone) async {
    try {
      return _customers.firstWhere((customer) => customer.phone == phone);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour ajouter un nouveau client
  Future<void> addCustomer(Customer customer) async {
    // Vérifier si l'ID existe déjà
    if (_customers.any((c) => c.id == customer.id)) {
      throw Exception('Un client avec cet ID existe déjà');
    }

    _customers.add(customer);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour mettre à jour un client existant
  Future<void> updateCustomer(Customer customer) async {
    final index = _customers.indexWhere((c) => c.id == customer.id);
    if (index == -1) {
      throw Exception('Client non trouvé');
    }

    _customers[index] = customer;
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour supprimer un client
  Future<void> deleteCustomer(String id) async {
    final index = _customers.indexWhere((c) => c.id == id);
    if (index == -1) {
      throw Exception('Client non trouvé');
    }

    _customers.removeAt(index);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour rechercher des clients
  Future<List<Customer>> searchCustomers(String query) async {
    final lowercaseQuery = query.toLowerCase();

    return _customers.where((customer) {
      return customer.name.toLowerCase().contains(lowercaseQuery) ||
          (customer.email?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          (customer.phone?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          (customer.notes?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          customer.tags.any(
            (tag) => tag.toLowerCase().contains(lowercaseQuery),
          );
    }).toList();
  }

  // Méthode pour obtenir les leads (prospects)
  Future<List<Customer>> getLeads() async {
    return _customers
        .where((customer) => customer.status == CustomerStatus.lead)
        .toList();
  }

  // Méthode pour obtenir les clients VIP
  Future<List<Customer>> getVipCustomers() async {
    return _customers
        .where((customer) => customer.status == CustomerStatus.vip)
        .toList();
  }

  // Méthode pour obtenir les clients inactifs
  Future<List<Customer>> getInactiveCustomers() async {
    return _customers
        .where((customer) => customer.status == CustomerStatus.inactive)
        .toList();
  }

  // Méthode pour obtenir les clients assignés à un commercial
  Future<List<Customer>> getCustomersByAssignedTo(String assignedTo) async {
    return _customers
        .where((customer) => customer.assignedTo == assignedTo)
        .toList();
  }

  // Méthode pour qualifier un lead
  Future<void> qualifyLead(String id, LeadQualification qualification) async {
    final customer = await getCustomerById(id);
    if (customer == null) {
      throw Exception('Client non trouvé');
    }

    final updatedCustomer = customer.copyWith(leadQualification: qualification);

    await updateCustomer(updatedCustomer);
  }

  // Méthode pour convertir un lead en client
  Future<void> convertLeadToCustomer(String id) async {
    final customer = await getCustomerById(id);
    if (customer == null) {
      throw Exception('Client non trouvé');
    }

    final updatedCustomer = customer.copyWith(status: CustomerStatus.customer);

    await updateCustomer(updatedCustomer);
  }

  // Méthode pour assigner un client à un commercial
  Future<void> assignCustomer(String id, String assignedTo) async {
    final customer = await getCustomerById(id);
    if (customer == null) {
      throw Exception('Client non trouvé');
    }

    final updatedCustomer = customer.copyWith(assignedTo: assignedTo);

    await updateCustomer(updatedCustomer);
  }

  // Ajouter des clients de test
  void _addTestCustomers() {
    _customers.addAll([
      Customer(
        id: '1',
        name: 'Jean Dupont',
        contactId: '+221771234567',
        phone: '+221771234567',
        email: '<EMAIL>',
        source: 'WhatsApp',
        createdAt: DateTime(2023, 5, 15),
        lastContact: DateTime(2023, 6, 20),
        status: CustomerStatus.customer,
        leadQualification: LeadQualification.qualified,
        assignedTo: 'commercial1',
        notes: 'Client régulier, préfère les coques personnalisées',
        tags: ['fidèle', 'personnalisation'],
        preferences: {
          'preferred_products': ['coque iphone', 'protection écran'],
          'favorite_colors': ['noir', 'bleu'],
        },
      ),
      Customer(
        id: '2',
        name: 'Marie Martin',
        contactId: '+221772345678',
        phone: '+221772345678',
        email: '<EMAIL>',
        source: 'Facebook',
        createdAt: DateTime(2023, 7, 10),
        lastContact: DateTime(2023, 7, 15),
        status: CustomerStatus.lead,
        leadQualification: LeadQualification.warm,
        notes: 'Intéressée par les coques pour Samsung',
        tags: ['prospect', 'samsung'],
      ),
      Customer(
        id: '3',
        name: 'Ahmed Diallo',
        contactId: '+221773456789',
        phone: '+221773456789',
        email: '<EMAIL>',
        source: 'Site web',
        createdAt: DateTime(2023, 4, 5),
        lastContact: DateTime(2023, 8, 12),
        status: CustomerStatus.vip,
        assignedTo: 'commercial2',
        notes: 'Client VIP, commande régulièrement en grande quantité',
        tags: ['vip', 'grossiste'],
        preferences: {
          'preferred_products': ['coque samsung', 'coque huawei'],
          'payment_method': 'Orange Money',
        },
      ),
    ]);
  }
}
