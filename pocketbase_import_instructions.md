# Instructions pour importer les collections PocketBase

Ce document explique comment importer le fichier de définition des collections `pocketbase_collections.json` dans votre instance PocketBase.

## Prérequis

1. Téléchargez et installez PocketBase depuis le site officiel : [https://pocketbase.io/docs/](https://pocketbase.io/docs/)
2. Extrayez le fichier téléchargé dans un dossier de votre choix (par exemple, `ncrm-backend`)

## Étapes d'importation

### 1. Démarrer PocketBase

Ouvrez un terminal et naviguez vers le dossier où vous avez extrait PocketBase, puis exécutez :

```bash
# Sur Windows
pocketbase.exe serve

# Sur macOS/Linux
./pocketbase serve
```

PocketBase démarrera et sera accessible à l'adresse : http://127.0.0.1:8090

### 2. Créer un compte administrateur

1. Accédez à http://127.0.0.1:8090/_/ pour ouvrir l'interface d'administration
2. Su<PERSON>z les instructions pour créer un compte administrateur
3. Connectez-vous avec vos identifiants

### 3. Importer les collections

1. Dans l'interface d'administration PocketBase, cliquez sur "Settings" dans le menu de gauche
2. Sélectionnez l'onglet "Import Collections"
3. Cliquez sur "Choose file" et sélectionnez le fichier `pocketbase_collections.json`
4. Cliquez sur "Import"
5. Confirmez l'importation lorsque vous êtes invité à le faire

### 4. Vérifier l'importation

Après l'importation, vous devriez voir les collections suivantes dans le menu de gauche :

- users (collection système)
- contacts
- messages
- tasks
- opportunities
- knowledge_base
- settings
- activity_log

## Structure des collections

Le fichier JSON importé crée les collections suivantes avec leurs champs :

### Collection `users` (gérée par PocketBase)
- Champs standard : email, password
- Champs personnalisés : name, avatar, role

### Collection `contacts`
- name (texte, obligatoire)
- phone (texte)
- email (email)
- company (texte)
- avatar (fichier)
- notes (texte)
- address (texte)
- city (texte)
- country (texte)
- postal_code (texte)
- tags (json)
- owner (relation → users)

### Collection `messages`
- contact_id (relation → contacts, obligatoire)
- content (texte, obligatoire)
- is_user (booléen, obligatoire)
- timestamp (date, obligatoire)
- media_url (texte)
- media_type (sélection : image, video, audio, document)
- media_name (texte)
- media_size (nombre)
- channel (sélection : app, whatsapp, facebook, sms, email)
- status (sélection : sent, delivered, read, failed)
- media_file (fichier)

### Collection `tasks`
- title (texte, obligatoire)
- description (texte)
- contact_id (relation → contacts)
- due_date (date, obligatoire)
- status (sélection : pending, in_progress, completed, obligatoire)
- priority (sélection : low, medium, high, obligatoire)
- assigned_to (relation → users)
- completed_at (date)

### Collection `opportunities`
- title (texte, obligatoire)
- contact_id (relation → contacts, obligatoire)
- value (nombre, obligatoire)
- status (sélection : lead, proposal, negotiation, won, lost, obligatoire)
- notes (texte)
- expected_close_date (date)
- owner (relation → users)
- products (json)
- probability (nombre, 0-100)

### Collection `knowledge_base`
- title (texte, obligatoire)
- content (texte, obligatoire)
- category (texte, obligatoire)
- tags (json)
- author (relation → users)
- is_public (booléen)
- attachments (fichiers, multiple)

### Collection `settings`
- key (texte, obligatoire, unique)
- value (json)
- description (texte)
- is_system (booléen)
- user_id (relation → users)

### Collection `activity_log`
- user_id (relation → users)
- action (texte, obligatoire)
- entity_type (texte, obligatoire)
- entity_id (texte)
- details (json)
- ip_address (texte)

## Règles d'accès

Les règles d'accès sont configurées pour permettre :

- Aux utilisateurs authentifiés de lire et écrire dans la plupart des collections
- Aux administrateurs d'avoir un accès complet à toutes les collections
- Aux utilisateurs de modifier uniquement leurs propres données dans certains cas
- Aux utilisateurs non authentifiés d'accéder uniquement aux articles publics de la base de connaissances

## Prochaines étapes

Après avoir importé les collections, vous devrez :

1. Créer quelques utilisateurs de test
2. Ajouter des données de test dans les collections
3. Configurer l'URL de PocketBase dans votre application Flutter (fichier `lib/config/pocketbase_config.dart`)
4. Tester la connexion depuis votre application Flutter

Pour plus d'informations sur l'utilisation de PocketBase avec Flutter, consultez le fichier `pocketbase_setup.md`.
