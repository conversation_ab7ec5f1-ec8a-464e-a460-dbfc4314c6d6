import 'dart:convert';

// Énumération des rôles de message
enum AIMessageRole {
  system, // Message système (instructions)
  user, // Message de l'utilisateur
  assistant, // Message de l'assistant
}

class AIMessage {
  final AIMessageRole role;
  final String content;
  final DateTime timestamp;

  AIMessage({required this.role, required this.content, DateTime? timestamp})
    : timestamp = timestamp ?? DateTime.now();

  AIMessage copyWith({
    AIMessageRole? role,
    String? content,
    DateTime? timestamp,
  }) {
    return AIMessage(
      role: role ?? this.role,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'role': _roleToString(role),
      'content': content,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  factory AIMessage.fromMap(Map<String, dynamic> map) {
    return AIMessage(
      role: _roleFromString(map['role'] ?? 'user'),
      content: map['content'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
    );
  }

  static AIMessageRole _roleFromString(String role) {
    switch (role) {
      case 'system':
        return AIMessageRole.system;
      case 'assistant':
        return AIMessageRole.assistant;
      case 'user':
      default:
        return AIMessageRole.user;
    }
  }

  static String _roleToString(AIMessageRole role) {
    switch (role) {
      case AIMessageRole.system:
        return 'system';
      case AIMessageRole.assistant:
        return 'assistant';
      case AIMessageRole.user:
        return 'user';
    }
  }

  String toJson() => json.encode(toMap());

  factory AIMessage.fromJson(String source) =>
      AIMessage.fromMap(json.decode(source));

  @override
  String toString() =>
      'AIMessage(role: $role, content: ${content.substring(0, content.length > 20 ? 20 : content.length)}..., timestamp: $timestamp)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AIMessage &&
        other.role == role &&
        other.content == content &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => role.hashCode ^ content.hashCode ^ timestamp.hashCode;
}
