import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../models/messaging/message.dart';
import '../../models/customer/customer.dart';
import '../../services/messaging/sms_service.dart';
import '../../services/customer/customer_service.dart';
import '../../theme/neon_theme.dart';
import 'send_sms_screen.dart';

class SmsHistoryScreen extends ConsumerStatefulWidget {
  final String? customerId;

  const SmsHistoryScreen({super.key, this.customerId});

  @override
  ConsumerState<SmsHistoryScreen> createState() => _SmsHistoryScreenState();
}

class _SmsHistoryScreenState extends ConsumerState<SmsHistoryScreen> {
  Customer? _customer;
  List<Message> _messages = [];
  bool _isLoading = true;
  String _filterText = '';
  final TextEditingController _searchController = TextEditingController();
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy HH:mm');

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Charger les messages SMS
      final messagesNotifier = ref.read(smsMessagesProvider.notifier);
      await messagesNotifier.loadMessages();
      final allMessages = ref.read(smsMessagesProvider);

      // Si un ID client est fourni, filtrer les messages pour ce client
      if (widget.customerId != null) {
        // Charger les informations du client
        final customerService = ref.read(customerServiceProvider);
        final customers = await customerService.getCustomers();
        _customer = customers.firstWhere(
          (c) => c.id == widget.customerId,
          orElse:
              () => Customer(
                id: '',
                name: 'Client inconnu',
                createdAt: DateTime.now(),
                lastContact: DateTime.now(),
                status: CustomerStatus.lead,
                tags: [],
              ),
        );

        // Filtrer les messages pour ce client
        _messages =
            allMessages
                .where((message) => message.contactId == widget.customerId)
                .toList();
      } else {
        // Afficher tous les messages
        _messages = allMessages;
      }

      // Trier les messages par date (plus récent en premier)
      _messages.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterMessages(String query) {
    setState(() {
      _filterText = query;
    });
  }

  List<Message> get _filteredMessages {
    if (_filterText.isEmpty) {
      return _messages;
    }

    return _messages.where((message) {
      final contentMatch = message.content.toLowerCase().contains(
        _filterText.toLowerCase(),
      );
      final dateMatch = _dateFormat
          .format(message.timestamp)
          .toLowerCase()
          .contains(_filterText.toLowerCase());
      return contentMatch || dateMatch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _customer != null
              ? 'SMS avec ${_customer!.name}'
              : 'Historique des SMS',
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Column(
          children: [
            // Barre de recherche
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Rechercher dans les messages...',
                  hintStyle: const TextStyle(color: Colors.white70),
                  prefixIcon: const Icon(Icons.search, color: Colors.white70),
                  suffixIcon:
                      _filterText.isNotEmpty
                          ? IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white70,
                            ),
                            onPressed: () {
                              _searchController.clear();
                              _filterMessages('');
                            },
                          )
                          : null,
                  filled: true,
                  fillColor: Colors.black.withAlpha(77),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.cyan),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                onChanged: _filterMessages,
              ),
            ),

            // Liste des messages
            Expanded(
              child:
                  _isLoading
                      ? const Center(
                        child: CircularProgressIndicator(color: Colors.cyan),
                      )
                      : _filteredMessages.isEmpty
                      ? Center(
                        child: Text(
                          _filterText.isNotEmpty
                              ? 'Aucun message ne correspond à votre recherche'
                              : 'Aucun message SMS',
                          style: const TextStyle(color: Colors.white70),
                        ),
                      )
                      : ListView.builder(
                        itemCount: _filteredMessages.length,
                        itemBuilder: (context, index) {
                          final message = _filteredMessages[index];
                          return _buildMessageTile(message);
                        },
                      ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => SendSmsScreen(
                    customerId: _customer?.id,
                    customerName: _customer?.name,
                    initialPhoneNumber: _customer?.phone,
                  ),
            ),
          ).then((_) => _loadData());
        },
        backgroundColor: Colors.cyan,
        child: const Icon(Icons.add, color: Colors.black),
      ),
    );
  }

  Widget _buildMessageTile(Message message) {
    final isOutgoing = message.isUser;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment:
            isOutgoing ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isOutgoing)
            const CircleAvatar(
              backgroundColor: Colors.cyan,
              child: Icon(Icons.person, color: Colors.black, size: 20),
            ),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color:
                    isOutgoing
                        ? Colors.cyan.withValues(alpha: 51) // 0.2 * 255 ≈ 51
                        : Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isOutgoing ? Colors.cyan : Colors.white24,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _dateFormat.format(message.timestamp),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 4),
                      if (isOutgoing)
                        Icon(
                          _getStatusIcon(message.status),
                          color: _getStatusColor(message.status),
                          size: 14,
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isOutgoing)
            const CircleAvatar(
              backgroundColor: Colors.cyan,
              radius: 16,
              child: Icon(Icons.person, color: Colors.black, size: 16),
            ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(MessageStatus status) {
    return switch (status) {
      MessageStatus.sending => Icons.access_time,
      MessageStatus.sent => Icons.check,
      MessageStatus.delivered => Icons.done_all,
      MessageStatus.read => Icons.done_all,
      MessageStatus.failed => Icons.error_outline,
    };
  }

  Color _getStatusColor(MessageStatus status) {
    return switch (status) {
      MessageStatus.sending => Colors.grey,
      MessageStatus.sent => Colors.cyan,
      MessageStatus.delivered => Colors.cyan,
      MessageStatus.read => Colors.green,
      MessageStatus.failed => Colors.red,
    };
  }
}
