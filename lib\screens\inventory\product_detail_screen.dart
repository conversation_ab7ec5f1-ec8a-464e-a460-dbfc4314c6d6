import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

// Provider pour obtenir un produit par ID
final productProvider = FutureProvider.family<Product?, String>((
  ref,
  id,
) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  final products = await inventoryService.getProducts();
  try {
    return products.firstWhere((product) => product.id == id);
  } catch (e) {
    return null;
  }
});

// Provider pour obtenir une catégorie par ID
final categoryProvider = FutureProvider.family<ProductCategory?, String>((
  ref,
  id,
) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  final categories = await inventoryService.getCategories();
  try {
    return categories.firstWhere((category) => category.id == id);
  } catch (e) {
    return null;
  }
});

class ProductDetailScreen extends ConsumerWidget {
  final String productId;

  const ProductDetailScreen({super.key, required this.productId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productAsync = ref.watch(productProvider(productId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détail du Produit'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.push('/inventory/edit/$productId');
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              _shareProductDetails(context, ref);
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: productAsync.when(
          data: (product) {
            if (product == null) {
              return const Center(
                child: Text(
                  'Produit non trouvé',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              );
            }

            return _buildProductDetail(context, ref, product);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
        ),
      ),
    );
  }

  Widget _buildProductDetail(
    BuildContext context,
    WidgetRef ref,
    Product product,
  ) {
    final categoryAsync = ref.watch(categoryProvider(product.categoryId));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NeonCard(
            color: NeonTheme.neonGreen,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image du produit
                    Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: NeonTheme.neonGreen.withValues(
                            alpha: 77,
                          ), // 0.3 * 255 ≈ 77
                          width: 1,
                        ),
                        boxShadow: NeonTheme.neonShadow(
                          NeonTheme.neonGreen,
                          intensity: 0.5,
                        ),
                      ),
                      child:
                          product.imageUrl != null
                              ? ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: _buildProductImage(product.imageUrl!),
                              )
                              : const Center(
                                child: Icon(
                                  Icons.image,
                                  color: Colors.white,
                                  size: 50,
                                ),
                              ),
                    ),
                    const SizedBox(width: 24),
                    // Informations du produit
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          categoryAsync.when(
                            data: (category) {
                              return Text(
                                'Catégorie: ${category?.name ?? 'Inconnue'}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              );
                            },
                            loading: () => const CircularProgressIndicator(),
                            error:
                                (_, __) => const Text(
                                  'Catégorie: Inconnue',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Prix: ${product.price.toStringAsFixed(0)} FCFA',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                'Quantité: ${product.quantity}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(width: 16),
                              _buildStockStatusBadge(product),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              NeonButton(
                                text: 'Modifier',
                                icon: FontAwesomeIcons.penToSquare,
                                color: NeonTheme.neonTurquoise,
                                onPressed: () {
                                  context.push('/inventory/edit/$productId');
                                },
                              ),
                              const SizedBox(width: 16),
                              NeonButton(
                                text: 'Supprimer',
                                icon: FontAwesomeIcons.trash,
                                color: Colors.red,
                                onPressed: () {
                                  _showDeleteConfirmation(
                                    context,
                                    ref,
                                    product,
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                const Text(
                  'Description',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  product.description ?? 'Aucune description disponible.',
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          NeonCard(
            color: NeonTheme.neonTurquoise,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Gestion du Stock',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    NeonButton(
                      text: 'Ajouter au Stock',
                      icon: FontAwesomeIcons.plus,
                      color: Colors.green,
                      onPressed: () {
                        _showAdjustStockDialog(context, ref, product, true);
                      },
                    ),
                    const SizedBox(width: 16),
                    NeonButton(
                      text: 'Retirer du Stock',
                      icon: FontAwesomeIcons.minus,
                      color: Colors.orange,
                      onPressed: () {
                        _showAdjustStockDialog(context, ref, product, false);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Méthode pour construire l'image du produit
  Widget _buildProductImage(String imageUrl) {
    // Vérifier si c'est une image base64
    if (imageUrl.startsWith('data:image')) {
      try {
        final base64String = imageUrl.split(',')[1];
        final bytes = base64Decode(base64String);
        
        // Ajouter un paramètre unique pour éviter le cache
        final uniqueKey = UniqueKey();
        
        return Image.memory(
          bytes,
          fit: BoxFit.cover,
          key: uniqueKey,
          errorBuilder: (context, error, stackTrace) {
            return const Center(
              child: Icon(
                Icons.image_not_supported,
                color: Colors.white,
                size: 50,
              ),
            );
          },
        );
      } catch (e) {
        return const Center(
          child: Icon(
            Icons.image_not_supported,
            color: Colors.white,
            size: 50,
          ),
        );
      }
    }
    // Vérifier si c'est une URL ou un chemin de fichier local
    else if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      // Image réseau
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return const Center(
            child: Icon(
              Icons.image_not_supported,
              color: Colors.white,
              size: 50,
            ),
          );
        },
      );
    } else {
      // Image locale
      return kIsWeb
          ? Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Icon(Icons.broken_image, color: Colors.red, size: 50),
              );
            },
          )
          : Image.file(
            File(imageUrl),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white,
                  size: 50,
                ),
              );
            },
          );
    }
  }

  Widget _buildStockStatusBadge(Product product) {
    Color statusColor;
    switch (product.stockStatus) {
      case StockStatus.critical:
        statusColor = Colors.red;
        break;
      case StockStatus.low:
        statusColor = Colors.orange;
        break;
      case StockStatus.normal:
        statusColor = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        product.getStatusLabel(),
        style: TextStyle(
          color: statusColor,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _shareProductDetails(BuildContext context, WidgetRef ref) async {
    final productAsync = ref.watch(productProvider(productId));

    if (productAsync.hasValue && productAsync.value != null) {
      final product = productAsync.value!;
      final categoryAsync = ref.watch(categoryProvider(product.categoryId));
      String categoryName = 'Inconnue';

      if (categoryAsync.hasValue && categoryAsync.value != null) {
        categoryName = categoryAsync.value!.name;
      }

      final text = '''
Détails du produit:
Nom: ${product.name}
Catégorie: $categoryName
Prix: ${product.price.toStringAsFixed(0)} FCFA
Quantité en stock: ${product.quantity}
État du stock: ${product.getStatusLabel()}
${product.description != null ? 'Description: ${product.description}' : ''}
''';

      await Share.share(text, subject: 'Détails du produit ${product.name}');
    }
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    Product product,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Confirmer la suppression',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer ${product.name} ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final inventoryService = ref.read(inventoryServiceProvider);
                await inventoryService.deleteProduct(product.id);

                // Rafraîchir les données
                ref.refresh(productsProvider); // ignore: unused_result

                if (context.mounted) {
                  Navigator.of(context).pop();
                  context.go('/inventory');
                }
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showAdjustStockDialog(
    BuildContext context,
    WidgetRef ref,
    Product product,
    bool isAddition,
  ) {
    final TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: Text(
            isAddition ? 'Ajouter au Stock' : 'Retirer du Stock',
            style: const TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                isAddition
                    ? 'Combien d\'unités souhaitez-vous ajouter ?'
                    : 'Combien d\'unités souhaitez-vous retirer ?',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Quantité',
                  labelStyle: const TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isAddition ? Colors.green : Colors.orange,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: isAddition ? Colors.green : Colors.orange,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity <= 0) return;

                final inventoryService = ref.read(inventoryServiceProvider);
                final newQuantity =
                    isAddition
                        ? product.quantity + quantity
                        : product.quantity - quantity;

                // Empêcher les quantités négatives
                if (!isAddition && newQuantity < 0) return;

                final updatedProduct = product.copyWith(
                  quantity: newQuantity,
                  updatedAt: DateTime.now(),
                );

                await inventoryService.updateProduct(updatedProduct);

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(productProvider(product.id));
                // ignore: unused_result
                ref.refresh(productsProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: Text(
                'Confirmer',
                style: TextStyle(
                  color: isAddition ? Colors.green : Colors.orange,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

