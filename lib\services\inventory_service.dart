import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/product.dart';
import '../models/product_category.dart';

// Provider pour le service d'inventaire
final inventoryServiceProvider = Provider<InventoryService>((ref) {
  return InventoryService();
});

// Provider pour la liste des produits
final productsProvider = StateNotifierProvider<ProductsNotifier, List<Product>>(
  (ref) {
    return ProductsNotifier(ref.watch(inventoryServiceProvider));
  },
);

// Notifier pour gérer l'état des produits
class ProductsNotifier extends StateNotifier<List<Product>> {
  final InventoryService _inventoryService;

  ProductsNotifier(this._inventoryService) : super([]) {
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    final products = await _inventoryService.getProducts();
    state = products;
  }

  Future<void> addProduct(Product product) async {
    await _inventoryService.addProduct(product);
    state = [...state, product];
  }

  Future<void> updateProduct(Product product) async {
    await _inventoryService.updateProduct(product);
    state = [
      for (final p in state)
        if (p.id == product.id) product else p,
    ];
  }

  Future<void> deleteProduct(String id) async {
    await _inventoryService.deleteProduct(id);
    state = state.where((p) => p.id != id).toList();
  }

  Future<void> refresh() async {
    _loadProducts();
  }
}

// Provider pour la liste des catégories
final categoriesProvider = FutureProvider<List<ProductCategory>>((ref) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  return await inventoryService.getCategories();
});

// Provider pour les produits filtrés par catégorie
final filteredProductsProvider = FutureProvider.family<List<Product>, String?>((
  ref,
  categoryId,
) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  final products = await inventoryService.getProducts();

  if (categoryId == null || categoryId.isEmpty) {
    return products;
  }

  return products.where((product) => product.categoryId == categoryId).toList();
});

// Provider pour les produits avec un stock faible
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  final products = await inventoryService.getProducts();

  return products
      .where((product) => product.stockStatus != StockStatus.normal)
      .toList();
});

class InventoryService {
  static const String _productsKey = 'inventory_products';
  static const String _categoriesKey = 'inventory_categories';

  // Méthode pour obtenir tous les produits
  Future<List<Product>> getProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Vérifier si les produits existent déjà en cache
      final productsJson = prefs.getStringList(_productsKey);

      if (productsJson != null && productsJson.isNotEmpty) {
        debugPrint(
          'Chargement des produits depuis le cache (${productsJson.length} produits)',
        );
        try {
          final products =
              productsJson.map((json) => Product.fromJson(json)).toList();
          return products;
        } catch (parseError) {
          debugPrint('Erreur lors du parsing des produits: $parseError');
          // En cas d'erreur de parsing, régénérer les produits
          await prefs.remove(_productsKey);
          final products = _generateSampleProducts();
          return products;
        }
      } else {
        debugPrint('Génération de nouveaux produits');
        final products = _generateSampleProducts();
        return products;
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des produits: $e');
      return _generateSampleProducts(); // Toujours retourner des produits même en cas d'erreur
    }
  }

  // Méthode pour obtenir toutes les catégories
  // Remplace la méthode getCategories par :
  Future<List<ProductCategory>> getCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getStringList(_categoriesKey);
      if (categoriesJson == null) {
        return _generateSampleCategories();
      }
      return categoriesJson
          .map((json) => ProductCategory.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des catégories: \$e');
      return [];
    }
  }

  // Méthode pour ajouter un produit
  Future<bool> addProduct(Product product) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final products = await getProducts();

      products.add(product);

      final productsJson = products.map((product) => product.toJson()).toList();
      await prefs.setStringList(_productsKey, productsJson);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'ajout du produit: $e');
      return false;
    }
  }

  // Méthode pour mettre à jour un produit
  Future<bool> updateProduct(Product product) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final products = await getProducts();

      final index = products.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        // Forcer la mise à jour de la date pour éviter les problèmes de cache
        final updatedProduct = product.copyWith(
          updatedAt: DateTime.now(),
        );
        
        products[index] = updatedProduct;

        final productsJson = products.map((p) => p.toJson()).toList();
        await prefs.setStringList(_productsKey, productsJson);
        
        // Rafraîchir les données en mémoire
        // ignore: unused_result
        ref.refresh(productsProvider);
        
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du produit: $e');
      return false;
    }
  }

  // Méthode pour supprimer un produit
  Future<bool> deleteProduct(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final products = await getProducts();

      products.removeWhere((product) => product.id == id);

      final productsJson = products.map((product) => product.toJson()).toList();
      await prefs.setStringList(_productsKey, productsJson);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression du produit: $e');
      return false;
    }
  }

  // Méthode pour ajouter une catégorie
  Future<bool> addCategory(ProductCategory category) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categories = await getCategories();

      categories.add(category);

      final categoriesJson =
          categories.map((category) => category.toJson()).toList();
      await prefs.setStringList(_categoriesKey, categoriesJson);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'ajout de la catégorie: $e');
      return false;
    }
  }

  // Méthode pour mettre à jour une catégorie
  Future<bool> updateCategory(ProductCategory category) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categories = await getCategories();

      final index = categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        categories[index] = category;

        final categoriesJson =
            categories.map((category) => category.toJson()).toList();
        await prefs.setStringList(_categoriesKey, categoriesJson);

        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour de la catégorie: $e');
      return false;
    }
  }

  // Méthode pour supprimer une catégorie
  Future<bool> deleteCategory(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categories = await getCategories();

      categories.removeWhere((category) => category.id == id);

      final categoriesJson =
          categories.map((category) => category.toJson()).toList();
      await prefs.setStringList(_categoriesKey, categoriesJson);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression de la catégorie: $e');
      return false;
    }
  }

  // Générer des catégories d'exemple
  List<ProductCategory> _generateSampleCategories() {
    final categories = [
      ProductCategory(
        id: const Uuid().v4(),
        name: 'VIP-1',
        price: 6000,
        description: 'Coques premium de haute qualité',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      ProductCategory(
        id: const Uuid().v4(),
        name: 'Premium',
        price: 8000,
        description: 'Coques de qualité supérieure',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      ProductCategory(
        id: const Uuid().v4(),
        name: 'Premium Magsafe',
        price: 10000,
        description: 'Coques compatibles avec la technologie MagSafe',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      ProductCategory(
        id: const Uuid().v4(),
        name: 'Fullpicture',
        price: 4500,
        description: 'Coques avec impression complète',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // Sauvegarder les catégories
    SharedPreferences.getInstance().then((prefs) {
      final categoriesJson =
          categories.map((category) => category.toJson()).toList();
      prefs.setStringList(_categoriesKey, categoriesJson);
    });

    return categories;
  }

  // Générer des produits d'exemple
  List<Product> _generateSampleProducts() {
    final random = Random();
    final products = <Product>[];

    // Obtenir les catégories
    getCategories().then((categories) {
      if (categories.isEmpty) return;

      final phoneModels = [
        'iPhone 16 Pro Max',
        'iPhone 16 Pro',
        'iPhone 16',
        'iPhone 15 Pro Max',
        'iPhone 15 Pro',
        'Samsung Galaxy S24 Ultra',
        'Samsung Galaxy S24+',
        'Samsung Galaxy S24',
        'Google Pixel 8 Pro',
        'Google Pixel 8',
      ];

      for (final model in phoneModels) {
        for (final category in categories) {
          final product = Product(
            id: const Uuid().v4(),
            name: 'Coque $model',
            categoryId: category.id,
            categoryName: category.name,
            price: category.price,
            quantity: random.nextInt(20),
            description: 'Coque pour $model de catégorie ${category.name}',
            imageUrl:
                'https://picsum.photos/200/300?random=${random.nextInt(100)}',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          products.add(product);
        }
      }

      // Sauvegarder les produits
      SharedPreferences.getInstance().then((prefs) {
        final productsJson =
            products.map((product) => product.toJson()).toList();
        prefs.setStringList(_productsKey, productsJson);
      });
    });

    return products;
  }
}

