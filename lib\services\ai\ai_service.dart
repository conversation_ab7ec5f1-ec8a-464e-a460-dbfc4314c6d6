import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/ai/ai_config.dart';
import '../../models/ai/ai_message.dart';
import '../../models/ai/ai_provider.dart';
import '../../models/ai/ai_performance.dart';
import '../../services/supabase_service.dart';

// Provider pour le service IA
final aiServiceProvider = Provider<AIService>((ref) {
  final config = ref.watch(aiConfigProvider);
  return AIService(config: config);
});

// Provider pour la configuration de l'IA
final aiConfigProvider = StateNotifierProvider<AIConfigNotifier, AIConfig>((
  ref,
) {
  return AIConfigNotifier();
});

// Provider pour l'historique des messages
final aiMessagesProvider =
    StateNotifierProvider<AIMessagesNotifier, List<AIMessage>>((ref) {
      return AIMessagesNotifier();
    });

// Provider pour les performances de l'IA
final aiPerformanceProvider =
    StateNotifierProvider<AIPerformanceNotifier, List<AIPerformance>>((ref) {
      return AIPerformanceNotifier();
    });

// Notifier pour la configuration de l'IA
class AIConfigNotifier extends StateNotifier<AIConfig> {
  AIConfigNotifier() : super(AIConfig.defaultConfig()) {
    _loadConfig();
  }

  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString('ai_config');

      if (configJson != null) {
        state = AIConfig.fromJson(configJson);
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement de la configuration IA: $e');
    }
  }

  Future<void> saveConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_config', state.toJson());
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde de la configuration IA: $e');
    }
  }

  void updateConfig(AIConfig config) {
    state = config;
    saveConfig();
  }

  void updateProvider(AIProviderType provider) {
    // Mettre à jour le modèle par défaut en fonction du fournisseur
    final defaultModel = AIConfig.getDefaultModelForProvider(provider);
    state = state.copyWith(provider: provider, model: defaultModel);
    saveConfig();
  }

  void updateModel(String model) {
    state = state.copyWith(model: model);
    saveConfig();
  }

  void updateApiKey(String apiKey) {
    state = state.copyWith(apiKey: apiKey);
    saveConfig();
  }

  void updateOrganizationId(String? organizationId) {
    state = state.copyWith(organizationId: organizationId);
    saveConfig();
  }

  void updateTemperature(double temperature) {
    state = state.copyWith(temperature: temperature);
    saveConfig();
  }

  void updateMaxTokens(int maxTokens) {
    state = state.copyWith(maxTokens: maxTokens);
    saveConfig();
  }

  void updateSystemPrompt(String systemPrompt) {
    state = state.copyWith(systemPrompt: systemPrompt);
    saveConfig();
  }
}

// Notifier pour l'historique des messages
class AIMessagesNotifier extends StateNotifier<List<AIMessage>> {
  AIMessagesNotifier() : super([]);

  void addMessage(AIMessage message) {
    state = [...state, message];
  }

  void clearMessages() {
    state = [];
  }
}

// Notifier pour les performances de l'IA
class AIPerformanceNotifier extends StateNotifier<List<AIPerformance>> {
  AIPerformanceNotifier() : super([]) {
    _loadPerformance();
  }

  Future<void> _loadPerformance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final performanceJson = prefs.getStringList('ai_performance');

      if (performanceJson != null) {
        state =
            performanceJson
                .map((json) => AIPerformance.fromJson(json))
                .toList();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des performances IA: $e');
    }
  }

  Future<void> savePerformance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final performanceJson = state.map((perf) => perf.toJson()).toList();
      await prefs.setStringList('ai_performance', performanceJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des performances IA: $e');
    }
  }

  void addPerformance(AIPerformance performance) {
    state = [...state, performance];
    savePerformance();
  }

  void clearPerformance() {
    state = [];
    savePerformance();
  }
}

// Service principal pour l'IA
class AIService {
  final AIConfig config;

  AIService({required this.config});

  // Méthode pour générer du texte à partir de messages (pour compatibilité avec les services existants)
  Future<String> generateText(
    List<AIMessage> messages, {
    String? overrideSystemPrompt,
  }) async {
    return sendMessage(messages, overrideSystemPrompt: overrideSystemPrompt);
  }

  // Méthode pour envoyer un message à l'IA et obtenir une réponse
  Future<String> sendMessage(
    List<AIMessage> messages, {
    String? overrideSystemPrompt,
  }) async {
    try {
      // Temps de début pour mesurer les performances (désactivé)
      // final startTime = DateTime.now();

      // Ajouter le message système au début de la conversation
      final systemPrompt = overrideSystemPrompt ?? config.systemPrompt;
      final fullMessages = [
        AIMessage(role: AIMessageRole.system, content: systemPrompt),
        ...messages,
      ];

      // Si c'est un prompt spécifique pour extraire des informations client
      if (overrideSystemPrompt != null &&
          overrideSystemPrompt.contains('extraction d\'informations client')) {
        return _generateCustomerInfoResponse(messages.last.content);
      }

      String response;
      switch (config.provider) {
        case AIProviderType.openai:
          response = await _sendToOpenAI(fullMessages);
          break;
        case AIProviderType.anthropic:
          response = await _sendToAnthropic(fullMessages);
          break;
        case AIProviderType.gemini:
          response = await _sendToGemini(fullMessages);
          break;
        case AIProviderType.mistral:
          response = await _sendToMistral(fullMessages);
          break;
        case AIProviderType.groq:
          response = await _sendToGroq(fullMessages);
          break;
        case AIProviderType.deepseek:
          response = await _sendToDeepSeek(fullMessages);
          break;
      }

      // Calculer les performances (désactivé pour le moment)
      /*final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      final performance = AIPerformance(
        provider: config.provider,
        model: config.model,
        promptLength: _calculateTokenCount(
          messages.map((m) => m.content).join(' '),
        ),
        responseLength: _calculateTokenCount(response),
        duration: duration.inMilliseconds,
        timestamp: endTime,
      );*/

      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message à l\'IA: $e');
      return 'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.';
    }
  }

  // Méthode pour envoyer un message à OpenAI
  Future<String> _sendToOpenAI(List<AIMessage> messages) async {
    final url = Uri.parse('https://api.openai.com/v1/chat/completions');

    // Vérifier si la clé API est vide
    if (config.apiKey.isEmpty) {
      return "Veuillez configurer votre clé API OpenAI dans les paramètres.";
    }

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${config.apiKey}',
    };

    if (config.organizationId != null && config.organizationId!.isNotEmpty) {
      headers['OpenAI-Organization'] = config.organizationId!;
    }

    // Convertir les messages au format OpenAI
    final formattedMessages =
        messages.map((m) {
          String role;
          switch (m.role) {
            case AIMessageRole.system:
              role = 'system';
              break;
            case AIMessageRole.user:
              role = 'user';
              break;
            case AIMessageRole.assistant:
              role = 'assistant';
              break;
          }
          return {'role': role, 'content': m.content};
        }).toList();

    final body = jsonEncode({
      'model': config.model,
      'messages': formattedMessages,
      'temperature': config.temperature,
      'max_tokens': config.maxTokens,
    });

    try {
      debugPrint(
        'Envoi de la requête à OpenAI avec le modèle: ${config.model}',
      );

      // Enregistrer la requête dans Supabase si configuré
      await _logRequestToSupabase(formattedMessages, config.model);

      final response = await http.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final content = jsonResponse['choices'][0]['message']['content'];

        // Enregistrer la réponse dans Supabase si configuré
        await _logResponseToSupabase(content);

        return content;
      } else {
        final errorBody = response.body;
        debugPrint('Erreur OpenAI: ${response.statusCode} $errorBody');

        // Analyser l'erreur pour donner un message plus clair
        if (errorBody.contains("invalid_api_key")) {
          return "La clé API OpenAI fournie est invalide. Veuillez vérifier votre clé dans les paramètres.";
        } else if (errorBody.contains("insufficient_quota")) {
          return "Votre quota OpenAI est épuisé. Veuillez vérifier votre compte OpenAI.";
        } else if (errorBody.contains("model_not_found")) {
          return "Le modèle '${config.model}' n'est pas disponible. Veuillez sélectionner un autre modèle dans les paramètres.";
        } else {
          return "Erreur lors de la communication avec OpenAI (${response.statusCode}). Veuillez réessayer plus tard.";
        }
      }
    } catch (e) {
      debugPrint('Exception lors de la requête OpenAI: $e');
      return "Erreur de connexion à OpenAI: $e. Veuillez vérifier votre connexion internet.";
    }
  }

  // Méthode pour enregistrer la requête dans Supabase
  Future<void> _logRequestToSupabase(
    List<Map<String, dynamic>> messages,
    String model,
  ) async {
    try {
      // Importer le service Supabase
      final supabaseService = SupabaseService();

      // Vérifier si Supabase est initialisé
      if (!supabaseService.isInitialized) {
        debugPrint(
          'Supabase n\'est pas initialisé, impossible d\'enregistrer la requête AI',
        );
        return;
      }

      // Enregistrer la requête
      await supabaseService.logAIRequest(
        model: model,
        messages: messages,
        userId: config.additionalConfig?['userId'],
      );

      debugPrint('Requête enregistrée dans Supabase');
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'enregistrement de la requête dans Supabase: $e',
      );
    }
  }

  // Méthode pour enregistrer la réponse dans Supabase
  Future<void> _logResponseToSupabase(String response) async {
    try {
      // Importer le service Supabase
      final supabaseService = SupabaseService();

      // Vérifier si Supabase est initialisé
      if (!supabaseService.isInitialized) {
        debugPrint(
          'Supabase n\'est pas initialisé, impossible d\'enregistrer la réponse AI',
        );
        return;
      }

      // Enregistrer la réponse
      await supabaseService.logAIResponse(
        content: response,
        userId: config.additionalConfig?['userId'],
      );

      debugPrint('Réponse enregistrée dans Supabase');
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'enregistrement de la réponse dans Supabase: $e',
      );
    }
  }

  // Méthode pour envoyer un message à Anthropic
  Future<String> _sendToAnthropic(List<AIMessage> messages) async {
    final url = Uri.parse('https://api.anthropic.com/v1/messages');

    final headers = {
      'Content-Type': 'application/json',
      'x-api-key': config.apiKey,
      'anthropic-version': '2023-06-01',
    };

    // Convertir les messages au format Anthropic
    final systemMessage = messages.first;
    final userMessages = messages.sublist(1);

    final body = jsonEncode({
      'model': config.model,
      'system': systemMessage.content,
      'messages':
          userMessages
              .map((m) => {'role': m.role, 'content': m.content})
              .toList(),
      'temperature': config.temperature,
      'max_tokens': config.maxTokens,
    });

    final response = await http.post(url, headers: headers, body: body);

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return jsonResponse['content'][0]['text'];
    } else {
      throw Exception(
        'Échec de la requête Anthropic: ${response.statusCode} ${response.body}',
      );
    }
  }

  // Méthode pour envoyer un message à Gemini
  Future<String> _sendToGemini(List<AIMessage> messages) async {
    // Utiliser la bonne URL pour l'API Gemini
    final url = Uri.parse(
      'https://generativelanguage.googleapis.com/v1/models/${config.model}:generateContent?key=${config.apiKey}',
    );

    final headers = {'Content-Type': 'application/json'};

    // Extraire les messages système, utilisateur et assistant
    final systemMessage =
        messages
            .firstWhere(
              (m) => m.role == AIMessageRole.system,
              orElse:
                  () => AIMessage(
                    role: AIMessageRole.system,
                    content: 'Tu es un assistant virtuel utile et précis.',
                  ),
            )
            .content;

    // Construire la conversation pour Gemini
    final contents = <Map<String, dynamic>>[];

    // Ajouter le message système comme premier message utilisateur
    contents.add({
      'role': 'user',
      'parts': [
        {'text': 'Instructions système: $systemMessage'},
      ],
    });

    // Ajouter une réponse initiale du modèle pour confirmer les instructions
    contents.add({
      'role': 'model',
      'parts': [
        {'text': 'Je comprends ces instructions et je vais les suivre.'},
      ],
    });

    // Ajouter les messages de la conversation (en ignorant le message système initial)
    for (int i = 1; i < messages.length; i++) {
      final message = messages[i];
      String role;

      switch (message.role) {
        case AIMessageRole.user:
          role = 'user';
          break;
        case AIMessageRole.assistant:
          role = 'model';
          break;
        default:
          continue; // Ignorer les autres types de messages
      }

      contents.add({
        'role': role,
        'parts': [
          {'text': message.content},
        ],
      });
    }

    // Format pour l'API Gemini
    final body = jsonEncode({
      'contents': contents,
      'generationConfig': {
        'temperature': config.temperature,
        'maxOutputTokens': config.maxTokens,
        'topP': 0.95,
        'topK': 40,
      },
    });

    try {
      debugPrint('Envoi de la requête à Gemini: ${url.toString()}');
      final response = await http.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        debugPrint('Réponse Gemini: ${response.body}');

        if (jsonResponse['candidates'] != null &&
            jsonResponse['candidates'].isNotEmpty &&
            jsonResponse['candidates'][0]['content'] != null &&
            jsonResponse['candidates'][0]['content']['parts'] != null &&
            jsonResponse['candidates'][0]['content']['parts'].isNotEmpty) {
          return jsonResponse['candidates'][0]['content']['parts'][0]['text'];
        } else {
          debugPrint('Réponse Gemini invalide: ${response.body}');
          return 'Désolé, je n\'ai pas pu générer une réponse. Veuillez réessayer.';
        }
      } else {
        debugPrint('Erreur Gemini: ${response.statusCode} ${response.body}');
        throw Exception(
          'Échec de la requête Gemini: ${response.statusCode} ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('Exception lors de la requête Gemini: $e');
      rethrow;
    }
  }

  // Méthode pour envoyer un message à Mistral
  Future<String> _sendToMistral(List<AIMessage> messages) async {
    final url = Uri.parse('https://api.mistral.ai/v1/chat/completions');

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${config.apiKey}',
    };

    final body = jsonEncode({
      'model': config.model,
      'messages':
          messages.map((m) => {'role': m.role, 'content': m.content}).toList(),
      'temperature': config.temperature,
      'max_tokens': config.maxTokens,
    });

    final response = await http.post(url, headers: headers, body: body);

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return jsonResponse['choices'][0]['message']['content'];
    } else {
      throw Exception(
        'Échec de la requête Mistral: ${response.statusCode} ${response.body}',
      );
    }
  }

  // Méthode pour envoyer un message à Groq
  Future<String> _sendToGroq(List<AIMessage> messages) async {
    final url = Uri.parse('https://api.groq.com/openai/v1/chat/completions');

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${config.apiKey}',
    };

    final body = jsonEncode({
      'model': config.model,
      'messages':
          messages.map((m) => {'role': m.role, 'content': m.content}).toList(),
      'temperature': config.temperature,
      'max_tokens': config.maxTokens,
    });

    final response = await http.post(url, headers: headers, body: body);

    if (response.statusCode == 200) {
      final jsonResponse = jsonDecode(response.body);
      return jsonResponse['choices'][0]['message']['content'];
    } else {
      throw Exception(
        'Échec de la requête Groq: ${response.statusCode} ${response.body}',
      );
    }
  }

  // Méthode pour envoyer un message à DeepSeek
  Future<String> _sendToDeepSeek(List<AIMessage> messages) async {
    final url = Uri.parse('https://api.deepseek.com/chat/completions');

    // Vérifier si la clé API est vide
    if (config.apiKey.isEmpty) {
      return "Veuillez configurer votre clé API DeepSeek dans les paramètres.";
    }

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${config.apiKey}',
    };

    // Convertir les messages au format DeepSeek (compatible OpenAI)
    final formattedMessages =
        messages.map((m) {
          String role;
          switch (m.role) {
            case AIMessageRole.system:
              role = 'system';
              break;
            case AIMessageRole.user:
              role = 'user';
              break;
            case AIMessageRole.assistant:
              role = 'assistant';
              break;
          }
          return {'role': role, 'content': m.content};
        }).toList();

    final body = jsonEncode({
      'model': config.model,
      'messages': formattedMessages,
      'temperature': config.temperature,
      'max_tokens': config.maxTokens,
    });

    try {
      debugPrint(
        'Envoi de la requête à DeepSeek avec le modèle: ${config.model}',
      );

      // Enregistrer la requête dans Supabase si configuré
      await _logRequestToSupabase(formattedMessages, config.model);

      final response = await http.post(url, headers: headers, body: body);

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final content = jsonResponse['choices'][0]['message']['content'];

        // Enregistrer la réponse dans Supabase si configuré
        await _logResponseToSupabase(content);

        return content;
      } else {
        final errorBody = response.body;
        debugPrint('Erreur DeepSeek: ${response.statusCode} $errorBody');

        // Analyser l'erreur pour donner un message plus clair
        if (errorBody.contains("invalid_api_key")) {
          return "La clé API DeepSeek fournie est invalide. Veuillez vérifier votre clé dans les paramètres.";
        } else if (errorBody.contains("insufficient_quota")) {
          return "Votre quota DeepSeek est épuisé. Veuillez vérifier votre compte DeepSeek.";
        } else if (errorBody.contains("model_not_found")) {
          return "Le modèle '${config.model}' n'est pas disponible. Veuillez sélectionner un autre modèle dans les paramètres.";
        } else {
          return "Erreur lors de la communication avec DeepSeek (${response.statusCode}). Veuillez réessayer plus tard.";
        }
      }
    } catch (e) {
      debugPrint('Exception lors de la requête DeepSeek: $e');
      return "Erreur de connexion à DeepSeek: $e. Veuillez vérifier votre connexion internet.";
    }
  }

  // Générer une réponse JSON avec les informations client extraites
  String _generateCustomerInfoResponse(String message) {
    // Simuler l'extraction d'informations client
    // Dans une implémentation réelle, cela utiliserait un modèle d'IA pour extraire les informations

    // Rechercher des patterns simples pour la démonstration
    final namePattern = RegExp(
      r"(?:je m'appelle|mon nom est|c'est) ([A-Za-zÀ-ÖØ-öø-ÿ\s]+)",
    );
    final phonePattern = RegExp(
      r"(?:téléphone|portable|numéro).*?(\d[\d\s]{8,})",
    );
    final emailPattern = RegExp(
      r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})",
    );

    String? name;
    String? phone;
    String? email;

    // Extraire le nom
    final nameMatch = namePattern.firstMatch(message);
    if (nameMatch != null) {
      name = nameMatch.group(1)?.trim();
    }

    // Extraire le téléphone
    final phoneMatch = phonePattern.firstMatch(message);
    if (phoneMatch != null) {
      phone = phoneMatch.group(1)?.replaceAll(RegExp(r'\s'), '');
    }

    // Extraire l'email
    final emailMatch = emailPattern.firstMatch(message);
    if (emailMatch != null) {
      email = emailMatch.group(1);
    }

    // Si aucune information n'a été trouvée, retourner un JSON vide
    if (name == null && phone == null && email == null) {
      return '{}';
    }

    // Construire la réponse JSON
    return '''
{
  "name": ${name != null ? '"$name"' : 'null'},
  "phone": ${phone != null ? '"$phone"' : 'null'},
  "email": ${email != null ? '"$email"' : 'null'},
  "source": "Conversation IA",
  "tags": ["chatbot", "auto-détecté"],
  "notes": "Informations extraites automatiquement par l'IA"
}
''';
  }
}
