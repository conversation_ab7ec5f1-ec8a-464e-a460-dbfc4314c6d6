import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/theme_provider.dart';
import '../../models/knowledge_article.dart';
import '../../models/ai/ai_provider.dart';
import '../../services/knowledge_service.dart';
import '../../services/knowledge_ai_chat_service.dart';
import '../../services/ai/ai_service.dart';
import 'knowledge_article_editor.dart';
import 'knowledge_article_detail.dart';

class KnowledgeScreen extends ConsumerStatefulWidget {
  const KnowledgeScreen({super.key});

  @override
  ConsumerState<KnowledgeScreen> createState() => _KnowledgeScreenState();
}

class _KnowledgeScreenState extends ConsumerState<KnowledgeScreen> {
  String _selectedCategory = 'Tous';
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  Widget build(BuildContext context) {
    final categories = ref.watch(knowledgeCategoriesProvider);
    final articlesAsync = ref.watch(knowledgeArticlesProvider);
    final appTheme = ref.watch(themeProvider);

    return Scaffold(
      backgroundColor: appTheme.primaryColor,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Titre et barre de recherche
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child:
                      _isSearching
                          ? TextField(
                            autofocus: true,
                            decoration: InputDecoration(
                              hintText: 'Rechercher...',
                              hintStyle: TextStyle(
                                color:
                                    ref.watch(isDarkThemeProvider)
                                        ? Colors.white.withAlpha(100)
                                        : Colors.black.withAlpha(100),
                              ),
                              border: InputBorder.none,
                              prefixIcon: Icon(
                                Icons.search,
                                color:
                                    ref.watch(isDarkThemeProvider)
                                        ? Colors.white
                                        : Colors.black,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  Icons.close,
                                  color:
                                      ref.watch(isDarkThemeProvider)
                                          ? Colors.white
                                          : Colors.black,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _isSearching = false;
                                    _searchQuery = '';
                                  });
                                },
                              ),
                            ),
                            style: TextStyle(
                              color:
                                  ref.watch(isDarkThemeProvider)
                                      ? Colors.white
                                      : Colors.black,
                            ),
                            onChanged: (value) {
                              setState(() {
                                _searchQuery = value;
                              });
                            },
                          )
                          : Text(
                            'Base de connaissances',
                            style: TextStyle(
                              color:
                                  ref.watch(isDarkThemeProvider)
                                      ? Colors.white
                                      : Colors.black,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
                if (!_isSearching)
                  IconButton(
                    icon: Icon(
                      Icons.search,
                      color:
                          ref.watch(isDarkThemeProvider)
                              ? Colors.white
                              : Colors.black,
                    ),
                    onPressed: () {
                      setState(() {
                        _isSearching = true;
                      });
                    },
                  ),
              ],
            ),
          ),

          // Filtres de catégories
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = category == _selectedCategory;

                return Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: FilterChip(
                    selected: isSelected,
                    label: Text(category),
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: const Color(0xFF1A1A2E),
                    selectedColor: appTheme.secondaryColor.withAlpha(50),
                    checkmarkColor: appTheme.secondaryColor,
                    labelStyle: TextStyle(
                      color:
                          isSelected
                              ? appTheme.secondaryColor
                              : (ref.watch(isDarkThemeProvider)
                                  ? Colors.white
                                  : Colors.black),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                      side: BorderSide(
                        color:
                            isSelected
                                ? appTheme.secondaryColor
                                : Colors.transparent,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Contenu principal
          Expanded(
            child: articlesAsync.when(
              loading:
                  () => Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        appTheme.secondaryColor,
                      ),
                    ),
                  ),
              error:
                  (error, stack) => Center(
                    child: Text(
                      'Erreur lors du chargement des articles: $error',
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
              data: (articles) {
                // Filtrer par catégorie
                List<KnowledgeArticle> filteredArticles =
                    _selectedCategory == 'Tous'
                        ? articles
                        : articles
                            .where((a) => a.category == _selectedCategory)
                            .toList();

                // Filtrer par recherche si nécessaire
                if (_searchQuery.isNotEmpty) {
                  final query = _searchQuery.toLowerCase();
                  filteredArticles =
                      filteredArticles.where((article) {
                        return article.title.toLowerCase().contains(query) ||
                            article.content.toLowerCase().contains(query) ||
                            article.tags.any(
                              (tag) => tag.toLowerCase().contains(query),
                            );
                      }).toList();
                }

                return filteredArticles.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.search_off,
                            color: Colors.white54,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isNotEmpty
                                ? 'Aucun résultat pour "$_searchQuery"'
                                : 'Aucun article dans cette catégorie',
                            style: const TextStyle(color: Colors.white70),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredArticles.length,
                      itemBuilder: (context, index) {
                        final article = filteredArticles[index];
                        return _buildKnowledgeCard(
                          id: article.id,
                          title: article.title,
                          content: article.content,
                          lastUpdated: _formatDate(article.lastUpdated),
                          category: article.category,
                          tags: article.tags,
                          isPublished: article.isPublished,
                        );
                      },
                    );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton.small(
            heroTag: 'add_article',
            backgroundColor: Colors.green,
            child: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              // Ouvrir l'éditeur d'article
              Navigator.of(context)
                  .push(
                    MaterialPageRoute(
                      builder: (context) => const KnowledgeArticleEditor(),
                    ),
                  )
                  .then((_) {
                    // Rafraîchir la liste des articles
                    ref.invalidate(knowledgeArticlesProvider);
                  });
            },
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: 'chat',
            backgroundColor: appTheme.secondaryColor,
            child: const Icon(Icons.chat, color: Colors.white),
            onPressed: () {
              // Ouvrir le chatbot
              _showChatbotDialog(context);
            },
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildKnowledgeCard({
    required String id,
    required String title,
    required String content,
    required String lastUpdated,
    required String category,
    required List<String> tags,
    required bool isPublished,
  }) {
    final appTheme = ref.watch(themeProvider);
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: const Color(0xFF1A1A2E),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.of(context)
              .push(
                MaterialPageRoute(
                  builder: (context) => KnowledgeArticleDetail(articleId: id),
                ),
              )
              .then((_) {
                // Rafraîchir la liste des articles
                ref.invalidate(knowledgeArticlesProvider);
              });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        if (!isPublished)
                          const Padding(
                            padding: EdgeInsets.only(right: 8.0),
                            child: Icon(
                              Icons.visibility_off,
                              size: 16,
                              color: Colors.orange,
                            ),
                          ),
                        Expanded(
                          child: Text(
                            title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Chip(
                    label: Text(
                      category,
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                    backgroundColor: appTheme.secondaryColor.withAlpha(50),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                content,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.white.withAlpha(200),
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                children: tags.map((tag) => _buildTag(tag)).toList(),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Dernière mise à jour: $lastUpdated',
                    style: TextStyle(
                      color: Colors.white.withAlpha(130),
                      fontSize: 12,
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14,
                    color: Colors.white.withAlpha(130),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xFF0F0F1E),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        tag,
        style: TextStyle(color: Colors.white.withAlpha(180), fontSize: 12),
      ),
    );
  }

  // Méthode pour obtenir le nom du fournisseur d'IA
  String _getProviderName(AIProviderType provider) {
    switch (provider) {
      case AIProviderType.openai:
        return 'OpenAI';
      case AIProviderType.anthropic:
        return 'Anthropic';
      case AIProviderType.gemini:
        return 'Google Gemini';
      case AIProviderType.mistral:
        return 'Mistral AI';
      case AIProviderType.groq:
        return 'Groq';
      case AIProviderType.deepseek:
        return 'DeepSeek';
      // Pas de default nécessaire car tous les cas sont couverts
    }
  }

  void _showChatbotDialog(BuildContext context) {
    final appTheme = ref.watch(themeProvider);
    final TextEditingController messageController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => Consumer(
            builder: (context, ref, child) {
              final chatMessages = ref.watch(knowledgeChatMessagesProvider);
              final isLoading = ref.watch(knowledgeChatLoadingProvider);
              final chatService = ref.watch(knowledgeAIChatServiceProvider);
              final aiConfig = ref.watch(aiConfigProvider);

              return AlertDialog(
                backgroundColor: const Color(0xFF1A1A2E),
                title: Row(
                  children: [
                    Icon(Icons.smart_toy, color: appTheme.secondaryColor),
                    const SizedBox(width: 8),
                    const Text(
                      'Demander à l\'IA',
                      style: TextStyle(color: Colors.white),
                    ),
                    const Spacer(),
                    // Afficher le fournisseur d'IA actuel
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(77), // 0.3 * 255 ≈ 77
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: appTheme.secondaryColor.withAlpha(
                            128,
                          ), // 0.5 * 255 ≈ 128
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _getProviderName(aiConfig.provider),
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 4),
                          IconButton(
                            icon: const Icon(
                              Icons.settings,
                              size: 16,
                              color: Colors.white70,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                              context.push('/settings');
                            },
                            tooltip: 'Configurer l\'IA',
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                content: SizedBox(
                  width: 500,
                  height: 400,
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color(0xFF0F0F1E),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child:
                              chatMessages.isEmpty && !isLoading
                                  ? const Center(
                                    child: Text(
                                      'Posez votre question à l\'IA...',
                                      style: TextStyle(color: Colors.white54),
                                    ),
                                  )
                                  : ListView.builder(
                                    itemCount:
                                        chatMessages.length +
                                        (isLoading ? 1 : 0),
                                    itemBuilder: (context, index) {
                                      if (isLoading &&
                                          index == chatMessages.length) {
                                        return Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    appTheme.secondaryColor,
                                                  ),
                                            ),
                                          ),
                                        );
                                      }

                                      final message = chatMessages[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 4.0,
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              message.isUser
                                                  ? MainAxisAlignment.end
                                                  : MainAxisAlignment.start,
                                          children: [
                                            Container(
                                              constraints: const BoxConstraints(
                                                maxWidth: 300,
                                              ),
                                              padding: const EdgeInsets.all(12),
                                              decoration: BoxDecoration(
                                                color:
                                                    message.isUser
                                                        ? appTheme
                                                            .secondaryColor
                                                            .withAlpha(50)
                                                        : const Color(
                                                          0xFF1A1A2E,
                                                        ),
                                                borderRadius:
                                                    BorderRadius.circular(16),
                                                border: Border.all(
                                                  color:
                                                      message.isUser
                                                          ? appTheme
                                                              .secondaryColor
                                                          : Colors.white24,
                                                  width: 1,
                                                ),
                                              ),
                                              child: Text(
                                                message.content,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextField(
                        controller: messageController,
                        decoration: InputDecoration(
                          hintText: 'Tapez votre message...',
                          hintStyle: const TextStyle(color: Colors.white38),
                          filled: true,
                          fillColor: const Color(0xFF0F0F1E),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide.none,
                          ),
                          suffixIcon: IconButton(
                            icon: Icon(
                              Icons.send,
                              color: appTheme.secondaryColor,
                            ),
                            onPressed:
                                isLoading
                                    ? null
                                    : () async {
                                      final message =
                                          messageController.text.trim();
                                      if (message.isEmpty) return;

                                      // Ajouter le message de l'utilisateur
                                      ref
                                          .read(
                                            knowledgeChatMessagesProvider
                                                .notifier,
                                          )
                                          .addUserMessage(message);
                                      messageController.clear();

                                      // Définir l'état de chargement
                                      ref
                                          .read(
                                            knowledgeChatLoadingProvider
                                                .notifier,
                                          )
                                          .state = true;

                                      try {
                                        // Envoyer la question à l'IA
                                        final response = await chatService
                                            .askQuestion(message);

                                        // Ajouter la réponse de l'IA
                                        if (context.mounted) {
                                          ref
                                              .read(
                                                knowledgeChatMessagesProvider
                                                    .notifier,
                                              )
                                              .addAIMessage(response);
                                        }
                                      } catch (e) {
                                        // En cas d'erreur, ajouter un message d'erreur
                                        if (context.mounted) {
                                          ref
                                              .read(
                                                knowledgeChatMessagesProvider
                                                    .notifier,
                                              )
                                              .addAIMessage(
                                                'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.',
                                              );
                                        }
                                      } finally {
                                        // Réinitialiser l'état de chargement
                                        if (context.mounted) {
                                          ref
                                              .read(
                                                knowledgeChatLoadingProvider
                                                    .notifier,
                                              )
                                              .state = false;
                                        }
                                      }
                                    },
                          ),
                        ),
                        style: const TextStyle(color: Colors.white),
                        onSubmitted:
                            isLoading
                                ? null
                                : (message) async {
                                  if (message.isEmpty) return;

                                  // Ajouter le message de l'utilisateur
                                  ref
                                      .read(
                                        knowledgeChatMessagesProvider.notifier,
                                      )
                                      .addUserMessage(message);
                                  messageController.clear();

                                  // Définir l'état de chargement
                                  ref
                                      .read(
                                        knowledgeChatLoadingProvider.notifier,
                                      )
                                      .state = true;

                                  try {
                                    // Envoyer la question à l'IA
                                    final response = await chatService
                                        .askQuestion(message);

                                    // Ajouter la réponse de l'IA
                                    if (context.mounted) {
                                      ref
                                          .read(
                                            knowledgeChatMessagesProvider
                                                .notifier,
                                          )
                                          .addAIMessage(response);
                                    }
                                  } catch (e) {
                                    // En cas d'erreur, ajouter un message d'erreur
                                    if (context.mounted) {
                                      ref
                                          .read(
                                            knowledgeChatMessagesProvider
                                                .notifier,
                                          )
                                          .addAIMessage(
                                            'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.',
                                          );
                                    }
                                  } finally {
                                    // Réinitialiser l'état de chargement
                                    if (context.mounted) {
                                      ref
                                          .read(
                                            knowledgeChatLoadingProvider
                                                .notifier,
                                          )
                                          .state = false;
                                    }
                                  }
                                },
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    child: const Text(
                      'Effacer',
                      style: TextStyle(color: Colors.red),
                    ),
                    onPressed: () {
                      ref
                          .read(knowledgeChatMessagesProvider.notifier)
                          .clearMessages();
                    },
                  ),
                  TextButton(
                    child: Text(
                      'Fermer',
                      style: TextStyle(color: appTheme.secondaryColor),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              );
            },
          ),
    );
  }
}
