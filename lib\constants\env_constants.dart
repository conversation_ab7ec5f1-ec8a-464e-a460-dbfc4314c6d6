import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Constantes d'environnement
class EnvConstants {
  // Supabase
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';

  // OpenAI
  static String get openaiApiKey => dotenv.env['OPENAI_API_KEY'] ?? '';
  
  // Chatbot
  static String get chatbotModelId => dotenv.env['CHATBOT_MODEL_ID'] ?? '';
  
  // Application
  static String get appEnv => dotenv.env['APP_ENV'] ?? 'development';
  static bool get debugMode => dotenv.env['DEBUG_MODE'] == 'true';
  
  // Base de données
  static String get databaseUrl => dotenv.env['DATABASE_URL'] ?? '';
  static String get databaseUser => dotenv.env['DATABASE_USER'] ?? '';
  static String get databasePassword => dotenv.env['DATABASE_PASSWORD'] ?? '';
  
  // Twilio (si utilisé)
  static String get twilioAccountSid => dotenv.env['TWILIO_ACCOUNT_SID'] ?? '';
  static String get twilioAuthToken => dotenv.env['TWILIO_AUTH_TOKEN'] ?? '';
  static String get twilioPhoneNumber => dotenv.env['TWILIO_PHONE_NUMBER'] ?? '';
  
  // Vérifier si les variables d'environnement essentielles sont définies
  static bool get hasRequiredEnvVars {
    return supabaseUrl.isNotEmpty && supabaseAnonKey.isNotEmpty;
  }
  
  // Vérifier si les variables d'environnement pour l'IA sont définies
  static bool get hasAiEnvVars {
    return openaiApiKey.isNotEmpty;
  }
}
