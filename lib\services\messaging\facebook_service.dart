import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../models/messaging/message.dart';
import '../../models/messaging/channel_config.dart';

class FacebookService {
  bool _isInitialized = false;
  late ChannelConfig _config;
  final String _baseUrl = 'https://graph.facebook.com/v18.0';

  Future<void> initialize(ChannelConfig config) async {
    _config = config;
    _isInitialized = true;
    debugPrint('Facebook service initialized');
  }

  bool get isInitialized => _isInitialized;

  Future<void> sendMessage(Message message) async {
    if (!_isInitialized) {
      throw Exception('Facebook service not initialized');
    }

    try {
      final recipientId = message.contactId;
      final endpoint = '$_baseUrl/${_config.pageId}/messages';

      Map<String, dynamic> payload = {
        'recipient': {'id': recipientId},
        'message': {'text': message.content},
        'messaging_type': 'RESPONSE',
      };

      // Si le message contient un média
      if (message.mediaUrl != null && message.mediaType != null) {
        switch (message.mediaType) {
          case MessageType.image:
            payload = {
              'recipient': {'id': recipientId},
              'message': {
                'attachment': {
                  'type': 'image',
                  'payload': {'url': message.mediaUrl, 'is_reusable': true},
                },
              },
              'messaging_type': 'RESPONSE',
            };
            break;
          case MessageType.video:
            payload = {
              'recipient': {'id': recipientId},
              'message': {
                'attachment': {
                  'type': 'video',
                  'payload': {'url': message.mediaUrl, 'is_reusable': true},
                },
              },
              'messaging_type': 'RESPONSE',
            };
            break;
          case MessageType.audio:
            payload = {
              'recipient': {'id': recipientId},
              'message': {
                'attachment': {
                  'type': 'audio',
                  'payload': {'url': message.mediaUrl, 'is_reusable': true},
                },
              },
              'messaging_type': 'RESPONSE',
            };
            break;
          case MessageType.document:
            payload = {
              'recipient': {'id': recipientId},
              'message': {
                'attachment': {
                  'type': 'file',
                  'payload': {'url': message.mediaUrl, 'is_reusable': true},
                },
              },
              'messaging_type': 'RESPONSE',
            };
            break;
          default:
            break;
        }
      }

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_config.apiKey}',
        },
        body: jsonEncode(payload),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to send Facebook message: ${response.body}');
      }

      debugPrint('Facebook message sent successfully');
    } catch (e) {
      debugPrint('Error sending Facebook message: $e');
      rethrow;
    }
  }

  Future<void> processWebhook(Map<String, dynamic> payload) async {
    if (!_isInitialized) {
      throw Exception('Facebook service not initialized');
    }

    try {
      if (payload.containsKey('entry')) {
        for (var entry in payload['entry']) {
          if (entry.containsKey('messaging')) {
            for (var messaging in entry['messaging']) {
              final senderId = messaging['sender']['id'];

              // Traiter les messages texte
              if (messaging.containsKey('message') &&
                  messaging['message'].containsKey('text')) {
                final text = messaging['message']['text'];
                debugPrint('Received Facebook message from $senderId: $text');

                // Ici, vous devriez créer un nouveau message dans votre système
              }

              // Traiter les médias
              if (messaging.containsKey('message') &&
                  messaging['message'].containsKey('attachments')) {
                for (var attachment in messaging['message']['attachments']) {
                  final type = attachment['type'];
                  final url = attachment['payload']['url'];

                  debugPrint('Received Facebook $type from $senderId: $url');

                  // Ici, vous devriez créer un nouveau message avec média dans votre système
                }
              }

              // Traiter les accusés de réception
              if (messaging.containsKey('delivery')) {
                // Récupérer le timestamp jusqu'auquel les messages sont livrés
                final timestamp = messaging['delivery']['watermark'];
                final messageIds = messaging['delivery']['mids'] ?? [];

                debugPrint(
                  'Facebook delivery receipt for messages: $messageIds, timestamp: $timestamp',
                );

                // Ici, vous devriez mettre à jour le statut des messages dans votre système
              }

              // Traiter les notifications de lecture
              if (messaging.containsKey('read')) {
                // Récupérer le timestamp jusqu'auquel les messages sont lus
                final timestamp = messaging['read']['watermark'];

                debugPrint(
                  'Facebook read receipt for messages up to timestamp: $timestamp',
                );

                // Ici, vous devriez mettre à jour le statut des messages dans votre système
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error processing Facebook webhook: $e');
      rethrow;
    }
  }
}
