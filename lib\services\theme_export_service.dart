import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/theme/theme_export.dart';
import '../theme/neon_theme.dart';
import '../theme/tz1_theme.dart';
import '../theme/tz2_theme.dart';

/// Provider pour le service d'exportation de thèmes
final themeExportServiceProvider = Provider<ThemeExportService>((ref) {
  return ThemeExportService();
});

/// Service pour l'exportation et l'importation de thèmes
class ThemeExportService {
  static const String _customThemesKey = 'custom_themes';

  /// Exporte un thème prédéfini
  ThemeExport exportPredefinedTheme(String themeName) {
    switch (themeName) {
      case 'unique':
        return _createNeonThemeExport();
      case 'tz1':
        return _createTZ1ThemeExport();
      case 'tz2':
        return _createTZ2ThemeExport();
      case 'creamyBeans':
        return _createCreamyBeansThemeExport();
      case 'orixPurpleSplit':
        return _createOrixPurpleSplitThemeExport();
      default:
        throw Exception('Thème non reconnu: $themeName');
    }
  }

  /// Crée un export du thème Neon
  ThemeExport _createNeonThemeExport() {
    return ThemeExport(
      name: 'Thème Unique',
      description: 'Thème sombre avec des accents néon',
      createdAt: DateTime.now(),
      colors: ThemeColors(
        primary: NeonTheme.primaryAccent,
        onPrimary: Colors.white,
        secondary: NeonTheme.secondaryAccent,
        onSecondary: Colors.white,
        background: NeonTheme.darkBackground,
        onBackground: Colors.white,
        surface: NeonTheme.darkSurface,
        onSurface: Colors.white,
        error: NeonTheme.errorColor,
        onError: Colors.white,
        border: NeonTheme.borderColor,
        inputFill: NeonTheme.darkSurface,
      ),
      gradients: ThemeGradients(
        mainGradientColors:
            NeonTheme.mainGradient.colors.map((c) => c.toARGB32()).toList(),
        sidebarGradientColors:
            NeonTheme.sidebarGradient.colors.map((c) => c.toARGB32()).toList(),
        cardGradientColors:
            NeonTheme.cardGradient.colors.map((c) => c.toARGB32()).toList(),
        secondaryGradientColors:
            NeonTheme.secondaryGradient.colors
                .map((c) => c.toARGB32())
                .toList(),
        activeGradientColors:
            NeonTheme.activeGradient.colors.map((c) => c.toARGB32()).toList(),
      ),
      isDark: true,
    );
  }

  /// Crée un export du thème TZ1
  ThemeExport _createTZ1ThemeExport() {
    return ThemeExport(
      name: 'Thème TZ-1',
      description: 'Thème clair avec palette de couleurs spécifiques',
      createdAt: DateTime.now(),
      colors: ThemeColors(
        primary: TZ1Theme.lavenderActive,
        onPrimary: TZ1Theme.primaryText,
        secondary: TZ1Theme.pinkReview,
        onSecondary: TZ1Theme.primaryText,
        background: TZ1Theme.pureWhite,
        onBackground: TZ1Theme.primaryText,
        surface: TZ1Theme.pureWhite,
        onSurface: TZ1Theme.primaryText,
        error: TZ1Theme.emergencyRed,
        onError: TZ1Theme.pureWhite,
        border: TZ1Theme.borderGrey,
        inputFill: TZ1Theme.pureWhite,
      ),
      gradients: ThemeGradients(
        mainGradientColors:
            TZ1Theme.mainGradient.colors.map((c) => c.toARGB32()).toList(),
        sidebarGradientColors: [
          TZ1Theme.pureWhite.toARGB32(),
          TZ1Theme.pureWhite.toARGB32(),
        ],
        cardGradientColors: [
          TZ1Theme.pureWhite.toARGB32(),
          TZ1Theme.pureWhite.toARGB32(),
        ],
        secondaryGradientColors: [
          TZ1Theme.pureWhite.toARGB32(),
          TZ1Theme.pureWhite.toARGB32(),
        ],
        activeGradientColors: [
          TZ1Theme.lavenderActive.toARGB32(),
          TZ1Theme.pinkReview.toARGB32(),
        ],
      ),
      isDark: false,
    );
  }

  /// Crée un export du thème TZ2
  ThemeExport _createTZ2ThemeExport() {
    return ThemeExport(
      name: 'Thème TZ-2',
      description: 'Thème lavande avec dégradés',
      createdAt: DateTime.now(),
      colors: ThemeColors(
        primary: TZ2Theme.cloudyViolet,
        onPrimary: TZ2Theme.primaryTextLight,
        secondary: TZ2Theme.freshMintGreen,
        onSecondary: TZ2Theme.primaryTextLight,
        background: TZ2Theme.airyLavenderEnd,
        onBackground: TZ2Theme.primaryTextDark,
        surface: TZ2Theme.primaryTextLight,
        onSurface: TZ2Theme.primaryTextDark,
        error: Colors.redAccent,
        onError: TZ2Theme.primaryTextLight,
        border: TZ2Theme.primaryTextLight.withValues(
          alpha: 51,
        ), // 0.2 * 255 ≈ 51
        inputFill: TZ2Theme.airyLavenderStart,
      ),
      gradients: ThemeGradients(
        mainGradientColors:
            TZ2Theme.zomoGradient.colors.map((c) => c.toARGB32()).toList(),
        sidebarGradientColors: [
          TZ2Theme.airyLavenderStart.toARGB32(),
          TZ2Theme.airyLavenderEnd.toARGB32(),
        ],
        cardGradientColors: [
          TZ2Theme.primaryTextLight.toARGB32(),
          TZ2Theme.primaryTextLight.toARGB32(),
        ],
        secondaryGradientColors: [
          TZ2Theme.airyLavenderStart.toARGB32(),
          TZ2Theme.airyLavenderEnd.toARGB32(),
        ],
        activeGradientColors: [
          TZ2Theme.cloudyViolet.toARGB32(),
          TZ2Theme.radiantPink.toARGB32(),
        ],
      ),
      isDark: false,
    );
  }

  /// Crée un export du thème Creamy Beans
  ThemeExport _createCreamyBeansThemeExport() {
    // Utiliser les couleurs du thème Creamy Beans
    const cappuccinoSombre = Color(0xFF4C2A1C);
    const coffeeLatte = Color(0xFFF2E8D4);
    const cremeVanille = Color(0xFFEFC66A);
    const laitMousseux = Color(0xFFE8C4B8);
    const espressoDense = Color(0xFF362015);

    return ThemeExport(
      name: 'Creamy Beans',
      description: 'Thème café avec des tons beiges et bruns',
      createdAt: DateTime.now(),
      colors: ThemeColors(
        primary: cappuccinoSombre,
        onPrimary: Colors.white,
        secondary: cremeVanille,
        onSecondary: espressoDense,
        background: coffeeLatte,
        onBackground: espressoDense,
        surface: laitMousseux,
        onSurface: espressoDense,
        error: Colors.redAccent,
        onError: Colors.white,
        border: espressoDense.withValues(alpha: 51), // 0.2 * 255 ≈ 51
        inputFill: coffeeLatte,
      ),
      gradients: ThemeGradients(
        mainGradientColors: [coffeeLatte.toARGB32(), laitMousseux.toARGB32()],
        sidebarGradientColors: [
          cappuccinoSombre.toARGB32(),
          cappuccinoSombre.withValues(alpha: 204).toARGB32(), // 0.8 * 255 ≈ 204
        ],
        cardGradientColors: [laitMousseux.toARGB32(), laitMousseux.toARGB32()],
        secondaryGradientColors: [
          coffeeLatte.toARGB32(),
          laitMousseux.toARGB32(),
        ],
        activeGradientColors: [
          cappuccinoSombre.toARGB32(),
          cremeVanille.toARGB32(),
        ],
      ),
      isDark: false,
    );
  }

  /// Crée un export du thème Orix Purple Split
  ThemeExport _createOrixPurpleSplitThemeExport() {
    // Utiliser les couleurs du thème Orix Purple Split
    const orixFondGeneral = Color(0xFF2B2543);
    const orixCartes = Color(0xFF3C2E59);
    const orixAccentPrincipal = Color(0xFFE3B487);
    const orixAccentSecondaire = Color(0xFF75E3DD);
    const textPrincipalOrix = Color(0xFFDAD9DE);

    return ThemeExport(
      name: 'Orix Purple Split',
      description: 'Thème sombre avec des accents violets et cyan',
      createdAt: DateTime.now(),
      colors: ThemeColors(
        primary: orixAccentPrincipal,
        onPrimary: orixFondGeneral,
        secondary: orixAccentSecondaire,
        onSecondary: textPrincipalOrix,
        background: orixFondGeneral,
        onBackground: textPrincipalOrix,
        surface: orixCartes,
        onSurface: textPrincipalOrix,
        error: Colors.redAccent,
        onError: textPrincipalOrix,
        border: orixAccentSecondaire.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        inputFill: orixCartes,
      ),
      gradients: ThemeGradients(
        mainGradientColors: [
          orixFondGeneral.toARGB32(),
          const Color(0xFF221E38).toARGB32(),
        ],
        sidebarGradientColors: [
          orixCartes.toARGB32(),
          orixFondGeneral.toARGB32(),
        ],
        cardGradientColors: [orixCartes.toARGB32(), orixCartes.toARGB32()],
        secondaryGradientColors: [
          orixFondGeneral.toARGB32(),
          orixCartes.toARGB32(),
        ],
        activeGradientColors: [
          orixAccentPrincipal.toARGB32(),
          orixAccentSecondaire.toARGB32(),
        ],
      ),
      isDark: true,
    );
  }

  /// Exporte un thème vers un fichier JSON
  Future<String> exportThemeToFile(ThemeExport theme) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          '${theme.name.replaceAll(' ', '_').toLowerCase()}_theme.json';
      final filePath = '${directory.path}/$fileName';

      final file = File(filePath);
      await file.writeAsString(theme.toJson());

      return filePath;
    } catch (e) {
      debugPrint('Erreur lors de l\'exportation du thème: $e');
      throw Exception('Impossible d\'exporter le thème: $e');
    }
  }

  /// Partage un thème
  Future<void> shareTheme(ThemeExport theme) async {
    try {
      final filePath = await exportThemeToFile(theme);
      await Share.shareXFiles([XFile(filePath)], text: 'Thème ${theme.name}');
    } catch (e) {
      debugPrint('Erreur lors du partage du thème: $e');
      throw Exception('Impossible de partager le thème: $e');
    }
  }

  /// Importe un thème depuis un fichier JSON
  Future<ThemeExport> importThemeFromFile(String filePath) async {
    try {
      final file = File(filePath);
      final jsonString = await file.readAsString();
      return ThemeExport.fromJson(jsonString);
    } catch (e) {
      debugPrint('Erreur lors de l\'importation du thème: $e');
      throw Exception('Impossible d\'importer le thème: $e');
    }
  }

  /// Sauvegarde un thème personnalisé
  Future<void> saveCustomTheme(ThemeExport theme) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customThemesJson = prefs.getStringList(_customThemesKey) ?? [];

      // Vérifier si un thème avec le même nom existe déjà
      final themeNames =
          customThemesJson
              .map((json) => ThemeExport.fromJson(json).name)
              .toList();

      if (themeNames.contains(theme.name)) {
        // Mettre à jour le thème existant
        final updatedThemes =
            customThemesJson.map((json) {
              final existingTheme = ThemeExport.fromJson(json);
              if (existingTheme.name == theme.name) {
                return theme.toJson();
              }
              return json;
            }).toList();

        await prefs.setStringList(_customThemesKey, updatedThemes);
      } else {
        // Ajouter un nouveau thème
        customThemesJson.add(theme.toJson());
        await prefs.setStringList(_customThemesKey, customThemesJson);
      }
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde du thème personnalisé: $e');
      throw Exception('Impossible de sauvegarder le thème personnalisé: $e');
    }
  }

  /// Récupère tous les thèmes personnalisés
  Future<List<ThemeExport>> getCustomThemes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customThemesJson = prefs.getStringList(_customThemesKey) ?? [];

      return customThemesJson
          .map((json) => ThemeExport.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des thèmes personnalisés: $e');
      return [];
    }
  }

  /// Supprime un thème personnalisé
  Future<void> deleteCustomTheme(String themeName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customThemesJson = prefs.getStringList(_customThemesKey) ?? [];

      final updatedThemes =
          customThemesJson.where((json) {
            final theme = ThemeExport.fromJson(json);
            return theme.name != themeName;
          }).toList();

      await prefs.setStringList(_customThemesKey, updatedThemes);
    } catch (e) {
      debugPrint('Erreur lors de la suppression du thème personnalisé: $e');
      throw Exception('Impossible de supprimer le thème personnalisé: $e');
    }
  }
}
