import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ai/ai_message.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/conversation.dart';
import '../../models/customer/customer.dart';
import '../ai/knowledge_ai_service.dart';
import '../messaging/messaging_service.dart';
import '../customer/customer_service.dart';

// Provider pour le service de réponses automatiques basé sur la base de connaissances
final autoResponseKnowledgeServiceProvider =
    Provider<AutoResponseKnowledgeService>((ref) {
      final knowledgeAIService = ref.watch(knowledgeAIServiceProvider);
      final messagingService = ref.watch(messagingServiceProvider);
      final customerService = ref.watch(customerServiceProvider);

      return AutoResponseKnowledgeService(
        knowledgeAIService: knowledgeAIService,
        messagingService: messagingService,
        customerService: customerService,
      );
    });

class AutoResponseKnowledgeService {
  final KnowledgeAIService knowledgeAIService;
  final MessagingService messagingService;
  final CustomerService customerService;

  // Paramètres de configuration
  bool _autoResponseEnabled = true;
  bool _followUpEnabled = true;
  int _followUpDelayHours = 24;

  AutoResponseKnowledgeService({
    required this.knowledgeAIService,
    required this.messagingService,
    required this.customerService,
  });

  // Activer/désactiver les réponses automatiques
  void setAutoResponseEnabled(bool enabled) {
    _autoResponseEnabled = enabled;
  }

  // Activer/désactiver la création automatique de leads
  void setLeadCreationEnabled(bool enabled) {
    // Fonctionnalité désactivée pour le moment
  }

  // Activer/désactiver les relances automatiques
  void setFollowUpEnabled(bool enabled) {
    _followUpEnabled = enabled;
  }

  // Définir le délai de relance (en heures)
  void setFollowUpDelay(int hours) {
    _followUpDelayHours = hours;
  }

  // Traiter un nouveau message entrant
  Future<void> processNewMessage(Message message) async {
    if (!_autoResponseEnabled) return;

    try {
      // Récupérer la conversation
      final conversation = await messagingService.getConversationById(
        message.conversationId,
      );
      if (conversation == null) {
        debugPrint('Conversation non trouvée: ${message.conversationId}');
        return;
      }

      // Vérifier si le message provient d'un client (pas de l'IA ou d'un agent)
      if (message.sender == 'ai' || message.sender == 'agent') {
        return;
      }

      // Générer une réponse automatique
      final responseText = await knowledgeAIService.aiService.generateText([
        AIMessage(role: AIMessageRole.user, content: message.content),
      ]);

      // Créer un message de réponse (non utilisé directement)
      // Message(
      //   id: DateTime.now().millisecondsSinceEpoch.toString(),
      //   contactId: message.contactId,
      //   content: responseText,
      //   isUser: false,
      //   timestamp: DateTime.now(),
      //   channel: message.channel,
      //   status: MessageStatus.sent,
      //   isAIGenerated: true,
      // );

      // Envoyer la réponse
      await messagingService.sendMessage(
        contactId: message.contactId,
        content: responseText,
        channel: message.channel,
      );

      // Mettre à jour la conversation
      await messagingService.updateConversation(
        conversation.copyWith(
          lastMessage: responseText,
          lastMessageTime: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('Erreur lors du traitement du message: $e');
    }
  }

  // Planifier des relances pour les conversations inactives
  Future<void> scheduleFollowUps() async {
    if (!_followUpEnabled) return;

    try {
      // Récupérer toutes les conversations
      final conversations = await messagingService.getConversations();

      // Date limite pour les relances (maintenant - délai de relance)
      final followUpThreshold = DateTime.now().subtract(
        Duration(hours: _followUpDelayHours),
      );

      for (final conversation in conversations) {
        // Vérifier si la conversation est inactive depuis le délai défini
        if (conversation.lastMessageTime.isBefore(followUpThreshold) &&
            !conversation.isFollowedUp) {
          // Récupérer le client associé à cette conversation
          final customer = await customerService.getCustomerByContactId(
            conversation.participantId,
          );

          if (customer != null) {
            // Générer un message de relance
            final followUpMessage = await _generateFollowUpMessage(
              customer,
              conversation,
            );

            // Envoyer le message de relance
            await messagingService.sendMessage(
              contactId: conversation.participantId,
              content: followUpMessage,
              channel: conversation.channel,
            );

            // Marquer la conversation comme ayant été relancée
            final updatedConversation = conversation.copyWith(
              isFollowedUp: true,
              lastMessage: followUpMessage,
              lastMessageTime: DateTime.now(),
            );

            await messagingService.updateConversation(updatedConversation);
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la planification des relances: $e');
    }
  }

  // Générer un message de relance personnalisé
  Future<String> _generateFollowUpMessage(
    Customer customer,
    Conversation conversation,
  ) async {
    // Déterminer le type de relance en fonction du statut du client et de l'historique
    String followUpType = 'general';

    if (customer.status == CustomerStatus.lead) {
      if (customer.leadQualification == LeadQualification.hot) {
        followUpType = 'hot_lead';
      } else if (customer.leadQualification == LeadQualification.warm) {
        followUpType = 'warm_lead';
      } else {
        followUpType = 'cold_lead';
      }
    } else if (customer.status == CustomerStatus.customer) {
      followUpType = 'existing_customer';
    } else if (customer.status == CustomerStatus.vip) {
      followUpType = 'vip_customer';
    }

    // Récupérer les derniers messages de la conversation
    final messages = await messagingService.getMessagesByConversationId(
      conversation.id,
    );
    final lastClientMessage =
        messages.where((m) => m.sender == customer.contactId).toList();

    // Construire le prompt pour l'IA
    final prompt = '''
    Tu es un assistant commercial pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

    Informations sur le client:
    - Nom: ${customer.name}
    - Statut: ${customer.status.name}
    - Type de lead: ${customer.leadQualification?.name ?? 'non défini'}
    - Dernier contact: il y a ${DateTime.now().difference(customer.lastContact).inDays} jours

    Historique récent:
    ${lastClientMessage.isNotEmpty ? 'Dernier message du client: "${lastClientMessage.last.content}"' : 'Pas de message récent'}

    Génère un message de relance personnalisé pour ce client. Le type de relance est: $followUpType.
    Le message doit être amical, professionnel et inciter à la reprise de conversation.
    Ne mentionne pas explicitement que c'est une relance automatique.
    ''';

    // Générer le message avec l'IA
    final response = await knowledgeAIService.aiService.generateText([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    return response;
  }

  // Qualifier automatiquement un lead en fonction des interactions
  Future<void> autoQualifyLead(String customerId) async {
    try {
      // Récupérer le client
      final customer = await customerService.getCustomerById(customerId);
      if (customer == null || customer.status != CustomerStatus.lead) {
        return;
      }

      // Récupérer les conversations associées à ce client
      final conversations = await messagingService
          .getConversationsByParticipantId(customer.contactId ?? '');
      if (conversations.isEmpty) {
        return;
      }

      // Récupérer tous les messages de ces conversations
      List<Message> allMessages = [];
      for (final conversation in conversations) {
        final messages = await messagingService.getMessagesByConversationId(
          conversation.id,
        );
        allMessages.addAll(messages);
      }

      // Analyser les messages pour déterminer l'intérêt du client
      int interestScore = 0;

      // Nombre de messages envoyés par le client
      final clientMessages =
          allMessages.where((m) => m.sender == customer.contactId).length;
      interestScore += clientMessages;

      // Fréquence des interactions
      if (conversations.isNotEmpty) {
        final firstConversation = conversations.first;
        final daysSinceFirstContact =
            DateTime.now().difference(firstConversation.createdAt).inDays;

        if (daysSinceFirstContact > 0) {
          final messagesPerDay = clientMessages / daysSinceFirstContact;
          if (messagesPerDay > 2) {
            interestScore += 3;
          } else if (messagesPerDay > 1) {
            interestScore += 2;
          } else if (messagesPerDay > 0.5) {
            interestScore += 1;
          }
        }
      }

      // Déterminer la qualification en fonction du score
      LeadQualification qualification;
      if (interestScore > 10) {
        qualification = LeadQualification.hot;
      } else if (interestScore > 5) {
        qualification = LeadQualification.warm;
      } else {
        qualification = LeadQualification.cold;
      }

      // Mettre à jour la qualification du lead
      if (customer.leadQualification != qualification) {
        await customerService.qualifyLead(customerId, qualification);
      }
    } catch (e) {
      debugPrint('Erreur lors de la qualification automatique du lead: $e');
    }
  }
}
