import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/auth_service.dart';
import '../../widgets/user_avatar.dart';
import '../../theme/neon_theme.dart';

class AvatarDebugScreen extends ConsumerStatefulWidget {
  const AvatarDebugScreen({super.key});

  @override
  ConsumerState<AvatarDebugScreen> createState() => _AvatarDebugScreenState();
}

class _AvatarDebugScreenState extends ConsumerState<AvatarDebugScreen> {
  String _debugInfo = '';

  @override
  void initState() {
    super.initState();
    _updateDebugInfo();
  }

  void _updateDebugInfo() {
    final user = ref.read(currentUserProvider);
    final authService = ref.read(authServiceProvider);

    setState(() {
      _debugInfo = '''
=== INFORMATIONS DE DÉBOGAGE AVATAR ===

Utilisateur actuel:
- ID: ${user?.id ?? 'null'}
- Nom: ${user?.name ?? 'null'}
- Avatar URL: ${user?.avatar ?? 'null'}

Service d'authentification:
- Utilisateur connecté: ${authService.isLoggedIn}
- Utilisateur actuel: ${authService.currentUser?.name ?? 'null'}
- Avatar du service: ${authService.currentUser?.avatar ?? 'null'}

Validation de l'avatar:
- URL valide: ${_isValidUrl(user?.avatar)}
- Fichier existe: ${_fileExists(user?.avatar)}
- Type d'URL: ${_getUrlType(user?.avatar)}

Cache d'images:
- Taille du cache: ${PaintingBinding.instance.imageCache.currentSize}
- Limite du cache: ${PaintingBinding.instance.imageCache.maximumSize}
''';
    });
  }

  bool _isValidUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') ||
        url.startsWith('https://') ||
        File(url).existsSync();
  }

  bool _fileExists(String? path) {
    if (path == null || path.isEmpty) return false;
    if (path.startsWith('http')) return true; // URL réseau
    return File(path).existsSync();
  }

  String _getUrlType(String? url) {
    if (url == null || url.isEmpty) return 'Aucune';
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return 'URL réseau';
    }
    return 'Fichier local';
  }

  void _clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    setState(() {
      _debugInfo += '\n\n[${DateTime.now()}] Cache d\'images vidé';
    });
  }

  void _forceRefreshUser() {
    ref.read(currentUserProvider.notifier).forceUpdate();
    _updateDebugInfo();
    setState(() {
      _debugInfo += '\n\n[${DateTime.now()}] Utilisateur forcé à se rafraîchir';
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Débogage Avatar'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _updateDebugInfo,
            tooltip: 'Actualiser les infos',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.black, Colors.grey[900]!],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Affichage de l'avatar actuel
              Center(
                child: Column(
                  children: [
                    const Text(
                      'Avatar actuel:',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      decoration: NeonTheme.avatarDecoration(
                        NeonTheme.neonCyan,
                      ),
                      child: UserAvatar(
                        imageUrl: user?.avatar,
                        name: user?.name ?? 'User',
                        size: 120,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'URL: ${user?.avatar ?? 'Aucune'}',
                      style: const TextStyle(color: Colors.white),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Boutons d'action
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _clearImageCache,
                    child: const Text('Vider Cache'),
                  ),
                  ElevatedButton(
                    onPressed: _forceRefreshUser,
                    child: const Text('Rafraîchir User'),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // Informations de débogage
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: NeonTheme.neonCyan),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Informations de débogage:',
                      style: TextStyle(
                        color: NeonTheme.neonCyan,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _debugInfo,
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
