import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../models/task/task.dart';
import '../../providers/theme_provider.dart';
import '../../services/task/task_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

// Provider pour une tâche spécifique
final taskDetailProvider = Provider.family<Task?, String>((ref, id) {
  final tasks = ref.watch(tasksProvider);
  try {
    return tasks.firstWhere((task) => task.id == id);
  } catch (e) {
    return null;
  }
});

class TaskDetailScreen extends ConsumerWidget {
  final String taskId;

  const TaskDetailScreen({super.key, required this.taskId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final task = ref.watch(taskDetailProvider(taskId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails de la tâche'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: ref.watch(primaryTextColorProvider),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              context.push('/tasks/edit/$taskId');
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            task == null
                ? Center(
                  child: Text(
                    'Tâche non trouvée',
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                      fontSize: 18,
                    ),
                  ),
                )
                : _buildTaskDetails(context, task, ref),
      ),
    );
  }

  Widget _buildTaskDetails(BuildContext context, Task task, WidgetRef ref) {
    final Color priorityColor = _getPriorityColor(task.priority);
    final bool isOverdue = task.isOverdue;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // En-tête avec titre et statut
          NeonCard(
            color: isOverdue ? Colors.red : priorityColor,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Indicateur de statut
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: _getStatusColor(task.status),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Statut textuel
                      Text(
                        _getStatusText(task.status),
                        style: TextStyle(
                          color: _getStatusColor(task.status),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      // Priorité
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: priorityColor.withAlpha(50),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: priorityColor),
                        ),
                        child: Text(
                          _getPriorityText(task.priority),
                          style: TextStyle(
                            color: priorityColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Titre de la tâche
                  Text(
                    task.title,
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Date d'échéance
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color:
                            isOverdue
                                ? NeonTheme.neonRed
                                : ref.watch(primaryTextColorProvider),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Échéance: ${DateFormat('dd/MM/yyyy').format(task.dueDate)}',
                        style: TextStyle(
                          color:
                              isOverdue
                                  ? NeonTheme.neonRed
                                  : ref.watch(primaryTextColorProvider),
                          fontWeight:
                              isOverdue ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      if (isOverdue)
                        Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: NeonTheme.neonRed.withAlpha(50),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'En retard de ${task.daysOverdue} jour${task.daysOverdue > 1 ? 's' : ''}',
                            style: const TextStyle(
                              color: NeonTheme.neonRed,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Description
          NeonCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Description',
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    task.description.isNotEmpty
                        ? task.description
                        : 'Aucune description',
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Informations supplémentaires
          NeonCard(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Informations',
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Assigné à
                  if (task.assignedToName != null)
                    _buildInfoRow(
                      Icons.person,
                      'Assigné à',
                      task.assignedToName!,
                    ),
                  // Date de création
                  _buildInfoRow(
                    Icons.access_time,
                    'Créée le',
                    DateFormat('dd/MM/yyyy').format(task.createdAt),
                  ),
                  // Tags
                  if (task.tags.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Icon(
                            Icons.tag,
                            color: NeonTheme.neonCyan,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Tags',
                                  style: TextStyle(
                                    color: ref.watch(
                                      secondaryTextColorProvider,
                                    ),
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children:
                                      task.tags.map((tag) {
                                        return Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Theme.of(
                                              context,
                                            ).colorScheme.surface.withAlpha(77),
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                            border: Border.all(
                                              color: NeonTheme.neonCyan
                                                  .withAlpha(77),
                                            ),
                                          ),
                                          child: Text(
                                            '#$tag',
                                            style: const TextStyle(
                                              color: NeonTheme.neonCyan,
                                              fontSize: 12,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Boutons d'action
          _buildActionButtons(context, task, ref),
        ],
      ),
    );
  }
}

Widget _buildInfoRow(IconData icon, String label, String value) {
  return Consumer(
    builder: (context, ref, child) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: NeonTheme.neonCyan, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      color: ref.watch(secondaryTextColorProvider),
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    value,
                    style: TextStyle(
                      color: ref.watch(primaryTextColorProvider),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Widget _buildActionButtons(BuildContext context, Task task, WidgetRef ref) {
  return Column(
    children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          NeonButton(
            text: 'Changer le statut',
            icon: FontAwesomeIcons.arrowRightArrowLeft,
            color: NeonTheme.neonCyan,
            onPressed: () {
              _showChangeStatusDialog(context, task, ref);
            },
          ),
          const SizedBox(width: 16),
          NeonButton(
            text: 'Modifier',
            icon: FontAwesomeIcons.penToSquare,
            color: NeonTheme.neonOrange,
            onPressed: () {
              context.push('/tasks/edit/${task.id}');
            },
          ),
        ],
      ),
      const SizedBox(height: 16),
      NeonButton(
        text: 'Supprimer la tâche',
        icon: FontAwesomeIcons.trash,
        color: NeonTheme.neonRed,
        onPressed: () {
          _showDeleteDialog(context, task, ref);
        },
      ),
    ],
  );
}

void _showChangeStatusDialog(BuildContext context, Task task, WidgetRef ref) {
  showDialog(
    context: context,
    builder: (context) {
      return Consumer(
        builder: (context, ref, child) {
          return AlertDialog(
            backgroundColor: Theme.of(context).colorScheme.surface,
            title: Text(
              'Changer le statut',
              style: TextStyle(color: ref.watch(primaryTextColorProvider)),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusOption(
                  context,
                  'À faire',
                  TaskStatus.todo,
                  task,
                  ref,
                ),
                _buildStatusOption(
                  context,
                  'En cours',
                  TaskStatus.inProgress,
                  task,
                  ref,
                ),
                _buildStatusOption(
                  context,
                  'Terminée',
                  TaskStatus.completed,
                  task,
                  ref,
                ),
                _buildStatusOption(
                  context,
                  'Annulée',
                  TaskStatus.cancelled,
                  task,
                  ref,
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

Widget _buildStatusOption(
  BuildContext context,
  String label,
  TaskStatus status,
  Task task,
  WidgetRef ref,
) {
  return Consumer(
    builder: (context, ref, child) {
      return ListTile(
        leading: Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: _getStatusColor(status),
            shape: BoxShape.circle,
          ),
        ),
        title: Text(
          label,
          style: TextStyle(color: ref.watch(primaryTextColorProvider)),
        ),
        selected: task.status == status,
        selectedTileColor: ref.watch(primaryTextColorProvider).withAlpha(26),
        onTap: () {
          ref.read(tasksProvider.notifier).updateTaskStatus(task.id, status);
          Navigator.of(context).pop();
        },
      );
    },
  );
}

void _showDeleteDialog(BuildContext context, Task task, WidgetRef ref) {
  showDialog(
    context: context,
    builder: (context) {
      return Consumer(
        builder: (context, ref, child) {
          return AlertDialog(
            backgroundColor: Theme.of(context).colorScheme.surface,
            title: Text(
              'Supprimer la tâche',
              style: TextStyle(color: ref.watch(primaryTextColorProvider)),
            ),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la tâche "${task.title}" ?',
              style: TextStyle(color: ref.watch(primaryTextColorProvider)),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: ref.watch(primaryTextColorProvider)),
                ),
              ),
              TextButton(
                onPressed: () {
                  ref.read(tasksProvider.notifier).deleteTask(task.id);
                  Navigator.of(context).pop();
                  context.pop(); // Retourner à l'écran précédent
                },
                child: const Text(
                  'Supprimer',
                  style: TextStyle(color: NeonTheme.neonRed),
                ),
              ),
            ],
          );
        },
      );
    },
  );
}

Color _getPriorityColor(TaskPriority priority) {
  switch (priority) {
    case TaskPriority.high:
      return NeonTheme.neonRed;
    case TaskPriority.medium:
      return NeonTheme.neonOrange;
    case TaskPriority.low:
      return NeonTheme.neonGreen;
  }
}

String _getPriorityText(TaskPriority priority) {
  switch (priority) {
    case TaskPriority.high:
      return 'Priorité haute';
    case TaskPriority.medium:
      return 'Priorité moyenne';
    case TaskPriority.low:
      return 'Priorité basse';
  }
}

Color _getStatusColor(TaskStatus status) {
  switch (status) {
    case TaskStatus.todo:
      return NeonTheme.neonGrey;
    case TaskStatus.inProgress:
      return NeonTheme.neonBlue;
    case TaskStatus.completed:
      return NeonTheme.neonGreen;
    case TaskStatus.cancelled:
      return NeonTheme.neonRed;
  }
}

String _getStatusText(TaskStatus status) {
  switch (status) {
    case TaskStatus.todo:
      return 'À faire';
    case TaskStatus.inProgress:
      return 'En cours';
    case TaskStatus.completed:
      return 'Terminée';
    case TaskStatus.cancelled:
      return 'Annulée';
  }
}
