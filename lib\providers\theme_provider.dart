import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/theme/theme_export.dart';
import '../theme/neon_theme.dart';
import '../theme/tz1_theme.dart';
import '../theme/tz2_theme.dart';
import '../theme/creamy_beans_theme.dart';
import '../theme/orix_purple_split_theme.dart';

// Constantes pour les nouveaux thèmes afin de simplifier l'accès dans l'extension
final ThemeData _creamyBeansThemeData = CreamyBeansTheme.themeData;
const Color _creamyBeansPrimaryColor =
    creamyButtonPrimaryBackground; // Mise à jour pour utiliser la nouvelle constante
const Color _creamyBeansSecondaryColor =
    creamyHighlightSecondary; // Mise à jour pour utiliser la nouvelle constante
final LinearGradient _creamyBeansMainGradient =
    CreamyBeansTheme
        .mainGradient; // 'const' supprimé car CreamyBeansTheme.mainGradient n'est plus const
final LinearGradient _creamyBeansSidebarGradient =
    CreamyBeansTheme
        .sidebarGradient; // 'const' supprimé car CreamyBeansTheme.sidebarGradient n'est plus const
final LinearGradient _creamyBeansCardGradient =
    CreamyBeansTheme
        .mainGradient; // Utiliser le même gradient que mainGradient pour les cartes

final ThemeData _orixPurpleSplitThemeData = OrixPurpleSplitTheme.themeData;
const Color _orixPurpleSplitPrimaryColor = orixAccentPrincipal; // Mise à jour
const Color _orixPurpleSplitSecondaryColor =
    orixAccentSecondaire; // Mise à jour
const LinearGradient _orixPurpleSplitMainGradient =
    OrixPurpleSplitTheme
        .mainGradient; // 'const' supprimé car la source n'est plus const
const LinearGradient _orixPurpleSplitSidebarGradient =
    OrixPurpleSplitTheme
        .sidebarGradient; // 'const' supprimé car la source n'est plus const
const LinearGradient _orixPurpleSplitCardGradient =
    OrixPurpleSplitTheme
        .mainGradient; // Utiliser le même gradient que mainGradient pour les cartes

// Enum pour les différents thèmes disponibles
enum AppThemeMode {
  unique, // Thème unique pour toute l'application
  tz1, // Thème TZ-1 (clair avec palette de couleurs spécifiques)
  tz2, // Thème TZ-2 (lavande avec dégradés)
  creamyBeans, // Thème Creamy Beans (Coffee shop)
  orixPurpleSplit, // Thème Orix Purple Split (Bill splitter)
  custom, // Thème personnalisé importé
}

// Extension pour obtenir le nom d'affichage du thème
extension AppThemeModeExtension on AppThemeMode {
  String get displayName {
    switch (this) {
      case AppThemeMode.unique:
        return 'Thème Unique';
      case AppThemeMode.tz1:
        return 'Thème TZ-1';
      case AppThemeMode.tz2:
        return 'Thème TZ-2';
      case AppThemeMode.creamyBeans:
        return 'Creamy Beans';
      case AppThemeMode.orixPurpleSplit:
        return 'Orix Purple Split';
      case AppThemeMode.custom:
        return 'Thème Personnalisé';
    }
  }

  ThemeData get themeData {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.themeData;
      case AppThemeMode.tz1:
        return TZ1Theme.themeData;
      case AppThemeMode.tz2:
        return TZ2Theme.themeData;
      case AppThemeMode.creamyBeans:
        return _creamyBeansThemeData;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitThemeData;
      case AppThemeMode.custom:
        // Pour le thème personnalisé, nous devons obtenir le ThemeNotifier
        // Cela sera géré par le provider currentThemeProvider
        return NeonTheme.themeData; // Valeur par défaut, sera remplacée
    }
  }

  Color get primaryColor {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.primaryAccent;
      case AppThemeMode.tz1:
        return TZ1Theme.lavenderActive;
      case AppThemeMode.tz2:
        return TZ2Theme.cloudyViolet;
      case AppThemeMode.creamyBeans:
        return _creamyBeansPrimaryColor;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitPrimaryColor;
      case AppThemeMode.custom:
        // Sera géré par le provider primaryColorProvider
        return NeonTheme.primaryAccent; // Valeur par défaut
    }
  }

  Color get secondaryColor {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.secondaryAccent;
      case AppThemeMode.tz1:
        return TZ1Theme.pinkReview;
      case AppThemeMode.tz2:
        return TZ2Theme.radiantPink;
      case AppThemeMode.creamyBeans:
        return _creamyBeansSecondaryColor;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitSecondaryColor;
      case AppThemeMode.custom:
        // Sera géré par le provider secondaryColorProvider
        return NeonTheme.secondaryAccent; // Valeur par défaut
    }
  }

  LinearGradient get mainGradient {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.mainGradient;
      case AppThemeMode.tz1:
        return TZ1Theme.mainGradient;
      case AppThemeMode.tz2:
        return TZ2Theme.zomoGradient;
      case AppThemeMode.creamyBeans:
        return _creamyBeansMainGradient;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitMainGradient;
      case AppThemeMode.custom:
        // Sera géré par le provider
        return NeonTheme.mainGradient; // Valeur par défaut
    }
  }

  LinearGradient get sidebarGradient {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.sidebarGradient;
      case AppThemeMode.tz1:
        return TZ1Theme.sidebarGradient;
      case AppThemeMode.tz2:
        return TZ2Theme.airyLavenderBackground;
      case AppThemeMode.creamyBeans:
        return _creamyBeansSidebarGradient;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitSidebarGradient;
      case AppThemeMode.custom:
        // Sera géré par le provider
        return NeonTheme.sidebarGradient; // Valeur par défaut
    }
  }

  LinearGradient get cardGradient {
    switch (this) {
      case AppThemeMode.unique:
        return NeonTheme.cardGradient;
      case AppThemeMode.tz1:
        return TZ1Theme.mainGradient; // Utiliser le gradient principal pour TZ1
      case AppThemeMode.tz2:
        return TZ2Theme.zomoGradient; // Utiliser le gradient principal pour TZ2
      case AppThemeMode.creamyBeans:
        return _creamyBeansCardGradient;
      case AppThemeMode.orixPurpleSplit:
        return _orixPurpleSplitCardGradient;
      case AppThemeMode.custom:
        // Sera géré par le provider
        return NeonTheme.cardGradient; // Valeur par défaut
    }
  }

  bool get isDark {
    switch (this) {
      case AppThemeMode.unique:
        return true;
      case AppThemeMode.tz1:
        return false;
      case AppThemeMode.tz2:
        return false;
      case AppThemeMode.creamyBeans:
        return false; // Creamy Beans est un thème clair
      case AppThemeMode.orixPurpleSplit:
        return true; // Orix Purple Split est un thème sombre
      case AppThemeMode.custom:
        // Sera géré par le provider
        return true; // Valeur par défaut
    }
  }

  // Couleur de texte principale adaptée au thème
  Color get primaryTextColor {
    switch (this) {
      case AppThemeMode.unique:
        return Colors.white;
      case AppThemeMode.tz1:
        return const Color(0xFF000000); // TZ1Theme.primaryText
      case AppThemeMode.tz2:
        return const Color(0xFF2D2D2D); // TZ2Theme.primaryTextDark
      case AppThemeMode.creamyBeans:
        return const Color(0xFF362015); // creamyTextPrimary
      case AppThemeMode.orixPurpleSplit:
        return const Color(0xFFE8E6F0); // textPrincipalOrix
      case AppThemeMode.custom:
        return Colors.white; // Valeur par défaut
    }
  }

  // Couleur de texte secondaire adaptée au thème
  Color get secondaryTextColor {
    switch (this) {
      case AppThemeMode.unique:
        return Colors.white.withValues(alpha: 179); // 0.7 * 255
      case AppThemeMode.tz1:
        return const Color(0xFF000000).withValues(alpha: 179);
      case AppThemeMode.tz2:
        return const Color(0xFF2D2D2D).withValues(alpha: 179);
      case AppThemeMode.creamyBeans:
        return const Color(0xFF6B4B38); // creamyTextSecondary
      case AppThemeMode.orixPurpleSplit:
        return const Color(0xFFDAD9DE); // textSecondaireOrix
      case AppThemeMode.custom:
        return Colors.white.withValues(alpha: 179); // Valeur par défaut
    }
  }

  // Couleur de surface adaptée au thème
  Color get surfaceColor {
    switch (this) {
      case AppThemeMode.unique:
        return const Color(0xFF2A2D3E); // NeonTheme.darkSurface
      case AppThemeMode.tz1:
        return const Color(0xFFFFFFFF); // TZ1Theme.pureWhite
      case AppThemeMode.tz2:
        return const Color(0xFFF2E9FF); // TZ2Theme.airyLavenderStart
      case AppThemeMode.creamyBeans:
        return const Color(0xFFF5E8D4); // laitMousseux
      case AppThemeMode.orixPurpleSplit:
        return const Color(0xFF3C2E59); // orixCartes
      case AppThemeMode.custom:
        return const Color(0xFF2A2D3E); // Valeur par défaut
    }
  }
}

// Notifier pour gérer le thème de l'application
class ThemeNotifier extends StateNotifier<AppThemeMode> {
  ThemeNotifier() : super(AppThemeMode.unique) {
    _loadTheme();
  }

  // Thème personnalisé actuel
  ThemeExport? _customTheme;

  // Getter pour le thème personnalisé
  ThemeExport? get customTheme => _customTheme;

  Future<void> _loadTheme() async {
    try {
      // Charger le thème depuis les préférences
      final prefs = await SharedPreferences.getInstance();
      final themeName = prefs.getString('app_theme');
      final customThemeJson = prefs.getString('current_custom_theme');

      if (themeName != null) {
        switch (themeName) {
          case 'unique':
            state = AppThemeMode.unique;
            break;
          case 'tz1':
            state = AppThemeMode.tz1;
            break;
          case 'tz2':
            state = AppThemeMode.tz2;
            break;
          case 'creamyBeans':
            state = AppThemeMode.creamyBeans;
            break;
          case 'orixPurpleSplit':
            state = AppThemeMode.orixPurpleSplit;
            break;
          case 'custom':
            if (customThemeJson != null) {
              _customTheme = ThemeExport.fromJson(customThemeJson);
              state = AppThemeMode.custom;
            } else {
              state = AppThemeMode.unique;
            }
            break;
          default:
            state = AppThemeMode.unique;
        }
      } else {
        // Par défaut, utiliser le thème unique
        state = AppThemeMode.unique;
      }

      debugPrint('Thème chargé: ${state.displayName}');
    } catch (e) {
      // En cas d'erreur, on garde le thème par défaut
      debugPrint('Erreur lors du chargement du thème: $e');
    }
  }

  Future<void> setTheme(AppThemeMode themeMode) async {
    try {
      // Mettre à jour le thème
      state = themeMode;

      // Sauvegarder le thème dans les préférences
      final prefs = await SharedPreferences.getInstance();
      String themeName;

      switch (themeMode) {
        case AppThemeMode.unique:
          themeName = 'unique';
          break;
        case AppThemeMode.tz1:
          themeName = 'tz1';
          break;
        case AppThemeMode.tz2:
          themeName = 'tz2';
          break;
        case AppThemeMode.creamyBeans:
          themeName = 'creamyBeans';
          break;
        case AppThemeMode.orixPurpleSplit:
          themeName = 'orixPurpleSplit';
          break;
        case AppThemeMode.custom:
          themeName = 'custom';
          break;
      }

      await prefs.setString('app_theme', themeName);
      debugPrint('Thème sauvegardé: ${themeMode.displayName}');
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde du thème: $e');
    }
  }

  /// Définit un thème personnalisé
  Future<void> setCustomTheme(ThemeExport theme) async {
    try {
      // Sauvegarder le thème personnalisé
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_custom_theme', theme.toJson());

      // Mettre à jour le thème personnalisé actuel
      _customTheme = theme;

      // Définir le mode de thème sur personnalisé
      await setTheme(AppThemeMode.custom);

      debugPrint('Thème personnalisé défini: ${theme.name}');
    } catch (e) {
      debugPrint('Erreur lors de la définition du thème personnalisé: $e');
    }
  }

  // Pour la compatibilité avec l'ancienne version
  Future<void> toggleTheme() async {
    // Passer au thème suivant
    AppThemeMode nextTheme;
    switch (state) {
      case AppThemeMode.unique:
        nextTheme = AppThemeMode.tz1;
        break;
      case AppThemeMode.tz1:
        nextTheme = AppThemeMode.tz2;
        break;
      case AppThemeMode.tz2:
        nextTheme = AppThemeMode.creamyBeans;
        break;
      case AppThemeMode.creamyBeans:
        nextTheme = AppThemeMode.orixPurpleSplit;
        break;
      case AppThemeMode.orixPurpleSplit:
        // Si des thèmes personnalisés sont disponibles, passer au premier thème personnalisé
        final customThemes = await _getCustomThemes();
        if (customThemes.isNotEmpty) {
          await setCustomTheme(customThemes.first);
          return;
        }
        nextTheme = AppThemeMode.unique;
        break;
      case AppThemeMode.custom:
        nextTheme = AppThemeMode.unique;
        break;
    }

    // Utiliser setTheme pour sauvegarder le thème
    await setTheme(nextTheme);
  }

  // Récupère les thèmes personnalisés
  Future<List<ThemeExport>> _getCustomThemes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customThemesJson = prefs.getStringList('custom_themes') ?? [];

      return customThemesJson
          .map((json) => ThemeExport.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des thèmes personnalisés: $e');
      return [];
    }
  }
}

// Provider pour le thème de l'application
final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeMode>((ref) {
  return ThemeNotifier();
});

// Provider pour le thème actuel
final currentThemeProvider = Provider<ThemeData>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.toThemeData();
  }

  return themeMode.themeData;
});

// Provider pour savoir si le thème actuel est sombre
final isDarkThemeProvider = Provider<bool>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.isDark;
  }

  return themeMode.isDark;
});

// Provider pour la couleur primaire du thème actuel
final primaryColorProvider = Provider<Color>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.colors.primary;
  }

  return themeMode.primaryColor;
});

// Provider pour la couleur de texte primaire du thème actuel
final primaryTextColorProvider = Provider<Color>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.colors.primary;
  }

  return themeMode.primaryTextColor;
});

// Provider pour la couleur de texte secondaire du thème actuel
final secondaryTextColorProvider = Provider<Color>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.colors.secondary;
  }

  return themeMode.secondaryTextColor;
});

// Provider pour la couleur secondaire du thème actuel
final secondaryColorProvider = Provider<Color>((ref) {
  final themeMode = ref.watch(themeProvider);
  final themeNotifier = ref.read(themeProvider.notifier);

  if (themeMode == AppThemeMode.custom && themeNotifier.customTheme != null) {
    return themeNotifier.customTheme!.colors.secondary;
  }

  return themeMode.secondaryColor;
});

// Ce provider est maintenant défini dans theme_export_service.dart
// final themeExportServiceProvider = Provider<ThemeExportService>((ref) {
//   return ThemeExportService();
// });

// Provider pour le dégradé principal du thème actuel
final mainGradientProvider = Provider<LinearGradient>((ref) {
  final themeMode = ref.watch(themeProvider);
  return themeMode.mainGradient;
});

// Provider pour le dégradé de la barre latérale du thème actuel
final sidebarGradientProvider = Provider<LinearGradient>((ref) {
  final themeMode = ref.watch(themeProvider);
  return themeMode.sidebarGradient;
});

// Provider pour le dégradé des cartes du thème actuel
final cardGradientProvider = Provider<LinearGradient>((ref) {
  final themeMode = ref.watch(themeProvider);
  return themeMode.cardGradient;
});
