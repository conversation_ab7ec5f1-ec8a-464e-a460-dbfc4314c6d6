// lib/theme/tz1_theme.dart
import 'package:flutter/material.dart';

class TZ1Theme {
  // Palette principale (Application de gestion de projet)
  static const Color lavenderActive = Color(0xFFA197FF); // In Progress
  static const Color pinkReview = Color(0xFFE7A1E1); // In Review
  static const Color honeyPaused = Color(0xFFF7C66F); // On Hold
  static const Color mintValidated = Color(0xFF82D49C); // Completed

  // Éléments secondaires
  static const Color statisticViolet = Color(
    0xFFA597FF,
  ); // Barres de progression
  static const Color softPink = Color(
    0xFFE29BD4,
  ); // Tâches en révision (statistiques)
  static const Color successGreen = Color(
    0xFF72CA95,
  ); // Tâches terminées (statistiques)

  // Badges et Statuts
  static const Color emergencyRed = Color(0xFFF96B6B); // High (texte blanc)
  static const Color calmGreen = Color(0xFF9DEFA7); // Low (texte noir)
  static const Color alertYellow = Color(0xFFEFC66A); // Medium (texte noir)
  static const Color meetingBlue = Color(0xFFC3D8F7); // Meeting (texte noir)
  static const Color trackingPink = Color(0xFFED9ADC); // On Track (texte noir)
  static const Color darkVioletRisk = Color(
    0xFF4B2E8D,
  ); // At Risk (texte blanc)

  // Couleurs globales
  static const Color pureWhite = Color(0xFFFFFFFF); // Fond
  static const Color primaryText = Color(0xFF000000); // Texte principal
  static const Color navigationIcons = Color(0xFF000000); // Navigation & icônes
  static final Color borderGrey = Colors.grey.shade300; // Bordures

  // Dégradés (Adaptés pour un thème clair et épuré)
  static const LinearGradient mainGradient = LinearGradient(
    colors: [
      pureWhite,
      Color(0xFFF5F5F5),
    ], // Dégradé subtil de blanc à gris très clair
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient sidebarGradient = LinearGradient(
    colors: [
      Color(0xFFF0F0F0),
      Color(0xFFE8E8E8),
    ], // Gris clair pour la sidebar
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // Ombres (Subtiles pour un look épuré)
  static final BoxShadow primaryShadow = BoxShadow(
    color: Colors.grey.withValues(alpha: 38), // 0.15 * 255 ≈ 38
    blurRadius: 6,
    spreadRadius: 1,
    offset: const Offset(0, 2),
  );

  static final BoxShadow secondaryShadow = BoxShadow(
    color: Colors.grey.withValues(alpha: 26), // 0.1 * 255 ≈ 26
    blurRadius: 4,
    spreadRadius: 1,
    offset: const Offset(0, 1),
  );

  // Thème principal
  static final ThemeData themeData = ThemeData(
    brightness: Brightness.light,
    primaryColor: lavenderActive, // Couleur primaire principale
    scaffoldBackgroundColor: pureWhite, // Fond général de l'application
    colorScheme: const ColorScheme.light(
      primary: lavenderActive,
      secondary:
          pinkReview, // Utilisée pour des accents ou éléments secondaires
      surface: pureWhite, // Fond général
      error: emergencyRed,
      onPrimary: primaryText, // Texte sur la couleur primaire (ex: boutons)
      onSecondary: primaryText, // Texte sur la couleur secondaire
      onSurface: primaryText, // Texte sur le fond général
      onError: pureWhite, // Texte sur la couleur d'erreur
    ),
    textTheme: _textTheme,
    appBarTheme: _appBarTheme,
    elevatedButtonTheme: _elevatedButtonTheme,
    inputDecorationTheme: _inputDecorationTheme,
    cardTheme: _cardTheme,
    iconTheme: const IconThemeData(color: navigationIcons),
    chipTheme: _chipTheme, // Thème pour les Chip (badges)
    textButtonTheme: _textButtonTheme, // Thème pour TextButton
    outlinedButtonTheme: _outlinedButtonTheme, // Thème pour OutlinedButton
  );

  // Styles de texte
  static final TextTheme _textTheme = TextTheme(
    displayLarge: const TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.bold,
      color: primaryText,
    ),
    displayMedium: const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: primaryText,
    ),
    displaySmall: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: primaryText,
    ),
    headlineMedium: const TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: primaryText,
    ),
    headlineSmall: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: primaryText,
    ),
    titleLarge: const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: primaryText,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      color: primaryText.withValues(alpha: 217),
    ), // 0.85 * 255 ≈ 217
    bodyMedium: TextStyle(
      fontSize: 14,
      color: primaryText.withValues(alpha: 191),
    ), // 0.75 * 255 ≈ 191
    labelLarge: const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.bold,
      color: primaryText,
    ), // Texte des boutons, ajuster si fond coloré
    bodySmall: TextStyle(
      fontSize: 12,
      color: primaryText.withValues(alpha: 166),
    ), // 0.65 * 255 ≈ 166
  );

  // Style de l'AppBar
  static final AppBarTheme _appBarTheme = AppBarTheme(
    backgroundColor: pureWhite,
    foregroundColor: primaryText,
    elevation: 0.5, // Légère ombre pour délimiter
    shadowColor: Colors.grey.withValues(alpha: 51), // 0.2 * 255 ≈ 51
    iconTheme: const IconThemeData(color: navigationIcons),
    titleTextStyle: _textTheme.headlineMedium?.copyWith(color: primaryText),
  );

  // Style des ElevatedButton
  static final ElevatedButtonThemeData _elevatedButtonTheme =
      ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          foregroundColor: pureWhite, // Texte blanc sur fond coloré par défaut
          backgroundColor: lavenderActive, // Couleur de fond par défaut
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          textStyle: _textTheme.labelLarge?.copyWith(
            color: pureWhite,
            letterSpacing: 0.5,
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          elevation: 1,
          shadowColor: Colors.grey.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        ),
      );

  // Style des TextButton
  static final TextButtonThemeData _textButtonTheme = TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: lavenderActive, // Couleur du texte
      textStyle: _textTheme.labelLarge?.copyWith(color: lavenderActive),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),
  );

  // Style des OutlinedButton
  static final OutlinedButtonThemeData _outlinedButtonTheme =
      OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: lavenderActive, // Couleur du texte et de la bordure
          side: const BorderSide(color: lavenderActive, width: 1.5),
          textStyle: _textTheme.labelLarge?.copyWith(color: lavenderActive),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );

  // Style des InputDecoration (pour TextField)
  static final InputDecorationTheme _inputDecorationTheme =
      InputDecorationTheme(
        filled: true,
        fillColor: pureWhite, // Fond blanc pour les champs
        hintStyle: _textTheme.bodyMedium?.copyWith(
          color: primaryText.withValues(alpha: 102), // 0.4 * 255 ≈ 102
        ),
        labelStyle: _textTheme.bodyMedium?.copyWith(
          color: lavenderActive,
        ), // Label en lavande
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: lavenderActive, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: emergencyRed, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: emergencyRed, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 14,
        ),
      );

  // Style des Card
  static final CardTheme _cardTheme = CardTheme(
    elevation: 1.5,
    shadowColor: Colors.grey.withValues(alpha: 38), // 0.15 * 255 ≈ 38
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10),
      // side: BorderSide(color: Colors.grey.shade200, width: 1), // Optionnel: bordure légère
    ),
    color: pureWhite, // Fond des cartes
    margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
  );

  // Style des Chip (pour les badges)
  static final ChipThemeData _chipTheme = ChipThemeData(
    backgroundColor: Colors.grey.shade200, // Couleur de fond par défaut
    labelStyle: _textTheme.bodySmall?.copyWith(
      color: primaryText,
      fontWeight: FontWeight.w500,
    ),
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
    elevation: 0,
    pressElevation: 0,
    // Les couleurs spécifiques des badges seront appliquées directement sur le widget Chip
  );

  // Méthodes utilitaires pour les couleurs de statut et styles de boutons
  static Color getStatusCardColor(String status) {
    switch (status.toLowerCase()) {
      case 'in progress':
        return lavenderActive;
      case 'in review':
        return pinkReview;
      case 'on hold':
        return honeyPaused;
      case 'completed':
        return mintValidated;
      default:
        return Colors.grey.shade200;
    }
  }

  static Color getStatusTextColor(Color backgroundColor) {
    // Pour les cartes de statut, le texte est toujours noir selon la palette
    return primaryText;
  }

  static Color getBadgeBackgroundColor(String badgeType) {
    switch (badgeType.toLowerCase()) {
      case 'high':
        return emergencyRed;
      case 'low':
        return calmGreen;
      case 'medium':
        return alertYellow;
      case 'meeting':
        return meetingBlue;
      case 'on track':
        return trackingPink;
      case 'at risk':
        return darkVioletRisk;
      default:
        return Colors.grey.shade300;
    }
  }

  static Color getBadgeTextColor(Color badgeBackgroundColor) {
    if (badgeBackgroundColor == emergencyRed ||
        badgeBackgroundColor == darkVioletRisk) {
      return pureWhite;
    }
    return primaryText;
  }

  // Style de bouton pour les actions principales (ex: 'Completed')
  static ButtonStyle statusActionButtonStyle(Color backgroundColor) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: primaryText, // Texte noir sur fond coloré
      textStyle: _textTheme.labelLarge?.copyWith(
        color: primaryText,
        fontWeight: FontWeight.w600,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 1,
      shadowColor: Colors.grey.withValues(alpha: 51), // 0.2 * 255 ≈ 51
    );
  }
}
