import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/theme_provider.dart';

class NeonCard extends ConsumerWidget {
  final Widget child;
  final Color? color;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final bool isActive;

  const NeonCard({
    super.key,
    required this.child,
    this.color,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final cardColor = color ?? primaryColor;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        margin: margin,
        padding: padding ?? const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isActive
                  ? cardColor.withValues(alpha: 51) // 0.2 * 255 ≈ 51
                  : Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isActive
                    ? cardColor
                    : cardColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
            width: isActive ? 1.5 : 0.5, // Épaisseur de la bordure selon l'état
          ),
          boxShadow:
              isActive
                  ? [
                    BoxShadow(
                      color: cardColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                    BoxShadow(
                      color: cardColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                      blurRadius: 16,
                      spreadRadius: 4,
                    ),
                  ]
                  : [
                    BoxShadow(
                      color: cardColor.withValues(alpha: 38), // 0.15 * 255 ≈ 38
                      blurRadius: 4, // Réduction de l'effet de flou
                      spreadRadius: 0.5, // Réduction de l'étalement
                    ),
                  ],
        ),
        child: child,
      ),
    );
  }
}
