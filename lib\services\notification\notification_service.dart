// Import supprimé car fourni par material.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart'; // Temporairement désactivé pour Windows
// import 'package:timezone/timezone.dart' as tz;
// import 'package:timezone/data/latest.dart' as tz_data;

// Provider pour le service de notifications
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Version simplifiée du service de notification pour Windows
class NotificationService {
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Pas besoin d'initialiser les fuseaux horaires pour cette version simplifiée
    // tz_data.initializeTimeZones();

    _isInitialized = true;
    debugPrint('Notification service initialized (Windows version)');
  }

  // Cette méthode a été supprimée car elle n'est pas utilisée dans la version Windows
  // Elle sera réimplémentée lorsque les notifications locales seront activées

  // Afficher une notification immédiate
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await _ensureInitialized();

    // Version simplifiée pour Windows - juste un log
    debugPrint('NOTIFICATION: [$id] $title - $body');
    if (payload != null) {
      debugPrint('Payload: $payload');
    }
  }

  // Planifier une notification
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    await _ensureInitialized();

    // Version simplifiée pour Windows - juste un log
    final now = DateTime.now();
    final difference = scheduledTime.difference(now);

    debugPrint('SCHEDULED NOTIFICATION: [$id] $title - $body');
    debugPrint(
      'Scheduled for: ${scheduledTime.toString()} (in ${difference.inMinutes} minutes)',
    );
    if (payload != null) {
      debugPrint('Payload: $payload');
    }
  }

  // Annuler une notification
  Future<void> cancelNotification(int id) async {
    await _ensureInitialized();
    debugPrint('Notification cancelled: $id');
  }

  // Annuler toutes les notifications
  Future<void> cancelAllNotifications() async {
    await _ensureInitialized();
    debugPrint('All notifications cancelled');
  }

  // S'assurer que le service est initialisé
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }
}
