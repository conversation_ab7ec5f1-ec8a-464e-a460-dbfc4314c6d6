import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ncrm/router/app_router.dart';
import 'package:ncrm/services/auth_service.dart';

void main() {
  test('Router should be correctly configured', () {
    final container = ProviderContainer();
    final router = container.read(routerProvider);

    // Vérifier que le routeur est correctement configuré
    expect(router, isA<GoRouter>());

    // Vérifier que l'authService est disponible
    final authService = container.read(authServiceProvider);
    expect(authService, isA<AuthService>());
    expect(
      authService.isLoggedIn,
      false,
    ); // Par défaut, l'utilisateur n'est pas connecté

    // Nettoyer
    container.dispose();
  });
}
