import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../services/messaging/messaging_service.dart';
import '../../../theme/neon_theme.dart';

class PlatformSelector extends ConsumerWidget {
  const PlatformSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedPlatform = ref.watch(selectedPlatformProvider);

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withAlpha(26), // 0.1 * 255 ≈ 26
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: _buildPlatformButton(
              context,
              ref,
              'WhatsApp',
              FontAwesomeIcons.whatsapp,
              Colors.green,
              selectedPlatform == 'WhatsApp',
            ),
          ),
          Expanded(
            child: _buildPlatformButton(
              context,
              ref,
              'Meta',
              FontAwesomeIcons.facebookMessenger,
              Colors.blue,
              selectedPlatform == 'Meta',
            ),
          ),
          Expanded(
            child: _buildPlatformButton(
              context,
              ref,
              'Telegram',
              FontAwesomeIcons.telegram,
              const Color(0xFF0088cc),
              selectedPlatform == 'Telegram',
            ),
          ),
          Expanded(
            child: _buildPlatformButton(
              context,
              ref,
              'Tous',
              FontAwesomeIcons.comments,
              NeonTheme.neonCyan,
              selectedPlatform == null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlatformButton(
    BuildContext context,
    WidgetRef ref,
    String platform,
    IconData icon,
    Color color,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () {
        // Si "Tous" est sélectionné, définir selectedPlatform sur null
        if (platform == 'Tous') {
          ref.read(selectedPlatformProvider.notifier).state = null;
        } else {
          ref.read(selectedPlatformProvider.notifier).state = platform;
        }
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(51) : Colors.transparent,
          border: Border(
            bottom: BorderSide(
              color: isSelected ? color : Colors.transparent,
              width: 3,
            ),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.white.withAlpha(179),
              size: 18,
            ),
            const SizedBox(height: 4),
            Text(
              platform,
              style: TextStyle(
                color: isSelected ? color : Colors.white.withAlpha(179),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
