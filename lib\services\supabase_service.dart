import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  late final SupabaseClient _client;
  bool _isInitialized = false;

  // Getter pour vérifier si le service est initialisé
  bool get isInitialized => _isInitialized;

  // Getter pour vérifier si l'utilisateur est connecté
  bool get isSignedIn => _isInitialized && _client.auth.currentUser != null;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Vérifier si Supabase est déjà initialisé
      try {
        _client = Supabase.instance.client;
        _isInitialized = true;
        debugPrint('Client Supabase récupéré avec succès');
        return;
      } catch (e) {
        // Supabase n'est pas encore initialisé, continuons
        debugPrint(
          'Supabase n\'est pas encore initialisé, tentative d\'initialisation',
        );
      }

      // Vérifier si les clés Supabase sont configurées
      if (!SupabaseConfig.isConfigured) {
        debugPrint(
          'Les clés Supabase ne sont pas configurées. Supabase ne sera pas initialisé.',
        );
        throw Exception('Les clés Supabase ne sont pas configurées.');
      }

      // Vérifier si nous utilisons une configuration locale
      final bool isLocalConfig =
          SupabaseConfig.url.contains('localhost') ||
          SupabaseConfig.url.contains('127.0.0.1');

      // Vérifier la connectivité réseau seulement si ce n'est pas une configuration locale
      if (!isLocalConfig) {
        try {
          final urlForLookup =
              SupabaseConfig.url
                  .replaceAll('https://', '')
                  .replaceAll('http://', '')
                  .split('/')[0]; // Prendre seulement le domaine

          final result = await InternetAddress.lookup(urlForLookup);
          if (result.isEmpty || result[0].rawAddress.isEmpty) {
            throw Exception('Impossible de résoudre l\'adresse Supabase');
          }
          debugPrint('Connectivité réseau vérifiée: ${result[0].address}');
        } catch (e) {
          debugPrint('Erreur de connectivité réseau: $e');

          // Si nous ne pouvons pas nous connecter, passer en mode hors ligne
          _initializeOfflineMode();
          return;
        }
      }

      // Initialiser Supabase si ce n'est pas déjà fait
      await Supabase.initialize(
        url: SupabaseConfig.url,
        anonKey: SupabaseConfig.anonKey,
      );
      _client = Supabase.instance.client;
      _isInitialized = true;
      debugPrint('Supabase initialisé avec succès: ${SupabaseConfig.url}');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de Supabase: $e');

      // En cas d'erreur, initialiser en mode hors ligne
      _initializeOfflineMode();
    }
  }

  // Initialiser le service en mode hors ligne
  void _initializeOfflineMode() {
    debugPrint('Initialisation de Supabase en mode hors ligne');
    _isInitialized = true;
    // Créer un client factice pour le mode hors ligne
    // Cela permettra à l'application de fonctionner sans Supabase
  }

  SupabaseClient get client {
    if (!_isInitialized) {
      // Si Supabase n'est pas initialisé, essayer de l'initialiser
      try {
        // Essayer de récupérer le client Supabase s'il est déjà initialisé
        _client = Supabase.instance.client;
        _isInitialized = true;
        debugPrint('Client Supabase récupéré avec succès (lazy)');
      } catch (e) {
        debugPrint('Erreur lors de l\'accès au client Supabase: $e');
        // Initialiser en mode hors ligne au lieu de lancer une exception
        _initializeOfflineMode();
        // Retourner un client factice ou null
        throw Exception(
          'Application en mode hors ligne. Certaines fonctionnalités ne sont pas disponibles.',
        );
      }
    }
    return _client;
  }

  // Méthodes pour interagir avec Supabase

  // Récupérer les données d'un utilisateur
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final response =
          await client.from('profiles').select().eq('id', userId).single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de la récupération des données utilisateur: $e');
      return null;
    }
  }

  // Récupérer les clients
  Future<List<Map<String, dynamic>>> fetchCustomers() async {
    try {
      final response = await client.from('customers').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des clients: $e');
      return [];
    }
  }

  // Récupérer les tâches
  Future<List<Map<String, dynamic>>> fetchTasks() async {
    try {
      final response = await client.from('tasks').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des tâches: $e');
      return [];
    }
  }

  // Récupérer les produits
  Future<List<Map<String, dynamic>>> fetchProducts() async {
    try {
      final response = await client.from('products').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des produits: $e');
      return [];
    }
  }

  // Insérer un client
  Future<Map<String, dynamic>?> insertCustomer(
    Map<String, dynamic> customerData,
  ) async {
    try {
      final response =
          await client.from('customers').insert(customerData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion du client: $e');
      return null;
    }
  }

  // Mettre à jour un client
  Future<Map<String, dynamic>?> updateCustomer(
    String id,
    Map<String, dynamic> customerData,
  ) async {
    try {
      final response =
          await client
              .from('customers')
              .update(customerData)
              .eq('id', id)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du client: $e');
      return null;
    }
  }

  // Supprimer un client
  Future<bool> deleteCustomer(String id) async {
    try {
      await client.from('customers').delete().eq('id', id);
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression du client: $e');
      return false;
    }
  }

  // Insérer un produit
  Future<Map<String, dynamic>?> insertProduct(
    Map<String, dynamic> productData,
  ) async {
    try {
      final response =
          await client.from('products').insert(productData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion du produit: $e');
      return null;
    }
  }

  // Mettre à jour un produit
  Future<Map<String, dynamic>?> updateProduct(
    String id,
    Map<String, dynamic> productData,
  ) async {
    try {
      final response =
          await client
              .from('products')
              .update(productData)
              .eq('id', id)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du produit: $e');
      return null;
    }
  }

  // Supprimer un produit
  Future<bool> deleteProduct(String id) async {
    try {
      await client.from('products').delete().eq('id', id);
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression du produit: $e');
      return false;
    }
  }

  // Insérer une tâche
  Future<Map<String, dynamic>?> insertTask(
    Map<String, dynamic> taskData,
  ) async {
    try {
      final response =
          await client.from('tasks').insert(taskData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion de la tâche: $e');
      return null;
    }
  }

  // Mettre à jour une tâche
  Future<Map<String, dynamic>?> updateTask(
    String id,
    Map<String, dynamic> taskData,
  ) async {
    try {
      final response =
          await client
              .from('tasks')
              .update(taskData)
              .eq('id', id)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour de la tâche: $e');
      return null;
    }
  }

  // Supprimer une tâche
  Future<bool> deleteTask(String id) async {
    try {
      await client.from('tasks').delete().eq('id', id);
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression de la tâche: $e');
      return false;
    }
  }

  // Récupérer les factures
  Future<List<Map<String, dynamic>>> fetchInvoices() async {
    try {
      final response = await client.from('invoices').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des factures: $e');
      return [];
    }
  }

  // Insérer une facture
  Future<Map<String, dynamic>?> insertInvoice(
    Map<String, dynamic> invoiceData,
  ) async {
    try {
      final response =
          await client.from('invoices').insert(invoiceData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion de la facture: $e');
      return null;
    }
  }

  // Mettre à jour une facture
  Future<Map<String, dynamic>?> updateInvoice(
    String id,
    Map<String, dynamic> invoiceData,
  ) async {
    try {
      final response =
          await client
              .from('invoices')
              .update(invoiceData)
              .eq('id', id)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour de la facture: $e');
      return null;
    }
  }

  // Supprimer une facture
  Future<bool> deleteInvoice(String id) async {
    try {
      await client.from('invoices').delete().eq('id', id);
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression de la facture: $e');
      return false;
    }
  }

  // Récupérer les messages
  Future<List<Map<String, dynamic>>> fetchMessages() async {
    try {
      final response = await client.from('messages').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des messages: $e');
      return [];
    }
  }

  // Insérer un message
  Future<Map<String, dynamic>?> insertMessage(
    Map<String, dynamic> messageData,
  ) async {
    try {
      final response =
          await client.from('messages').insert(messageData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion du message: $e');
      return null;
    }
  }

  // Enregistrer une requête AI
  Future<Map<String, dynamic>?> logAIRequest({
    required String model,
    required List<Map<String, dynamic>> messages,
    String? userId,
  }) async {
    try {
      if (!_isInitialized) {
        debugPrint(
          'Supabase n\'est pas initialisé, impossible d\'enregistrer la requête AI',
        );
        return null;
      }

      final logData = {
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'request',
        'model': model,
        'messages': messages,
        'user_id': userId ?? client.auth.currentUser?.id ?? 'anonymous',
      };

      final response =
          await client.from('ai_logs').insert(logData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement de la requête AI: $e');
      return null;
    }
  }

  // Enregistrer une réponse AI
  Future<Map<String, dynamic>?> logAIResponse({
    required String content,
    String? userId,
  }) async {
    try {
      if (!_isInitialized) {
        debugPrint(
          'Supabase n\'est pas initialisé, impossible d\'enregistrer la réponse AI',
        );
        return null;
      }

      final logData = {
        'timestamp': DateTime.now().toIso8601String(),
        'type': 'response',
        'content': content,
        'user_id': userId ?? client.auth.currentUser?.id ?? 'anonymous',
      };

      final response =
          await client.from('ai_logs').insert(logData).select().single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement de la réponse AI: $e');
      return null;
    }
  }

  // Récupérer les logs AI
  Future<List<Map<String, dynamic>>> fetchAILogs({
    int limit = 50,
    String? userId,
  }) async {
    try {
      if (!_isInitialized) {
        debugPrint(
          'Supabase n\'est pas initialisé, impossible de récupérer les logs AI',
        );
        return [];
      }

      var query = client.from('ai_logs').select();

      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      final response = await query
          .order('timestamp', ascending: false)
          .limit(limit);
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Erreur lors de la récupération des logs AI: $e');
      return [];
    }
  }

  // Récupérer les catégories de produits
  Future<List<Map<String, dynamic>>> fetchProductCategories() async {
    try {
      final response = await client.from('product_categories').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint(
        'Erreur lors de la récupération des catégories de produits: $e',
      );
      return [];
    }
  }

  // Insérer une catégorie de produit
  Future<Map<String, dynamic>?> insertProductCategory(
    Map<String, dynamic> categoryData,
  ) async {
    try {
      final response =
          await client
              .from('product_categories')
              .insert(categoryData)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'insertion de la catégorie de produit: $e');
      return null;
    }
  }

  // Mettre à jour une catégorie de produit
  Future<Map<String, dynamic>?> updateProductCategory(
    String id,
    Map<String, dynamic> categoryData,
  ) async {
    try {
      final response =
          await client
              .from('product_categories')
              .update(categoryData)
              .eq('id', id)
              .select()
              .single();
      return response;
    } catch (e) {
      debugPrint(
        'Erreur lors de la mise à jour de la catégorie de produit: $e',
      );
      return null;
    }
  }

  // Supprimer une catégorie de produit
  Future<bool> deleteProductCategory(String id) async {
    try {
      await client.from('product_categories').delete().eq('id', id);
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de la suppression de la catégorie de produit: $e',
      );
      return false;
    }
  }
}
