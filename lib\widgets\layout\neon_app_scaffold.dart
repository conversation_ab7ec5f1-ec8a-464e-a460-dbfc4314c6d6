import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../services/auth_service.dart';
import '../../providers/theme_provider.dart';
import '../user_avatar.dart';
import 'package:url_launcher/url_launcher.dart';

/// Scaffold avec style néon pour toute l'application
class NeonAppScaffold extends ConsumerWidget {
  final Widget child;

  const NeonAppScaffold({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final selectedNavIndex = _getSelectedIndex(context);

    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final mainGradient = appTheme.mainGradient;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(gradient: mainGradient),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Barre supérieure néon
              _buildNeonAppBar(context, ref, themeMode, appTheme),

              // Contenu de la page
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 80), // Espace pour la barre flottante
                  child: child,
                ),
              ),
            ],
          ),
        ),
      ),
      // Barre de navigation flottante
      floatingActionButton: _buildFloatingNavBar(context, ref, selectedNavIndex),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildNeonAppBar(
    BuildContext context,
    WidgetRef ref,
    AppThemeMode themeMode,
    AppThemeMode appTheme,
  ) {
    final user = ref.watch(currentUserProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 26),
            width: 1,
          ), // 0.1 * 255 ≈ 26
        ),
      ),
      child: Row(
        children: [
          // Titre avec effet néon
          Text(
            'HCP-DESIGN CRM',
            style: TextStyle(
              color: primaryColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: primaryColor.withValues(alpha: 128),
                  blurRadius: 5,
                  offset: const Offset(0, 0),
                ),
                Shadow(
                  color: primaryColor.withValues(alpha: 77),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
          ),
          const Spacer(),

          // Bouton des services web
          _buildWebServicesButton(context, ref),

          const SizedBox(width: 8),

          // Bouton de notifications
          _buildNeonIconButton(FontAwesomeIcons.bell, secondaryColor, () {
            // Afficher les notifications
          }, 'Notifications'),

          const SizedBox(width: 8),

          // Bouton de basculement de thème
          _buildNeonIconButton(
            themeMode.isDark ? FontAwesomeIcons.sun : FontAwesomeIcons.moon,
            primaryColor,
            () {
              ref.read(themeProvider.notifier).toggleTheme();
            },
            themeMode.isDark ? 'Mode clair' : 'Mode sombre',
          ),

          const SizedBox(width: 8),

          // Avatar de l'utilisateur avec menu
          PopupMenuButton<String>(
            offset: const Offset(0, 56),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: secondaryColor, width: 1),
            ),
            color: Colors.black.withValues(alpha: 230), // 0.9 * 255 ≈ 230
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: primaryColor.withValues(alpha: 128),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 77),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: UserAvatar(
                imageUrl: user?.avatar,
                name: user?.name ?? 'User',
                size: 36,
              ),
            ),
            itemBuilder:
                (context) => <PopupMenuEntry<String>>[
                  PopupMenuItem<String>(
                    value: 'profile',
                    child: _buildMenuItem(
                      FontAwesomeIcons.user,
                      'Profil',
                      primaryColor,
                    ),
                    onTap: () => context.go('/profile'),
                  ),
                  PopupMenuItem<String>(
                    value: 'settings',
                    child: _buildMenuItem(
                      FontAwesomeIcons.gear,
                      'Paramètres',
                      secondaryColor,
                    ),
                    onTap: () => context.go('/settings'),
                  ),
                  const PopupMenuDivider(height: 1),
                  PopupMenuItem<String>(
                    value: 'logout',
                    child: _buildMenuItem(
                      FontAwesomeIcons.rightFromBracket,
                      'Déconnexion',
                      Colors.redAccent,
                    ),
                    onTap: () {
                      ref.read(authServiceProvider).logout();
                      context.go('/login');
                    },
                  ),
                ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(IconData icon, String label, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Text(label, style: TextStyle(color: color, fontSize: 14)),
      ],
    );
  }

  // Bouton pour les services web (WhatsApp, Telegram, Google Messages)
  Widget _buildWebServicesButton(BuildContext context, WidgetRef ref) {
    final appTheme = ref.watch(themeProvider);
    final secondaryColor = appTheme.secondaryColor;

    return PopupMenuButton<String>(
      offset: const Offset(0, 56),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: secondaryColor, width: 1),
      ),
      color: Colors.black.withValues(alpha: 230), // 0.9 * 255 ≈ 230
      tooltip: 'Services de messagerie web',
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          border: Border.all(
            color: secondaryColor.withValues(alpha: 128),
            width: 1,
          ), // 0.5 * 255 ≈ 128
        ),
        child: Icon(FontAwesomeIcons.globe, size: 18, color: secondaryColor),
      ),
      onSelected: (value) {
        String url;

        switch (value) {
          case 'whatsapp':
            url = 'https://web.whatsapp.com/';
            break;
          case 'telegram':
            url = 'https://web.telegram.org/';
            break;
          case 'google_messages':
            url = 'https://messages.google.com/web/';
            break;
          default:
            return;
        }

        // Ouvrir le site web dans le navigateur par défaut
        _launchUrl(url, context);
      },
      itemBuilder:
          (context) => [
            PopupMenuItem<String>(
              value: 'whatsapp',
              child: _buildMenuItem(
                Icons.message,
                'WhatsApp Web',
                Colors.green,
              ),
            ),
            PopupMenuItem<String>(
              value: 'telegram',
              child: _buildMenuItem(Icons.send, 'Telegram Web', Colors.blue),
            ),
            PopupMenuItem<String>(
              value: 'google_messages',
              child: _buildMenuItem(
                Icons.chat,
                'Google Messages Web',
                Colors.orange,
              ),
            ),
          ],
    );
  }

  Widget _buildNeonIconButton(
    IconData icon,
    Color color,
    VoidCallback onPressed,
    String tooltip,
  ) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            border: Border.all(
              color: color.withValues(alpha: 128),
              width: 1,
            ), // 0.5 * 255 ≈ 128
          ),
          child: Icon(icon, size: 18, color: color),
        ),
      ),
    );
  }

  Widget _buildFloatingNavBar(
    BuildContext context,
    WidgetRef ref,
    int selectedIndex,
  ) {
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Container(
      margin: const EdgeInsets.only(bottom: 20, left: 20, right: 20),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 230), // 0.9 * 255 ≈ 230
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: secondaryColor.withValues(alpha: 128), // 0.5 * 255 ≈ 128
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            blurRadius: 15,
            spreadRadius: 2,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: secondaryColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
            blurRadius: 25,
            spreadRadius: -5,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildFloatingNavItem(
              context,
              ref,
              0,
              FontAwesomeIcons.gaugeHigh,
              'Dashboard',
              primaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              1,
              FontAwesomeIcons.message,
              'Messages',
              secondaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              2,
              FontAwesomeIcons.userGroup,
              'Clients',
              primaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              3,
              FontAwesomeIcons.listCheck,
              'Tâches',
              secondaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              4,
              FontAwesomeIcons.boxesStacked,
              'Inventaire',
              primaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              5,
              FontAwesomeIcons.book,
              'Knowledge',
              primaryColor,
              selectedIndex,
            ),
            _buildFloatingNavItem(
              context,
              ref,
              6,
              FontAwesomeIcons.gear,
              'Settings',
              secondaryColor,
              selectedIndex,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingNavItem(
    BuildContext context,
    WidgetRef ref,
    int index,
    IconData icon,
    String label,
    Color color,
    int selectedIndex,
  ) {
    final isSelected = selectedIndex == index;

    return Tooltip(
      message: label,
      child: InkWell(
        onTap: () => _onItemTapped(context, ref, index),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color:
                isSelected
                    ? color.withValues(alpha: 51) // 0.2 * 255 ≈ 51
                    : Colors.transparent,
            border: isSelected
                ? Border.all(
                    color: color.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                    width: 2,
                  )
                : null,
            boxShadow:
                isSelected
                    ? [
                        BoxShadow(
                          color: color.withValues(alpha: 102), // 0.4 * 255 ≈ 102
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ]
                    : null,
          ),
          child: Icon(
            icon,
            size: 24,
            color:
                isSelected
                    ? Colors.white // Icône sélectionnée en blanc
                    : Colors.grey[800], // Couleur foncée pour les icônes non sélectionnées
          ),
        ),
      ),
    );
  }

  int _getSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).matchedLocation;
    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/messaging')) return 1;
    if (location.startsWith('/customers')) return 2;
    if (location.startsWith('/tasks')) return 3;
    if (location.startsWith('/inventory')) return 4;
    if (location.startsWith('/knowledge')) return 5;
    if (location.startsWith('/settings')) return 6;
    if (location.startsWith('/analytics')) return 7;
    if (location.startsWith('/profile')) {
      return -1; // Index spécial pour le profil
    }
    return 0;
  }

  void _onItemTapped(BuildContext context, WidgetRef ref, int index) {
    switch (index) {
      case -1: // Index spécial pour le profil
        context.go('/profile');
        break;
      case 0:
        context.go('/dashboard');
        break;
      case 1:
        context.go('/messaging');
        break;
      case 2:
        context.go('/customers');
        break;
      case 3:
        context.go('/tasks');
        break;
      case 4:
        context.go('/inventory');
        break;
      case 5:
        context.go('/knowledge');
        break;
      case 6:
        context.go('/settings');
        break;
      case 7:
        context.go('/analytics');
        break;
      case 10:
        // Déconnexion
        ref.read(authServiceProvider).logout();
        context.go('/login');
        break;
    }
  }

  /// Ouvre une URL dans le navigateur par défaut
  Future<void> _launchUrl(String urlString, BuildContext context) async {
    final Uri url = Uri.parse(urlString);
    try {
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Impossible d\'ouvrir $urlString'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
