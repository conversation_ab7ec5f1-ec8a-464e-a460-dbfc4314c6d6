import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../../models/inventory/product.dart';

// Provider pour le service d'inventaire
final inventoryServiceProvider = Provider<InventoryService>((ref) {
  return InventoryService();
});

// Provider pour les produits
final productsProvider = StateNotifierProvider<ProductsNotifier, List<Product>>(
  (ref) {
    return ProductsNotifier();
  },
);

// Provider pour les produits filtrés par catégorie
final productsByCategoryProvider = Provider.family<List<Product>, String>((
  ref,
  category,
) {
  final products = ref.watch(productsProvider);
  if (category.isEmpty) return products;
  return products.where((product) => product.category == category).toList();
});

// Provider pour les produits en rupture de stock
final outOfStockProductsProvider = Provider<List<Product>>((ref) {
  final products = ref.watch(productsProvider);
  return products.where((product) => product.quantity <= 0).toList();
});

// Provider pour les produits en stock faible
final lowStockProductsProvider = Provider<List<Product>>((ref) {
  final products = ref.watch(productsProvider);
  return products
      .where((product) => product.quantity > 0 && product.quantity <= 5)
      .toList();
});

// Provider pour les catégories de produits
final productCategoriesProvider = Provider<List<String>>((ref) {
  final products = ref.watch(productsProvider);
  return products.map((product) => product.category).toSet().toList();
});

// Notifier pour les produits
class ProductsNotifier extends StateNotifier<List<Product>> {
  ProductsNotifier() : super([]) {
    loadProducts();
  }

  Future<void> loadProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = prefs.getStringList('products');

      if (productsJson != null) {
        state = productsJson.map((json) => Product.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des produits: $e');
    }
  }

  // Obtenir la liste des produits
  Future<List<Product>> getProducts() async {
    // Si les produits ne sont pas encore chargés, les charger
    if (state.isEmpty) {
      await loadProducts();
    }
    return state;
  }

  Future<void> _saveProducts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final productsJson = state.map((product) => product.toJson()).toList();
      await prefs.setStringList('products', productsJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des produits: $e');
    }
  }

  Future<void> addProduct(Product product) async {
    state = [...state, product];
    await _saveProducts();
  }

  Future<void> updateProduct(String id, Product product) async {
    state = [
      for (final item in state)
        if (item.id == id) product else item,
    ];
    await _saveProducts();
  }

  Future<void> deleteProduct(String id) async {
    state = state.where((product) => product.id != id).toList();
    await _saveProducts();
  }

  Future<void> updateQuantity(String id, int quantity) async {
    state = [
      for (final product in state)
        if (product.id == id)
          product.copyWith(quantity: quantity, updatedAt: DateTime.now())
        else
          product,
    ];
    await _saveProducts();
  }

  Future<void> importProducts(List<Product> products) async {
    state = [...state, ...products];
    await _saveProducts();
  }
}

// Service principal pour l'inventaire
class InventoryService {
  // Obtenir tous les produits
  Future<List<Product>> getProducts() async {
    // Cette méthode est un placeholder, car les données sont gérées par le provider
    return [];
  }

  // Créer un nouveau produit
  Future<Product> createProduct({
    required String name,
    required String description,
    required double price,
    required int quantity,
    required String category,
    String? imageUrl,
    Map<String, dynamic>? attributes,
  }) async {
    final now = DateTime.now();

    return Product(
      id: const Uuid().v4(),
      name: name,
      description: description,
      price: price,
      quantity: quantity,
      category: category,
      imageUrl: imageUrl,
      attributes: attributes,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Générer des produits de test
  Future<List<Product>> generateSampleProducts() async {
    final now = DateTime.now();

    return [
      Product(
        id: const Uuid().v4(),
        name: 'Coque iPhone 15 Pro - Noir',
        description: 'Coque de protection pour iPhone 15 Pro en silicone noir',
        price: 29.99,
        quantity: 50,
        category: 'Coques iPhone',
        imageUrl: 'https://example.com/iphone15-case-black.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque iPhone 15 Pro - Transparent',
        description: 'Coque de protection transparente pour iPhone 15 Pro',
        price: 24.99,
        quantity: 35,
        category: 'Coques iPhone',
        imageUrl: 'https://example.com/iphone15-case-clear.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Samsung Galaxy S23 - Noir',
        description:
            'Coque de protection pour Samsung Galaxy S23 en silicone noir',
        price: 27.99,
        quantity: 40,
        category: 'Coques Samsung',
        imageUrl: 'https://example.com/samsung-s23-case-black.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Samsung Galaxy S23 - Bleu',
        description:
            'Coque de protection pour Samsung Galaxy S23 en silicone bleu',
        price: 27.99,
        quantity: 30,
        category: 'Coques Samsung',
        imageUrl: 'https://example.com/samsung-s23-case-blue.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Xiaomi 13 - Noir',
        description: 'Coque de protection pour Xiaomi 13 en silicone noir',
        price: 19.99,
        quantity: 25,
        category: 'Coques Xiaomi',
        imageUrl: 'https://example.com/xiaomi-13-case-black.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Xiaomi 13 - Rouge',
        description: 'Coque de protection pour Xiaomi 13 en silicone rouge',
        price: 19.99,
        quantity: 20,
        category: 'Coques Xiaomi',
        imageUrl: 'https://example.com/xiaomi-13-case-red.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Google Pixel 7 - Noir',
        description: 'Coque de protection pour Google Pixel 7 en silicone noir',
        price: 24.99,
        quantity: 15,
        category: 'Coques Google',
        imageUrl: 'https://example.com/pixel-7-case-black.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque Google Pixel 7 - Vert',
        description: 'Coque de protection pour Google Pixel 7 en silicone vert',
        price: 24.99,
        quantity: 10,
        category: 'Coques Google',
        imageUrl: 'https://example.com/pixel-7-case-green.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque iPhone 14 - Noir',
        description: 'Coque de protection pour iPhone 14 en silicone noir',
        price: 26.99,
        quantity: 45,
        category: 'Coques iPhone',
        imageUrl: 'https://example.com/iphone14-case-black.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
      Product(
        id: const Uuid().v4(),
        name: 'Coque iPhone 14 - Rose',
        description: 'Coque de protection pour iPhone 14 en silicone rose',
        price: 26.99,
        quantity: 30,
        category: 'Coques iPhone',
        imageUrl: 'https://example.com/iphone14-case-pink.jpg',
        isActive: true,
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }
}
