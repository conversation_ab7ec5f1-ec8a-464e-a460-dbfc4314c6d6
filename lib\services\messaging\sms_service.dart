import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
// import 'package:flutter_sms/flutter_sms.dart' as flutter_sms; // Désactivé temporairement
// import 'package:telephony/telephony.dart'; // Désactivé temporairement
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/channel_config.dart';

import '../customer/customer_service.dart';
import '../notification_service.dart';

// Provider pour le service SMS
final smsServiceProvider = Provider<SMSService>((ref) {
  final customerService = ref.watch(customerServiceProvider);
  final notificationService = ref.watch(notificationServiceProvider);
  return SMSService(customerService, notificationService);
});

// Provider pour les messages SMS
final smsMessagesProvider =
    StateNotifierProvider<SMSMessagesNotifier, List<Message>>((ref) {
      return SMSMessagesNotifier();
    });

// Notifier pour les messages SMS
class SMSMessagesNotifier extends StateNotifier<List<Message>> {
  SMSMessagesNotifier() : super([]);

  Future<void> loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList('sms_messages') ?? [];

      state = messagesJson.map((json) => Message.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Erreur lors du chargement des messages SMS: $e');
    }
  }

  Future<void> saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = state.map((message) => message.toJson()).toList();
      await prefs.setStringList('sms_messages', messagesJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des messages SMS: $e');
    }
  }

  void addMessage(Message message) {
    state = [message, ...state];
    saveMessages();
  }

  List<Message> getMessagesByContactId(String contactId) {
    return state.where((message) => message.contactId == contactId).toList();
  }

  List<Message> getMessagesByPhoneNumber(String phoneNumber) {
    return state
        .where((message) => message.contactId.contains(phoneNumber))
        .toList();
  }
}

class SMSService {
  // final Telephony _telephony = Telephony.instance; // Désactivé temporairement
  final CustomerService _customerService;
  final NotificationService _notificationService;
  bool _isInitialized = false;
  late ChannelConfig _config;

  SMSService(this._customerService, this._notificationService) {
    // Initialisation par défaut
    _isInitialized = false;
  }

  Future<void> initialize(ChannelConfig config) async {
    _config = config;

    try {
      // Version simplifiée sans le plugin telephony
      _isInitialized = true;
      debugPrint(
        'Service SMS initialisé en mode limité (sans plugin telephony)',
      );
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du service SMS: $e');
    }
  }

  bool get isInitialized => _isInitialized;

  // Méthode pour gérer la réception d'un SMS (version simplifiée)

  Future<void> sendMessage(Message message) async {
    if (!_isInitialized) {
      throw Exception('SMS service not initialized');
    }

    try {
      final phoneNumber = message.contactId;
      final accountSid = _config.accountId;
      final authToken = _config.apiSecret;
      final fromNumber = _config.phoneNumber;

      // Si nous utilisons Twilio (vérifier si les informations Twilio sont configurées)
      if (_config.accountId != null && _config.apiSecret != null) {
        final endpoint =
            'https://api.twilio.com/2010-04-01/Accounts/$accountSid/Messages.json';

        final response = await http.post(
          Uri.parse(endpoint),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization':
                'Basic ${base64Encode(utf8.encode('$accountSid:$authToken'))}',
          },
          body: {
            'To': phoneNumber,
            'From': fromNumber,
            'Body': message.content,
          },
        );

        if (response.statusCode != 201) {
          throw Exception('Failed to send SMS: ${response.body}');
        }
      }
      // Si nous utilisons l'API native Android (désactivé temporairement)
      else {
        // Version simplifiée sans le plugin flutter_sms
        debugPrint('Envoi de SMS natif désactivé temporairement');
        debugPrint('Message: ${message.content}');
        debugPrint('Destinataire: $phoneNumber');

        // Simuler un envoi réussi
        await Future.delayed(const Duration(seconds: 1));
      }

      debugPrint('SMS sent successfully');
    } catch (e) {
      debugPrint('Error sending SMS: $e');
      rethrow;
    }
  }

  // Méthode pour envoyer un SMS directement (sans passer par Message)
  Future<bool> sendSMS({
    required String phoneNumber,
    required String message,
    required String contactId,
  }) async {
    try {
      // Version simplifiée sans le plugin flutter_sms
      debugPrint('Envoi de SMS natif désactivé temporairement');
      debugPrint('Message: $message');
      debugPrint('Destinataire: $phoneNumber');

      // Simuler un envoi réussi
      await Future.delayed(const Duration(seconds: 1));

      // Créer un objet Message et l'ajouter à l'historique
      final container = ProviderContainer();
      final messagesNotifier = container.read(smsMessagesProvider.notifier);

      messagesNotifier.addMessage(
        Message(
          id: const Uuid().v4(),
          contactId: contactId,
          content: message,
          isUser: true,
          timestamp: DateTime.now(),
          channel: MessageChannel.sms,
          status: MessageStatus.sent,
        ),
      );

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du SMS: $e');
      return false;
    }
  }

  // Traiter les messages en attente
  Future<void> processPendingMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingMessages = prefs.getStringList('pending_sms_messages') ?? [];

      if (pendingMessages.isEmpty) {
        return;
      }

      debugPrint('Traitement de ${pendingMessages.length} messages en attente');

      for (final messageStr in pendingMessages) {
        try {
          // Convertir la chaîne en Map
          final messageMap = json.decode(messageStr) as Map<String, dynamic>;

          // Créer un message simulé à partir des données stockées
          final address = messageMap['address'] as String?;
          final body = messageMap['body'] as String?;
          final timestamp = DateTime.parse(messageMap['timestamp']);

          // Simuler un SmsMessage en créant directement un Message
          final contactId = address ?? const Uuid().v4();

          // Créer un objet Message et l'ajouter à l'historique
          final container = ProviderContainer();
          final messagesNotifier = container.read(smsMessagesProvider.notifier);

          messagesNotifier.addMessage(
            Message(
              id: const Uuid().v4(),
              contactId: contactId,
              content: body ?? '',
              isUser: false,
              timestamp: timestamp,
              channel: MessageChannel.sms,
              status: MessageStatus.delivered,
            ),
          );

          // Le message a déjà été traité en créant l'objet Message
        } catch (e) {
          debugPrint('Erreur lors du traitement d\'un message en attente: $e');
        }
      }

      // Effacer les messages en attente
      await prefs.setStringList('pending_sms_messages', []);
    } catch (e) {
      debugPrint('Erreur lors du traitement des messages en attente: $e');
    }
  }

  Future<void> processWebhook(Map<String, dynamic> payload) async {
    if (!_isInitialized) {
      throw Exception('SMS service not initialized');
    }

    try {
      final from = payload['From'];
      final to = payload['To'];
      final body = payload['Body'];
      final messageId = payload['MessageSid'];
      final status = payload['MessageStatus'];

      debugPrint(
        'Received SMS webhook: From=$from, To=$to, Body=$body, Status=$status',
      );

      // Traiter les notifications de statut
      if (status != null) {
        MessageStatus messageStatus;
        switch (status) {
          case 'sent':
            messageStatus = MessageStatus.sent;
            break;
          case 'delivered':
            messageStatus = MessageStatus.delivered;
            break;
          case 'read':
            messageStatus = MessageStatus.read;
            break;
          default:
            messageStatus = MessageStatus.failed;
        }

        // Mettre à jour le statut du message dans le provider
        final container = ProviderContainer();
        final messagesNotifier = container.read(smsMessagesProvider.notifier);

        // Charger les messages pour trouver celui qui correspond à l'ID
        await messagesNotifier.loadMessages();

        // Nous devrions avoir une méthode pour mettre à jour le statut d'un message par son ID
        // Pour l'instant, nous nous contentons de logger l'information
        debugPrint('SMS $messageId status updated to $messageStatus');
      }

      // Traiter les messages entrants
      if (from != null && body != null) {
        // Simuler un SmsMessage en créant directement un Message
        final customers = await _customerService.getCustomers();
        final matchingCustomers =
            customers
                .where(
                  (customer) =>
                      customer.phone != null && customer.phone!.contains(from),
                )
                .toList();

        String contactId = from;

        if (matchingCustomers.isNotEmpty) {
          contactId = matchingCustomers.first.id;
        }

        // Créer un objet Message et l'ajouter à l'historique
        final container = ProviderContainer();
        final messagesNotifier = container.read(smsMessagesProvider.notifier);

        messagesNotifier.addMessage(
          Message(
            id: const Uuid().v4(),
            contactId: contactId,
            content: body,
            isUser: false,
            timestamp: DateTime.now(),
            channel: MessageChannel.sms,
            status: MessageStatus.delivered,
          ),
        );

        // Envoyer une notification
        _notificationService.showNotification(
          title:
              matchingCustomers.isNotEmpty
                  ? 'SMS de ${matchingCustomers.first.name}'
                  : 'SMS de $from',
          body: body,
          payload: 'sms:$contactId',
        );

        debugPrint('Received SMS from $from: $body');
      }
    } catch (e) {
      debugPrint('Error processing SMS webhook: $e');
      rethrow;
    }
  }
}

// Gestionnaire de messages en arrière-plan (version simplifiée)
@pragma('vm:entry-point')
void backgroundMessageHandler(Map<String, dynamic> message) async {
  // Cette fonction est appelée lorsqu'un SMS est reçu en arrière-plan
  final String body = message['body'] ?? '';
  final String address = message['address'] ?? '';

  debugPrint('SMS reçu en arrière-plan: $body');

  // Nous ne pouvons pas accéder aux providers ici, donc nous devons stocker le message
  // dans les préférences partagées pour le traiter plus tard
  try {
    final prefs = await SharedPreferences.getInstance();
    final pendingMessages = prefs.getStringList('pending_sms_messages') ?? [];

    // Créer un message temporaire
    final tempMessage = {
      'address': address,
      'body': body,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Ajouter le message à la liste des messages en attente
    pendingMessages.add(json.encode(tempMessage));
    await prefs.setStringList('pending_sms_messages', pendingMessages);
  } catch (e) {
    debugPrint('Erreur lors du traitement du SMS en arrière-plan: $e');
  }
}


