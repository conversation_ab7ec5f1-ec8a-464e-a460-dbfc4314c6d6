import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/messaging/channel_config.dart';
import '../../models/messaging/message.dart';
import '../../providers/theme_provider.dart';
import '../../services/messaging/whatsapp_service.dart';
import '../../services/supabase_service.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class WhatsAppSettingsScreen extends ConsumerStatefulWidget {
  const WhatsAppSettingsScreen({super.key});

  @override
  ConsumerState<WhatsAppSettingsScreen> createState() =>
      _WhatsAppSettingsScreenState();
}

class _WhatsAppSettingsScreenState
    extends ConsumerState<WhatsAppSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountSidController = TextEditingController();
  final _authTokenController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _webhookUrlController = TextEditingController();
  final _tempTokenController = TextEditingController();
  final _permanentTokenController = TextEditingController();
  final _phoneNumberIdController = TextEditingController();
  final _businessIdController = TextEditingController();
  bool _isEnabled = false;
  bool _isLoading = true;
  bool _showApiKey = false;
  bool _showTokens = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _accountSidController.dispose();
    _authTokenController.dispose();
    _phoneNumberController.dispose();
    _webhookUrlController.dispose();
    _tempTokenController.dispose();
    _permanentTokenController.dispose();
    _phoneNumberIdController.dispose();
    _businessIdController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // D'abord, essayer de charger depuis les préférences partagées
      final prefs = await SharedPreferences.getInstance();
      final accountSid = prefs.getString('whatsapp_account_sid');
      final authToken = prefs.getString('whatsapp_auth_token');
      final phoneNumber = prefs.getString('whatsapp_phone_number');
      final isEnabled = prefs.getBool('whatsapp_enabled');

      // Si nous avons des données locales, les utiliser
      if (accountSid != null || authToken != null || phoneNumber != null) {
        setState(() {
          _accountSidController.text = accountSid ?? '';
          _authTokenController.text = authToken ?? '';
          _phoneNumberController.text = phoneNumber ?? '';
          if (isEnabled != null) _isEnabled = isEnabled;
        });

        // Afficher un message indiquant que les données sont chargées localement
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Paramètres chargés depuis le stockage local'),
              backgroundColor: Colors.blue,
            ),
          );
        }
      } else {
        // Essayer de charger depuis Supabase si aucune donnée locale n'est disponible
        try {
          final supabaseService = ref.read(supabaseServiceProvider);

          // Vérifier si Supabase est initialisé
          if (!supabaseService.isInitialized) {
            await supabaseService.initialize();
          }

          // Récupérer la configuration WhatsApp depuis Supabase
          final response =
              await supabaseService.client
                  .from('channel_configs')
                  .select()
                  .eq('channel_type', 'whatsapp')
                  .maybeSingle();

          if (response != null) {
            setState(() {
              _accountSidController.text =
                  response['account_id'] as String? ?? '';
              _authTokenController.text = response['api_key'] as String? ?? '';
              _phoneNumberController.text =
                  response['phone_number'] as String? ?? '';
              _webhookUrlController.text =
                  response['webhook_url'] as String? ?? '';
              _isEnabled = response['enabled'] as bool? ?? false;
            });

            // Sauvegarder également dans les préférences partagées
            await prefs.setString(
              'whatsapp_account_sid',
              _accountSidController.text,
            );
            await prefs.setString(
              'whatsapp_auth_token',
              _authTokenController.text,
            );
            await prefs.setString(
              'whatsapp_phone_number',
              _phoneNumberController.text,
            );
            await prefs.setBool('whatsapp_enabled', _isEnabled);
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Erreur lors du chargement des paramètres depuis Supabase: $e',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des paramètres: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final supabaseService = ref.read(supabaseServiceProvider);

      // Vérifier si Supabase est initialisé
      if (!supabaseService.isInitialized) {
        try {
          await supabaseService.initialize();
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Erreur de connexion à Supabase: $e\nLes paramètres seront sauvegardés localement.',
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 5),
              ),
            );
          }
          // Continuer même en cas d'erreur - nous sauvegarderons localement
        }
      }

      // Sauvegarder les paramètres dans les préférences partagées
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        'whatsapp_account_sid',
        _accountSidController.text.trim(),
      );
      await prefs.setString(
        'whatsapp_auth_token',
        _authTokenController.text.trim(),
      );
      await prefs.setString(
        'whatsapp_phone_number',
        _phoneNumberController.text.trim(),
      );
      await prefs.setBool('whatsapp_enabled', _isEnabled);

      // Cette section est maintenant gérée plus bas dans le code

      // Essayer d'enregistrer dans Supabase si possible
      try {
        final supabaseService = ref.read(supabaseServiceProvider);
        if (supabaseService.isInitialized) {
          // Préparer les données à enregistrer
          final configData = {
            'channel_type': 'whatsapp',
            'account_id': _accountSidController.text.trim(),
            'api_key': _authTokenController.text.trim(),
            'phone_number': _phoneNumberController.text.trim(),
            'webhook_url': _webhookUrlController.text.trim(),
            'enabled': _isEnabled,
            'updated_at': DateTime.now().toIso8601String(),
          };

          // Vérifier si la configuration existe déjà
          final existingConfig =
              await supabaseService.client
                  .from('channel_configs')
                  .select('id')
                  .eq('channel_type', 'whatsapp')
                  .maybeSingle();

          if (existingConfig != null) {
            // Mettre à jour la configuration existante
            await supabaseService.client
                .from('channel_configs')
                .update(configData)
                .eq('id', existingConfig['id']);
          } else {
            // Créer une nouvelle configuration
            await supabaseService.client.from('channel_configs').insert({
              ...configData,
              'created_at': DateTime.now().toIso8601String(),
            });
          }
        }
      } catch (e) {
        debugPrint('Erreur lors de l\'enregistrement dans Supabase: $e');
        // Continuer même en cas d'erreur - nous avons déjà sauvegardé localement
      }

      // Initialiser le service WhatsApp avec la nouvelle configuration
      final whatsAppService = ref.read(whatsAppServiceProvider);
      await whatsAppService.initialize(
        ChannelConfig(
          channel: MessageChannel.whatsapp,
          isEnabled: _isEnabled,
          accountId: _accountSidController.text.trim(),
          apiKey: _authTokenController.text.trim(),
          phoneNumber: _phoneNumberController.text.trim(),
        ),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paramètres WhatsApp enregistrés avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors de l\'enregistrement des paramètres: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Cette méthode n'est pas utilisée actuellement mais pourrait être utile à l'avenir
  // pour tester la connexion WhatsApp
  /*
  Future<void> _testConnection() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Initialiser le service WhatsApp avec les paramètres actuels
      final whatsAppService = ref.read(whatsAppServiceProvider);
      await whatsAppService.initialize(
        ChannelConfig(
          channel: MessageChannel.whatsapp,
          isEnabled: true,
          accountId: _accountSidController.text.trim(),
          apiKey: _authTokenController.text.trim(),
          phoneNumber: _phoneNumberController.text.trim(),
        ),
      );

      // Envoyer un message de test à votre propre numéro
      final phoneNumber = await _showPhoneNumberDialog();

      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        final result = await whatsAppService.sendMessage(
          to: phoneNumber,
          content: 'Test de connexion WhatsApp réussi! 🎉',
        );

        if (result is Map<String, dynamic> && result['success'] as bool) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Connexion WhatsApp testée avec succès!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Erreur lors du test de connexion: ${result['error']}',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du test de connexion: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
  */

  // Cette méthode n'est pas utilisée actuellement mais pourrait être utile à l'avenir
  // pour demander un numéro de téléphone
  /*
  Future<String?> _showPhoneNumberDialog() async {
    return await showDialog<String>(
      context: context,
      builder: (context) {
        final TextEditingController phoneNumberController =
            TextEditingController();
        return AlertDialog(
          title: const Text('Entrez votre numéro de téléphone WhatsApp'),
          content: TextField(
            controller: phoneNumberController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              labelText: 'Numéro de téléphone',
              hintText: '+1234567890',
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Envoyer'),
              onPressed: () {
                Navigator.of(context).pop(phoneNumberController.text);
              },
            ),
          ],
        );
      },
    );
  }
  */

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration WhatsApp'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child:
            _isLoading
                ? Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NeonCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Configuration de l\'API WhatsApp Business',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Account SID Twilio
                              _buildSecureField(
                                controller: _accountSidController,
                                labelText: 'Account SID Twilio',
                                hintText:
                                    'Ex: **********************************',
                                isVisible: _showApiKey,
                                onToggleVisibility: () {
                                  setState(() {
                                    _showApiKey = !_showApiKey;
                                  });
                                },
                                color: primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Identifiant de votre compte Twilio pour l\'API WhatsApp',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Auth Token Twilio
                              _buildSecureField(
                                controller: _authTokenController,
                                labelText: 'Auth Token Twilio',
                                hintText:
                                    'Ex: 159cbca0bd80694ca3a29f4ef7a716e0',
                                isVisible: _showApiKey,
                                onToggleVisibility: () {
                                  setState(() {
                                    _showApiKey = !_showApiKey;
                                  });
                                },
                                color: primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Token d\'authentification pour votre compte Twilio',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Numéro de téléphone WhatsApp
                              NeonTextField(
                                controller: _phoneNumberController,
                                labelText: 'Numéro de téléphone WhatsApp',
                                hintText: 'Ex: whatsapp:+2250709495848',
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Numéro de téléphone WhatsApp au format international avec préfixe',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Token d'accès temporaire
                              _buildSecureField(
                                controller: _tempTokenController,
                                labelText: 'Token d\'accès temporaire',
                                hintText: 'Token temporaire (valide 24h)',
                                isVisible: _showTokens,
                                onToggleVisibility: () {
                                  setState(() {
                                    _showTokens = !_showTokens;
                                  });
                                },
                                color: primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Token temporaire fourni par Meta pour les tests (valide 24h)',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Token permanent (OAuth)
                              _buildSecureField(
                                controller: _permanentTokenController,
                                labelText: 'Token permanent (OAuth)',
                                hintText: 'Token permanent pour la production',
                                isVisible: _showTokens,
                                color: primaryColor,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Token permanent obtenu via OAuth pour la production',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Phone Number ID
                              NeonTextField(
                                controller: _phoneNumberIdController,
                                labelText: 'Phone Number ID',
                                hintText: 'Ex: whatsapp:+2250709495848',
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'ID du numéro de téléphone WhatsApp Business fourni par Meta',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 16),

                              // Business ID
                              NeonTextField(
                                controller: _businessIdController,
                                labelText: 'Business ID',
                                hintText: 'ID de votre entreprise WhatsApp',
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'ID de votre entreprise WhatsApp Business fourni par Meta',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 153),
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Bouton de sauvegarde
                              Center(
                                child: NeonButton(
                                  text: 'Sauvegarder',
                                  icon: Icons.save,
                                  color: primaryColor,
                                  onPressed: _saveSettings,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  // Construire un champ de texte sécurisé
  Widget _buildSecureField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required bool isVisible,
    required Color color,
    Function()? onToggleVisibility,
  }) {
    return Row(
      children: [
        Expanded(
          child: NeonTextField(
            controller: controller,
            labelText: labelText,
            hintText: hintText,
            obscureText: !isVisible,
          ),
        ),
        if (onToggleVisibility != null)
          IconButton(
            icon: Icon(
              isVisible ? Icons.visibility_off : Icons.visibility,
              color: color,
            ),
            onPressed: onToggleVisibility,
          ),
      ],
    );
  }
}
