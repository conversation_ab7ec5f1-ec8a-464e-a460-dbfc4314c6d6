import 'package:flutter/material.dart';

// Application Coffee Ordering – Style Coffee Beige
// Titre de thème : “Creamy Beans”

// 🎨 Palette de couleurs :
const Color creamyBackground = Color(0xFFF2E8D4);
const Color creamyCardBackground = Color(0xFFE8C4B8);
const Color creamyTextPrimary = Color(0xFF362015);
const Color creamyTextSecondary = Color(0xFF6B4B38);
const Color creamyHighlight = Color(0xFFA7D9D9);
const Color creamyHighlightSecondary = Color(0xFFEFC66A);
const Color creamyAccent = Color(0xFFC97EFF);

const Color creamyButtonPrimaryBackground = Color(0xFF4C2A1C);
const Color creamyButtonPrimaryText = Color(0xFFFFFFFF);

const Color creamySidebarBackground = Color(0xFFA8815D);
const Color creamySidebarIcon = Color(0xFFFFFFFF);
const Color creamySidebarActive = Color(0xFFF5E8D4);

const Color creamyStatCardClients = Color(0xFFFBECD8);
const Color creamyStatCardTasks = Color(0xFFF6E1CA);
const Color creamyStatCardMessages = Color(0xFFF4D1CB);

// Couleurs spécifiques au thème
const Color cappuccinoSombre = Color(0xFF4A3B2A);
const Color cremeVanille = Color(0xFFEFC66A);
const Color laitMousseux = Color(0xFFF5E8D4);
const Color coffeeLatte = Color(0xFFF2E8D4);
const Color espressoDense = Color(0xFF362015);
const Color macchiatoRose = Color(0xFFE8C4B8);

// 🖍️ Couleurs de texte (utilisant les nouvelles constantes) :
const Color textPrincipalCreamy = creamyTextPrimary;
const Color titresCreamy =
    creamyTextPrimary; // Ajusté pour utiliser la couleur de texte primaire
const Color textSecondaireCreamy = creamyTextSecondary;

class CreamyBeansTheme {
  static final ThemeData themeData = ThemeData(
    brightness: Brightness.light,
    primaryColor: cappuccinoSombre,
    scaffoldBackgroundColor: coffeeLatte,
    colorScheme: const ColorScheme.light(
      primary: cappuccinoSombre,
      secondary: cremeVanille,
      surface: laitMousseux,
      error: Colors.redAccent, // À définir selon les besoins
      onPrimary:
          Colors.white, // Texte sur couleur primaire (boutons principaux)
      onSecondary:
          espressoDense, // Texte sur couleur secondaire (boutons secondaires)
      onSurface: espressoDense, // Texte sur le fond principal
      onError: Colors.white,
      brightness: Brightness.light,
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(color: titresCreamy, fontWeight: FontWeight.bold),
      displayMedium: TextStyle(
        color: titresCreamy,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(color: titresCreamy, fontWeight: FontWeight.bold),
      headlineMedium: TextStyle(
        color: titresCreamy,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        color: titresCreamy,
        fontWeight: FontWeight.bold,
      ),
      titleLarge: TextStyle(color: titresCreamy, fontWeight: FontWeight.bold),
      bodyLarge: TextStyle(color: textPrincipalCreamy),
      bodyMedium: TextStyle(color: textPrincipalCreamy),
      labelLarge: TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ), // Pour les boutons principaux
      bodySmall: TextStyle(
        color: textSecondaireCreamy,
      ), // Placeholder, texte secondaire
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: cappuccinoSombre,
      foregroundColor:
          Colors.white, // Couleur du titre et des icônes de l'AppBar
      elevation: 2,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
    buttonTheme: const ButtonThemeData(
      buttonColor: cappuccinoSombre, // Boutons principaux
      textTheme: ButtonTextTheme.primary,
      colorScheme: ColorScheme.light(
        primary: cappuccinoSombre,
        secondary: cremeVanille,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: cappuccinoSombre,
        foregroundColor: Colors.white,
        textStyle: const TextStyle(fontWeight: FontWeight.bold),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor:
            cremeVanille, // Couleur du texte pour les boutons secondaires
        backgroundColor: Colors.transparent, // Fond pour boutons secondaires
        textStyle: const TextStyle(
          color: espressoDense,
          fontWeight: FontWeight.bold,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: laitMousseux,
      hintStyle: TextStyle(
        color: textSecondaireCreamy.withValues(alpha: 204),
      ), // 0.8 * 255 ≈ 204
      labelStyle: const TextStyle(color: textSecondaireCreamy),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none, // Bordure invisible
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(
          color: const Color(
            0xFF4A3B2A,
          ).withValues(alpha: 51), // 0.2 * 255 ≈ 51
          width: 1,
        ), // Bordure subtile
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: cappuccinoSombre, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.redAccent.shade200, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.redAccent.shade400, width: 1.5),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),
    cardTheme: CardTheme(
      color: macchiatoRose, // Arrière-plan de cartes secondaires
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
    ),
    iconTheme: const IconThemeData(color: espressoDense),
    checkboxTheme: CheckboxThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return creamyButtonPrimaryBackground; // Couleur quand coché
        }
        return creamyCardBackground; // Couleur quand non coché
      }),
      checkColor: WidgetStateProperty.all(creamyButtonPrimaryText),
      side: BorderSide(
        color: creamyButtonPrimaryBackground.withValues(alpha: 128),
      ), // 0.5 * 255 ≈ 128
    ),
    radioTheme: RadioThemeData(
      fillColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return creamyButtonPrimaryBackground;
        }
        return creamyCardBackground;
      }),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return creamyHighlightSecondary;
        }
        return creamyCardBackground;
      }),
      trackColor: WidgetStateProperty.resolveWith<Color?>((
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return creamyButtonPrimaryBackground.withValues(
            alpha: 179,
          ); // 0.7 * 255 ≈ 179
        }
        return creamyButtonPrimaryBackground.withValues(
          alpha: 77,
        ); // 0.3 * 255 ≈ 77
      }),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: creamyButtonPrimaryBackground,
      selectedItemColor: creamyHighlightSecondary,
      unselectedItemColor: creamyBackground.withValues(
        alpha: 179,
      ), // 0.7 * 255 ≈ 179
      type: BottomNavigationBarType.fixed,
    ),
  );

  // Couleurs spécifiques au thème qui ne sont pas directement dans ThemeData mais utiles pour les widgets personnalisés
  static const Color primaryTextColor = creamyTextPrimary;
  static const Color secondaryTextColor = creamyTextSecondary;
  static const Color headerColor = creamyButtonPrimaryBackground;
  static const Color footerColor = creamyButtonPrimaryBackground;
  static const Color selectionButtonColor =
      creamyHighlightSecondary; // Mise à jour
  static const Color cardBackgroundColor = creamyCardBackground;

  // Couleurs pour les éléments de la barre latérale (sidebar)
  static const Color sidebarBackgroundColor = creamySidebarBackground;
  static const Color sidebarIconColor = creamySidebarIcon;
  static const Color sidebarActiveColor = creamySidebarActive;

  // Couleurs pour les cartes de statistiques (stat_card)
  static const Color statCardClientsColor = creamyStatCardClients;
  static const Color statCardTasksColor = creamyStatCardTasks;
  static const Color statCardMessagesColor = creamyStatCardMessages;

  // Gradients (si vous décidez de les utiliser)
  static LinearGradient mainGradient = LinearGradient(
    colors: [
      creamyBackground,
      creamyCardBackground.withValues(alpha: 204),
    ], // 0.8 * 255 ≈ 204
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static LinearGradient sidebarGradient = LinearGradient(
    colors: [
      creamySidebarBackground,
      creamySidebarBackground.withValues(alpha: 204), // 0.8 * 255 ≈ 204
    ], // Dégradé pour la sidebar
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
