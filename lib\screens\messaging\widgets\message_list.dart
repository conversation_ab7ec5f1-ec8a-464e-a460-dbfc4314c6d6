import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../models/messaging/message.dart';
import '../../../services/messaging/messaging_service.dart';
import 'message_bubble.dart';

class MessageList extends ConsumerWidget {
  final String contactId;
  final MessageChannel? channel;

  const MessageList({super.key, required this.contactId, this.channel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterParams = FilterParams(contactId: contactId, channel: channel);
    final messages = ref.watch(filteredMessagesProvider(filterParams));

    // Trier les messages par date (du plus récent au plus ancien)
    final sortedMessages = List.of(messages)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: const AssetImage('assets/images/chat_background.jpg'),
          fit: BoxFit.cover,
          colorFilter: ColorFilter.mode(
            Colors.black.withValues(alpha: 179), // 0.7 * 255 ≈ 179
            BlendMode.darken,
          ),
        ),
      ),
      child:
          sortedMessages.isEmpty
              ? const Center(
                child: Text(
                  'Aucun message',
                  style: TextStyle(color: Colors.white),
                ),
              )
              : ListView.builder(
                reverse: true,
                padding: const EdgeInsets.all(16),
                itemCount: sortedMessages.length,
                itemBuilder: (context, index) {
                  final message = sortedMessages[index];
                  final previousMessage =
                      index < sortedMessages.length - 1
                          ? sortedMessages[index + 1]
                          : null;

                  // Vérifier si c'est un nouveau jour
                  final showDateSeparator =
                      previousMessage == null ||
                      !_isSameDay(message.timestamp, previousMessage.timestamp);

                  // Vérifier si c'est un nouveau groupe de messages
                  final isNewGroup =
                      previousMessage == null ||
                      previousMessage.isUser != message.isUser ||
                      _timeDifferenceInMinutes(
                            message.timestamp,
                            previousMessage.timestamp,
                          ) >
                          5;

                  return Column(
                    children: [
                      if (showDateSeparator)
                        _buildDateSeparator(message.timestamp),

                      MessageBubble(
                        message: message,
                        showSender: isNewGroup,
                        showTimestamp: isNewGroup,
                      ),
                    ],
                  );
                },
              ),
    );
  }

  Widget _buildDateSeparator(DateTime date) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: Colors.white.withValues(alpha: 77), // 0.3 * 255 ≈ 77
              thickness: 1,
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 128), // 0.5 * 255 ≈ 128
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              _formatDate(date),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Divider(
              color: Colors.white.withValues(alpha: 77), // 0.3 * 255 ≈ 77
              thickness: 1,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == DateTime(now.year, now.month, now.day)) {
      return 'Aujourd\'hui';
    } else if (dateToCheck == yesterday) {
      return 'Hier';
    } else {
      return DateFormat('dd/MM/yyyy').format(date);
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  int _timeDifferenceInMinutes(DateTime date1, DateTime date2) {
    return date1.difference(date2).inMinutes.abs();
  }
}
