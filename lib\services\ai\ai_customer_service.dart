import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/ai/ai_message.dart';
import '../../models/customer/customer.dart';
import '../customer/customer_service.dart';
import 'ai_service.dart';

// Provider pour le service d'intégration IA-Client
final aiCustomerServiceProvider = Provider<AICustomerService>((ref) {
  final customerService = ref.watch(customerServiceProvider);
  final aiService = ref.watch(aiServiceProvider);
  return AICustomerService(
    customerService: customerService,
    aiService: aiService,
  );
});

class AICustomerService {
  final CustomerService customerService;
  final AIService aiService;

  AICustomerService({required this.customerService, required this.aiService});

  // Méthode pour extraire les informations client à partir d'un message
  Future<Map<String, dynamic>?> extractCustomerInfo(String message) async {
    try {
      // Créer un prompt spécifique pour extraire les informations client
      const systemPrompt = '''
Tu es un assistant spécialisé dans l'extraction d'informations client.
Analyse le message et extrait les informations suivantes si elles sont présentes :
- Nom complet
- Numéro de téléphone
- Email
- Source (WhatsApp, Facebook, Instagram, Site web, etc.)
- Tags pertinents (séparés par des virgules)
- Notes ou informations supplémentaires

Réponds UNIQUEMENT au format JSON suivant, sans aucun texte supplémentaire :
{
  "name": "Nom complet du client ou null si absent",
  "phone": "Numéro de téléphone ou null si absent",
  "email": "Email ou null si absent",
  "source": "Source du contact ou null si absente",
  "tags": ["tag1", "tag2"] ou [] si aucun tag,
  "notes": "Notes ou informations supplémentaires ou null si absentes"
}

Si aucune information client n'est détectée, réponds avec un JSON vide : {}
''';

      // Envoyer le message à l'IA pour extraction
      final aiMessage = AIMessage(role: AIMessageRole.user, content: message);
      final response = await aiService.sendMessage([
        aiMessage,
      ], overrideSystemPrompt: systemPrompt);

      // Vérifier si la réponse est au format JSON
      if (response.trim().startsWith('{') && response.trim().endsWith('}')) {
        // Nettoyer la réponse pour s'assurer qu'elle est au format JSON valide
        final cleanedResponse = response.trim();

        // Convertir la réponse en Map
        final Map<String, dynamic> extractedInfo = Map<String, dynamic>.from(
          jsonDecode(cleanedResponse) as Map,
        );

        // Vérifier si des informations ont été extraites
        if (extractedInfo.isNotEmpty && extractedInfo['name'] != null) {
          return extractedInfo;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Erreur lors de l\'extraction des informations client: $e');
      return null;
    }
  }

  // Méthode pour créer un nouveau client à partir des informations extraites
  Future<Customer?> createCustomerFromInfo(Map<String, dynamic> info) async {
    try {
      // Vérifier si le client existe déjà par téléphone ou email
      if (info['phone'] != null) {
        final existingCustomer = await customerService.getCustomerByPhone(
          info['phone'],
        );
        if (existingCustomer != null) {
          return existingCustomer;
        }
      }

      if (info['email'] != null) {
        final existingCustomer = await customerService.getCustomerByEmail(
          info['email'],
        );
        if (existingCustomer != null) {
          return existingCustomer;
        }
      }

      // Créer un nouveau client
      final customer = Customer(
        id: const Uuid().v4(),
        name: info['name'],
        phone: info['phone'],
        email: info['email'],
        source: info['source'],
        createdAt: DateTime.now(),
        lastContact: DateTime.now(),
        status: CustomerStatus.lead,
        leadQualification: LeadQualification.cold,
        notes: info['notes'],
        tags: List<String>.from(info['tags'] ?? []),
      );

      // Ajouter le client à la base de données
      await customerService.addCustomer(customer);

      return customer;
    } catch (e) {
      debugPrint('Erreur lors de la création du client: $e');
      return null;
    }
  }

  // Méthode pour analyser un message et créer un client si nécessaire
  Future<Customer?> processMessageForCustomerCreation(String message) async {
    // Extraire les informations client du message
    final extractedInfo = await extractCustomerInfo(message);

    // Si des informations ont été extraites, créer un client
    if (extractedInfo != null && extractedInfo.isNotEmpty) {
      return await createCustomerFromInfo(extractedInfo);
    }

    return null;
  }

  // Méthode pour générer une réponse qui confirme la création du client
  Future<String> generateCustomerCreationResponse(Customer customer) async {
    const systemPrompt = '''
Tu es un assistant qui confirme la création d'un nouveau client dans le système CRM.
Réponds de manière professionnelle et amicale pour confirmer que le client a été ajouté.
Inclus les détails du client dans ta réponse.
''';

    final customerInfo = '''
Nom: ${customer.name}
Téléphone: ${customer.phone ?? 'Non spécifié'}
Email: ${customer.email ?? 'Non spécifié'}
Source: ${customer.source ?? 'Non spécifiée'}
Statut: Prospect
Tags: ${customer.tags.isEmpty ? 'Aucun' : customer.tags.join(', ')}
''';

    final aiMessage = AIMessage(
      role: AIMessageRole.user,
      content:
          'Un nouveau client a été créé avec les informations suivantes:\n$customerInfo',
    );

    return await aiService.sendMessage([
      aiMessage,
    ], overrideSystemPrompt: systemPrompt);
  }
}
