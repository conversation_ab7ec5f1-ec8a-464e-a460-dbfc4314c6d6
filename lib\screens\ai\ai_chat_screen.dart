import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/ai/ai_message.dart';
import '../../services/ai/ai_service.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class AIChatScreen extends ConsumerStatefulWidget {
  const AIChatScreen({super.key});

  @override
  ConsumerState<AIChatScreen> createState() => _AIChatScreenState();
}

class _AIChatScreenState extends ConsumerState<AIChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<AIMessage> _messages = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Méthode pour envoyer un message
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    setState(() {
      _messages.add(AIMessage(role: AIMessageRole.user, content: message));
      _isLoading = true;
      _messageController.clear();
    });

    // Faire défiler vers le bas
    _scrollToBottom();

    try {
      // Obtenir le service AI
      final aiService = ref.read(aiServiceProvider);

      // Envoyer le message et obtenir la réponse
      final response = await aiService.sendMessage(_messages);

      // Ajouter la réponse à la liste des messages
      setState(() {
        _messages.add(
          AIMessage(role: AIMessageRole.assistant, content: response),
        );
        _isLoading = false;
      });

      // Faire défiler vers le bas
      _scrollToBottom();
    } catch (e) {
      // Gérer l'erreur
      setState(() {
        _messages.add(
          AIMessage(
            role: AIMessageRole.assistant,
            content: 'Désolé, une erreur s\'est produite: $e',
          ),
        );
        _isLoading = false;
      });

      // Faire défiler vers le bas
      _scrollToBottom();
    }
  }

  // Méthode pour faire défiler vers le bas
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Assistant IA'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.push('/ai/settings');
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child: Column(
          children: [
            // Liste des messages
            Expanded(
              child:
                  _messages.isEmpty
                      ? Center(
                        child: Text(
                          'Envoyez un message pour commencer la conversation',
                          style: TextStyle(
                            color: Colors.white.withValues(
                              alpha: 179,
                            ), // 0.7 * 255 ≈ 179
                          ),
                        ),
                      )
                      : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _messages.length + (_isLoading ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == _messages.length) {
                            // Indicateur de chargement
                            return Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: primaryColor.withValues(
                                      alpha: 77,
                                    ), // 0.3 * 255 ≈ 77
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              primaryColor,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Réflexion en cours...',
                                      style: TextStyle(color: primaryColor),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }

                          final message = _messages[index];
                          final isUser = message.role == AIMessageRole.user;

                          return Align(
                            alignment:
                                isUser
                                    ? Alignment.centerRight
                                    : Alignment.centerLeft,
                            child: Container(
                              margin: const EdgeInsets.symmetric(vertical: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color:
                                    isUser
                                        ? secondaryColor.withValues(
                                          alpha: 51,
                                        ) // 0.2 * 255 ≈ 51
                                        : Colors.black.withValues(
                                          alpha: 153,
                                        ), // 0.6 * 255 ≈ 153
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color:
                                      isUser
                                          ? secondaryColor.withValues(
                                            alpha: 77,
                                          ) // 0.3 * 255 ≈ 77
                                          : primaryColor.withValues(
                                            alpha: 77,
                                          ), // 0.3 * 255 ≈ 77
                                ),
                              ),
                              constraints: BoxConstraints(
                                maxWidth:
                                    MediaQuery.of(context).size.width * 0.8,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    isUser ? 'Vous' : 'Assistant',
                                    style: TextStyle(
                                      color:
                                          isUser
                                              ? secondaryColor
                                              : primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    message.content,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
            ),

            // Zone de saisie
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: NeonTextField(
                      controller: _messageController,
                      labelText: 'Message',
                      hintText: 'Tapez votre message...',
                      maxLines: 3,
                      minLines: 1,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  NeonButton(
                    icon: Icons.send,
                    text: 'Envoyer',
                    color: primaryColor,
                    isLoading: _isLoading,
                    onPressed: () {
                      if (!_isLoading) {
                        _sendMessage();
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
