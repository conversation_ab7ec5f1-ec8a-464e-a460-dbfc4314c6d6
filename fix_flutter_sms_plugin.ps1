Write-Host "Fixing flutter_sms FlutterSmsPlugin.kt..."

$ktFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\src\main\kotlin\com\example\flutter_sms\FlutterSmsPlugin.kt"

if (-not (Test-Path $ktFile)) {
    Write-Host "File not found: $ktFile"
    exit 1
}

Write-Host "Original FlutterSmsPlugin.kt content:"
Get-Content $ktFile

Write-Host ""
Write-Host "Fixing Registrar reference in FlutterSmsPlugin.kt..."

$content = Get-Content $ktFile -Raw
$updatedContent = $content -replace "import io.flutter.plugin.common.PluginRegistry.Registrar", "import io.flutter.plugin.common.PluginRegistry.Registrar"
$updatedContent = $updatedContent -replace "companion object {
    @JvmStatic
    fun registerWith(registrar: Registrar) {
      val channel = MethodChannel(registrar.messenger(), \"flutter_sms\")
      channel.setMethodCallHandler(FlutterSmsPlugin(registrar.activity()))
    }
  }", "companion object {
    @JvmStatic
    fun registerWith(registrar: io.flutter.plugin.common.PluginRegistry.Registrar) {
      val channel = MethodChannel(registrar.messenger(), \"flutter_sms\")
      channel.setMethodCallHandler(FlutterSmsPlugin(registrar.activity()))
    }
  }"

$updatedContent | Set-Content $ktFile

Write-Host ""
Write-Host "Modified FlutterSmsPlugin.kt content:"
Get-Content $ktFile

Write-Host ""
Write-Host "Fix completed!"
