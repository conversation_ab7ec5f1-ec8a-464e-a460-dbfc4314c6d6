// Modèle de ticket de support

// Type de ticket de support
enum SupportTicketType {
  orderTracking, // Suivi de commande
  returnRefund, // Retour/Remboursement
  productIssue, // Problème avec un produit
  technicalSupport, // Assistance technique
  complaint, // Réclamation
  generalInquiry, // Demande générale
}

// Statut du ticket
enum SupportTicketStatus {
  open, // Ouvert
  inProgress, // En cours
  pending, // En attente
  resolved, // Résolu
  closed, // Fermé
}

// Priorité du ticket
enum SupportTicketPriority {
  low, // Basse
  medium, // Moyenne
  high, // Haute
  urgent, // Urgente
}

class SupportTicket {
  final String id;
  final String customerId;
  final String customerName;
  final String? orderId;
  final SupportTicketType type;
  final SupportTicketStatus status;
  final SupportTicketPriority priority;
  final String subject;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? assignedTo;
  final String? conversationId;
  final List<String>? attachments;

  SupportTicket({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.orderId,
    required this.type,
    required this.status,
    required this.priority,
    required this.subject,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    this.assignedTo,
    this.conversationId,
    this.attachments,
  });

  // Créer une copie avec des modifications
  SupportTicket copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? orderId,
    SupportTicketType? type,
    SupportTicketStatus? status,
    SupportTicketPriority? priority,
    String? subject,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? assignedTo,
    String? conversationId,
    List<String>? attachments,
  }) {
    return SupportTicket(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      orderId: orderId ?? this.orderId,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      subject: subject ?? this.subject,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedTo: assignedTo ?? this.assignedTo,
      conversationId: conversationId ?? this.conversationId,
      attachments: attachments ?? this.attachments,
    );
  }

  // Conversion depuis JSON
  factory SupportTicket.fromJson(Map<String, dynamic> json) {
    return SupportTicket(
      id: json['id'] as String,
      customerId: json['customer_id'] as String,
      customerName: json['customer_name'] as String,
      orderId: json['order_id'] as String?,
      type: SupportTicketType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SupportTicketType.generalInquiry,
      ),
      status: SupportTicketStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SupportTicketStatus.open,
      ),
      priority: SupportTicketPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => SupportTicketPriority.medium,
      ),
      subject: json['subject'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      assignedTo: json['assigned_to'] as String?,
      conversationId: json['conversation_id'] as String?,
      attachments:
          json['attachments'] != null
              ? List<String>.from(json['attachments'] as List)
              : null,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'order_id': orderId,
      'type': type.name,
      'status': status.name,
      'priority': priority.name,
      'subject': subject,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'assigned_to': assignedTo,
      'conversation_id': conversationId,
      'attachments': attachments,
    };
  }

  @override
  String toString() {
    return 'SupportTicket{id: $id, subject: $subject, status: ${status.name}}';
  }
}
