import '../models/product/product.dart';

class ProductData {
  // Liste des produits avec les prix corrects
  static List<Product> getProducts() {
    final now = DateTime.now();

    return [
      Product(
        id: '1',
        name: 'VIP1',
        description: 'Forfait VIP1',
        price: 6000, // 6000 FCFA
        category: 'Forfaits',
        quantity: 25,
        stockStatus: StockStatus.inStock,
        sku: 'VIP1-001',
        barcode: '123456789012',
        images: ['assets/images/products/vip1.jpg'],
        attributes: {
          'type': 'Forfait',
          'durée': '1 mois',
        },
        isActive: true,
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now,
        popularity: 10,
      ),
      Product(
        id: '2',
        name: 'Premium',
        description: 'Forfait Premium',
        price: 8000, // 8000 FCFA
        category: 'Forfaits',
        quantity: 20,
        stockStatus: StockStatus.inStock,
        sku: 'PREMIUM-001',
        barcode: '234567890123',
        images: ['assets/images/products/premium.jpg'],
        attributes: {
          'type': 'Forfait',
          'durée': '1 mois',
        },
        isActive: true,
        createdAt: now.subtract(const Duration(days: 45)),
        updatedAt: now.subtract(const Duration(days: 5)),
        popularity: 8,
      ),
      Product(
        id: '3',
        name: 'Premium Magsafe',
        description: 'Forfait Premium Magsafe',
        price: 10000, // 10000 FCFA
        category: 'Forfaits',
        quantity: 15,
        stockStatus: StockStatus.inStock,
        sku: 'PREMIUM-MAGSAFE-001',
        barcode: '345678901234',
        images: ['assets/images/products/premium_magsafe.jpg'],
        attributes: {
          'type': 'Forfait',
          'durée': '1 mois',
          'compatibilité': 'Magsafe',
        },
        isActive: true,
        createdAt: now.subtract(const Duration(days: 60)),
        updatedAt: now.subtract(const Duration(days: 2)),
        popularity: 9,
      ),
      Product(
        id: '4',
        name: 'Fullpicture',
        description: 'Forfait Fullpicture',
        price: 4500, // 4500 FCFA
        category: 'Forfaits',
        quantity: 30,
        stockStatus: StockStatus.inStock,
        sku: 'FULLPICTURE-001',
        barcode: '456789012345',
        images: ['assets/images/products/fullpicture.jpg'],
        attributes: {
          'type': 'Forfait',
          'durée': '1 mois',
        },
        isActive: true,
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now,
        popularity: 7,
      ),
    ];
  }
}
