import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/register_screen.dart';
import '../screens/customers/customers_screen.dart';
import '../screens/customers/customer_detail_screen.dart';
import '../screens/customers/add_customer_screen.dart';
import '../screens/dashboard/dashboard_screen.dart';
import '../screens/inventory/inventory_screen.dart';
import '../screens/inventory/product_detail_screen.dart';
import '../screens/inventory/product_form_screen.dart';
import '../screens/inventory/stock_alerts_screen.dart';
import '../screens/inventory/categories_screen.dart';
import '../screens/knowledge/knowledge_screen.dart';
import '../screens/messaging/messaging_screen.dart';
import '../screens/profile/profile_screen.dart';
import '../screens/settings/ai_settings_screen.dart' as settings;
import '../screens/settings/messaging_settings_screen.dart';
import '../screens/settings/auto_response_screen.dart';
import '../screens/settings/ai_performance_screen.dart';
import '../screens/settings/user_management_screen.dart';
import '../screens/settings/telegram_settings_screen.dart';
import '../screens/settings/telegram_webhook_screen.dart';
import '../screens/settings/theme_manager_screen.dart';
import '../screens/settings/theme_settings_screen.dart';
import '../screens/settings/whatsapp_settings_screen.dart';
import '../screens/settings/settings_main_screen.dart';
import '../screens/settings/sync_settings_screen.dart';
import '../screens/debug/avatar_debug_screen.dart';
import '../screens/messaging/send_sms_screen.dart';
import '../screens/messaging/sms_history_screen.dart';
import '../screens/splash/splash_screen.dart';
import '../screens/tasks/tasks_screen.dart';
import '../screens/tasks/task_detail_screen.dart';
import '../screens/tasks/add_task_screen.dart';
import '../screens/ai/ai_settings_screen.dart';
import '../screens/ai/gemini_assistant_screen.dart';
import '../screens/ai/gemini_settings_screen.dart';
import '../screens/ai/ai_tools_screen.dart';
import '../services/auth_service.dart';
import '../widgets/layout/neon_app_scaffold.dart';

final routerProvider = Provider<GoRouter>((ref) {
  final authService = ref.watch(authServiceProvider);

  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final isLoggedIn = authService.isLoggedIn;
      final isLoginRoute = state.matchedLocation == '/login';
      final isRegisterRoute = state.matchedLocation == '/register';
      final isSplashRoute = state.matchedLocation == '/';

      // Si l'utilisateur est sur la page de splash, laissez-le
      if (isSplashRoute) return null;

      // Si l'utilisateur n'est pas connecté et n'est pas sur la page de connexion ou d'inscription, redirigez-le vers la connexion
      if (!isLoggedIn && !isLoginRoute && !isRegisterRoute) return '/login';

      // Si l'utilisateur est connecté et sur la page de connexion ou d'inscription, redirigez-le vers le tableau de bord
      if (isLoggedIn && (isLoginRoute || isRegisterRoute)) return '/dashboard';

      // Pas de redirection nécessaire
      return null;
    },
    routes: [
      GoRoute(path: '/', builder: (context, state) => const SplashScreen()),
      GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      ShellRoute(
        builder: (context, state, child) => NeonAppScaffold(child: child),
        routes: [
          GoRoute(
            path: '/dashboard',
            builder: (context, state) => const DashboardScreen(),
          ),
          GoRoute(
            path: '/messaging',
            builder: (context, state) => const MessagingScreen(),
          ),
          GoRoute(
            path: '/customers',
            builder: (context, state) => const CustomersScreen(),
            routes: [
              GoRoute(
                path: 'add',
                builder: (context, state) => const AddCustomerScreen(),
              ),
              GoRoute(
                path: ':id',
                builder:
                    (context, state) => CustomerDetailScreen(
                      customerId: state.pathParameters['id']!,
                    ),
              ),
            ],
          ),
          GoRoute(
            path: '/tasks',
            builder: (context, state) => const TasksScreen(),
            routes: [
              GoRoute(
                path: 'add',
                builder: (context, state) => const AddTaskScreen(),
              ),
              GoRoute(
                path: 'details/:id',
                builder:
                    (context, state) =>
                        TaskDetailScreen(taskId: state.pathParameters['id']!),
              ),
              GoRoute(
                path: 'edit/:id',
                builder:
                    (context, state) =>
                        AddTaskScreen(taskId: state.pathParameters['id']!),
              ),
            ],
          ),
          GoRoute(
            path: '/inventory',
            builder: (context, state) => const InventoryScreen(),
            routes: [
              GoRoute(
                path: 'add',
                builder: (context, state) => const ProductFormScreen(),
              ),
              GoRoute(
                path: 'categories',
                builder: (context, state) => const CategoriesScreen(),
              ),
              GoRoute(
                path: 'alerts',
                builder: (context, state) => const StockAlertsScreen(),
              ),
              GoRoute(
                path: 'product/:id',
                builder:
                    (context, state) => ProductDetailScreen(
                      productId: state.pathParameters['id']!,
                    ),
              ),
              GoRoute(
                path: 'edit/:id',
                builder:
                    (context, state) => ProductFormScreen(
                      productId: state.pathParameters['id']!,
                    ),
              ),
            ],
          ),
          GoRoute(
            path: '/knowledge',
            builder: (context, state) => const KnowledgeScreen(),
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsMainScreen(),
            routes: [
              GoRoute(
                path: 'ai',
                builder: (context, state) => const settings.AISettingsScreen(),
              ),
              GoRoute(
                path: 'messaging',
                builder: (context, state) => const MessagingSettingsScreen(),
              ),
              GoRoute(
                path: 'telegram',
                builder: (context, state) => const TelegramSettingsScreen(),
              ),
              GoRoute(
                path: 'telegram/webhook',
                builder: (context, state) => const TelegramWebhookScreen(),
              ),
              GoRoute(
                path: 'whatsapp',
                builder: (context, state) => const WhatsAppSettingsScreen(),
              ),
              GoRoute(
                path: 'sync',
                builder: (context, state) => const SyncSettingsScreen(),
              ),
              GoRoute(
                path: 'sms',
                builder: (context, state) => const SmsHistoryScreen(),
              ),
              GoRoute(
                path: 'sms/send',
                builder: (context, state) {
                  final contactDetails = state.extra as Map<String, dynamic>?;
                  return SendSmsScreen(
                    initialPhoneNumber: contactDetails?['phone'] as String?,
                    customerId: contactDetails?['id'] as String?,
                    customerName: contactDetails?['name'] as String?,
                  );
                },
              ),
              GoRoute(
                path: 'auto-response',
                builder: (context, state) => const AutoResponseScreen(),
              ),
              GoRoute(
                path: 'ai-performance',
                builder: (context, state) => const AIPerformanceScreen(),
              ),
              GoRoute(
                path: 'user-management',
                builder: (context, state) => const UserManagementScreen(),
              ),
              GoRoute(
                path: 'theme',
                builder: (context, state) => const ThemeSettingsScreen(),
              ),
              GoRoute(
                path: 'theme-manager',
                builder: (context, state) => const ThemeManagerScreen(),
              ),
              GoRoute(
                path: 'avatar-debug',
                builder: (context, state) => const AvatarDebugScreen(),
              ),
            ],
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
          ),
          GoRoute(
            path: '/ai-chat',
            builder:
                (context, state) => const Scaffold(
                  body: Center(child: Text('AI Chat - En développement')),
                ),
          ),
          GoRoute(
            path: '/ai-settings',
            builder: (context, state) => const AISettingsScreen(),
          ),
          GoRoute(
            path: '/gemini-assistant',
            builder: (context, state) => const GeminiAssistantScreen(),
          ),
          GoRoute(
            path: '/gemini-settings',
            builder: (context, state) => const GeminiSettingsScreen(),
          ),
          GoRoute(
            path: '/ai-tools',
            builder: (context, state) => const AIToolsScreen(),
          ),
        ],
      ),
    ],
  );
});
