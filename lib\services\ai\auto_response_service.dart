import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/ai/ai_message.dart';
import 'ai_service.dart';

// Provider pour le service de réponses automatiques
final autoResponseServiceProvider = Provider<AutoResponseService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  return AutoResponseService(aiService);
});

// Provider pour les déclencheurs de réponses automatiques
final autoResponseTriggersProvider = StateNotifierProvider<
  AutoResponseTriggersNotifier,
  List<AutoResponseTrigger>
>((ref) {
  return AutoResponseTriggersNotifier();
});

// Classe pour les déclencheurs de réponses automatiques
class AutoResponseTrigger {
  final String id;
  final String name;
  final List<String> keywords;
  final String? response;
  final bool useAI;
  final String? aiPrompt;
  final bool isEnabled;

  AutoResponseTrigger({
    required this.id,
    required this.name,
    required this.keywords,
    this.response,
    required this.useAI,
    this.aiPrompt,
    required this.isEnabled,
  });

  // Vérifier si le message correspond à ce déclencheur
  bool matchesMessage(String message) {
    final lowerMessage = message.toLowerCase();
    return keywords.any(
      (keyword) => lowerMessage.contains(keyword.toLowerCase()),
    );
  }

  AutoResponseTrigger copyWith({
    String? id,
    String? name,
    List<String>? keywords,
    String? response,
    bool? useAI,
    String? aiPrompt,
    bool? isEnabled,
  }) {
    return AutoResponseTrigger(
      id: id ?? this.id,
      name: name ?? this.name,
      keywords: keywords ?? this.keywords,
      response: response ?? this.response,
      useAI: useAI ?? this.useAI,
      aiPrompt: aiPrompt ?? this.aiPrompt,
      isEnabled: isEnabled ?? this.isEnabled,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'keywords': keywords,
      'response': response,
      'useAI': useAI,
      'aiPrompt': aiPrompt,
      'isEnabled': isEnabled,
    };
  }

  factory AutoResponseTrigger.fromMap(Map<String, dynamic> map) {
    return AutoResponseTrigger(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      keywords: List<String>.from(map['keywords'] ?? []),
      response: map['response'],
      useAI: map['useAI'] ?? false,
      aiPrompt: map['aiPrompt'],
      isEnabled: map['isEnabled'] ?? true,
    );
  }

  String toJson() => json.encode(toMap());

  factory AutoResponseTrigger.fromJson(String source) =>
      AutoResponseTrigger.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AutoResponseTrigger(id: $id, name: $name, keywords: $keywords, response: $response, useAI: $useAI, aiPrompt: $aiPrompt, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AutoResponseTrigger &&
        other.id == id &&
        other.name == name &&
        listEquals(other.keywords, keywords) &&
        other.response == response &&
        other.useAI == useAI &&
        other.aiPrompt == aiPrompt &&
        other.isEnabled == isEnabled;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        keywords.hashCode ^
        response.hashCode ^
        useAI.hashCode ^
        aiPrompt.hashCode ^
        isEnabled.hashCode;
  }
}

// Notifier pour les déclencheurs de réponses automatiques
class AutoResponseTriggersNotifier
    extends StateNotifier<List<AutoResponseTrigger>> {
  AutoResponseTriggersNotifier() : super([]) {
    _loadTriggers();
  }

  Future<void> _loadTriggers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final triggersJson = prefs.getStringList('auto_response_triggers');

      if (triggersJson != null) {
        state =
            triggersJson
                .map((json) => AutoResponseTrigger.fromJson(json))
                .toList();
      } else {
        // Initialiser avec des déclencheurs par défaut
        state = [
          AutoResponseTrigger(
            id: '1',
            name: 'Salutations',
            keywords: ['bonjour', 'salut', 'hello', 'hi', 'hey'],
            response: 'Bonjour ! Comment puis-je vous aider aujourd\'hui ?',
            useAI: false,
            isEnabled: true,
          ),
          AutoResponseTrigger(
            id: '2',
            name: 'Remerciements',
            keywords: ['merci', 'thanks', 'thank you'],
            response:
                'De rien ! N\'hésitez pas si vous avez d\'autres questions.',
            useAI: false,
            isEnabled: true,
          ),
          AutoResponseTrigger(
            id: '3',
            name: 'Questions sur les produits',
            keywords: [
              'produit',
              'product',
              'article',
              'item',
              'prix',
              'price',
              'coût',
              'cost',
            ],
            useAI: true,
            aiPrompt:
                'Le client pose une question sur nos produits. Réponds de manière concise et précise.',
            isEnabled: true,
          ),
        ];
        await saveTriggers();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des déclencheurs: $e');
    }
  }

  Future<void> saveTriggers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final triggersJson = state.map((trigger) => trigger.toJson()).toList();
      await prefs.setStringList('auto_response_triggers', triggersJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des déclencheurs: $e');
    }
  }

  void addTrigger(AutoResponseTrigger trigger) {
    state = [...state, trigger];
    saveTriggers();
  }

  void updateTrigger(AutoResponseTrigger trigger) {
    state =
        state.map((t) {
          if (t.id == trigger.id) {
            return trigger;
          }
          return t;
        }).toList();
    saveTriggers();
  }

  void removeTrigger(String triggerId) {
    state = state.where((t) => t.id != triggerId).toList();
    saveTriggers();
  }

  void toggleTrigger(String triggerId) {
    state =
        state.map((t) {
          if (t.id == triggerId) {
            return t.copyWith(isEnabled: !t.isEnabled);
          }
          return t;
        }).toList();
    saveTriggers();
  }
}

// Service principal pour les réponses automatiques
class AutoResponseService {
  final AIService _aiService;

  AutoResponseService(this._aiService);

  // Vérifier si un message correspond à un déclencheur et générer une réponse
  Future<String?> processMessage(
    String message,
    List<AutoResponseTrigger> triggers,
  ) async {
    try {
      // Trouver le premier déclencheur qui correspond au message
      final matchingTrigger = triggers.firstWhere(
        (trigger) => trigger.isEnabled && trigger.matchesMessage(message),
        orElse: () => throw Exception('No matching trigger found'),
      );

      // Si le déclencheur utilise l'IA
      if (matchingTrigger.useAI) {
        final aiMessages = [
          AIMessage(role: AIMessageRole.user, content: message),
        ];

        // Utiliser le prompt personnalisé si disponible
        final systemPrompt = matchingTrigger.aiPrompt;

        return await _aiService.sendMessage(
          aiMessages,
          overrideSystemPrompt: systemPrompt,
        );
      } else {
        // Sinon, utiliser la réponse prédéfinie
        return matchingTrigger.response;
      }
    } catch (e) {
      // Aucun déclencheur ne correspond au message
      return null;
    }
  }
}
