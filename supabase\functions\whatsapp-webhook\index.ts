import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Gérer les requêtes OPTIONS (CORS preflight)
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Vérifier que la requête est une POST
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Méthode non autorisée' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Récupérer les données du formulaire
    const formData = await req.formData()
    const payload: Record<string, string> = {}
    
    // Convertir FormData en objet
    for (const [key, value] of formData.entries()) {
      payload[key] = value.toString()
    }

    // Créer un client Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Extraire les informations du message
    const from = payload.From || ''
    const body = payload.Body || ''
    const messageSid = payload.MessageSid || ''
    
    // Nettoyer le numéro de téléphone
    const contactId = from.replace('whatsapp:', '')
    
    // Enregistrer le message dans la base de données
    const { data, error } = await supabase
      .from('messages')
      .insert({
        id: messageSid,
        contact_id: contactId,
        content: body,
        is_user: false,
        timestamp: new Date().toISOString(),
        channel: 'whatsapp',
        status: 'delivered',
      })
      .select()
    
    if (error) {
      console.error('Erreur lors de l\'enregistrement du message:', error)
    }
    
    // Vérifier si nous devons envoyer une réponse automatique
    let responseMessage = ''
    
    // Exemple: Si le message contient "prix", envoyer les informations de prix
    if (body.toLowerCase().includes('prix')) {
      responseMessage = 'Nos prix commencent à 5000 FCFA. Consultez notre catalogue pour plus d\'informations.'
    }
    
    // Répondre à Twilio avec un TwiML
    const twimlResponse = responseMessage 
      ? `<?xml version="1.0" encoding="UTF-8"?><Response><Message>${responseMessage}</Message></Response>`
      : '<?xml version="1.0" encoding="UTF-8"?><Response></Response>'
    
    return new Response(twimlResponse, {
      headers: { ...corsHeaders, 'Content-Type': 'text/xml' },
    })
  } catch (error) {
    console.error('Erreur lors du traitement du webhook:', error)
    
    return new Response(JSON.stringify({ error: 'Erreur interne du serveur' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})