import 'message.dart';

class Conversation {
  final String id;
  final String participantId;
  final String participantName;
  final String? participantAvatar;
  final MessageChannel channel;
  final DateTime createdAt;
  final DateTime lastMessageTime;
  final String lastMessage;
  final int unreadCount;
  final bool isFollowedUp;
  final bool isPinned;
  final bool isArchived;
  final bool isBlocked;
  final Map<String, dynamic>? metadata;

  Conversation({
    required this.id,
    required this.participantId,
    required this.participantName,
    this.participantAvatar,
    required this.channel,
    required this.createdAt,
    required this.lastMessageTime,
    required this.lastMessage,
    required this.unreadCount,
    this.isFollowedUp = false,
    this.isPinned = false,
    this.isArchived = false,
    this.isBlocked = false,
    this.metadata,
  });

  // Créer une copie avec des modifications
  Conversation copyWith({
    String? id,
    String? participantId,
    String? participantName,
    String? participantAvatar,
    MessageChannel? channel,
    DateTime? createdAt,
    DateTime? lastMessageTime,
    String? lastMessage,
    int? unreadCount,
    bool? isFollowedUp,
    bool? isPinned,
    bool? isArchived,
    bool? isBlocked,
    Map<String, dynamic>? metadata,
  }) {
    return Conversation(
      id: id ?? this.id,
      participantId: participantId ?? this.participantId,
      participantName: participantName ?? this.participantName,
      participantAvatar: participantAvatar ?? this.participantAvatar,
      channel: channel ?? this.channel,
      createdAt: createdAt ?? this.createdAt,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      isFollowedUp: isFollowedUp ?? this.isFollowedUp,
      isPinned: isPinned ?? this.isPinned,
      isArchived: isArchived ?? this.isArchived,
      isBlocked: isBlocked ?? this.isBlocked,
      metadata: metadata ?? this.metadata,
    );
  }

  // Conversion depuis JSON
  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'] as String,
      participantId: json['participant_id'] as String,
      participantName: json['participant_name'] as String,
      participantAvatar: json['participant_avatar'] as String?,
      channel: MessageChannel.values.firstWhere(
        (e) => e.name == json['channel'],
        orElse: () => MessageChannel.whatsapp,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastMessageTime: DateTime.parse(json['last_message_time'] as String),
      lastMessage: json['last_message'] as String,
      unreadCount: json['unread_count'] as int,
      isFollowedUp: json['is_followed_up'] as bool? ?? false,
      isPinned: json['is_pinned'] as bool? ?? false,
      isArchived: json['is_archived'] as bool? ?? false,
      isBlocked: json['is_blocked'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participant_id': participantId,
      'participant_name': participantName,
      'participant_avatar': participantAvatar,
      'channel': channel.name,
      'created_at': createdAt.toIso8601String(),
      'last_message_time': lastMessageTime.toIso8601String(),
      'last_message': lastMessage,
      'unread_count': unreadCount,
      'is_followed_up': isFollowedUp,
      'is_pinned': isPinned,
      'is_archived': isArchived,
      'is_blocked': isBlocked,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'Conversation{id: $id, participantName: $participantName, channel: ${channel.name}}';
  }
}
