import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

// Provider pour le service de notification
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

class NotificationService {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  // Constructeur par défaut
  NotificationService();

  // Initialiser le service de notification
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialiser le fuseau horaire
      tz.initializeTimeZones();

      // Configurer les paramètres d'initialisation pour Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Configurer les paramètres d'initialisation pour iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // Configurer les paramètres d'initialisation pour toutes les plateformes
      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      // Initialiser le plugin de notification
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Vérifier les permissions de notification
      await _checkNotificationPermissions();

      _isInitialized = true;
      debugPrint('Service de notification initialisé avec succès');
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de notification: $e',
      );
    }
  }

  // Vérifier les permissions de notification (version simplifiée)
  Future<void> _checkNotificationPermissions() async {
    if (Platform.isAndroid) {
      // Version simplifiée sans le plugin notification_permissions
      debugPrint(
        'Vérification des permissions de notification désactivée temporairement',
      );
      // Nous supposons que les permissions sont accordées
    }
  }

  // Gérer le tap sur une notification
  void _onNotificationTapped(NotificationResponse response) {
    // Traiter le payload de la notification
    final String? payload = response.payload;
    if (payload != null) {
      debugPrint('Notification tapped with payload: $payload');

      // Stocker le payload pour que l'application puisse le récupérer
      _storeNotificationPayload(payload);
    }
  }

  // Stocker le payload de la notification
  Future<void> _storeNotificationPayload(String payload) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_notification_payload', payload);
      await prefs.setInt(
        'last_notification_time',
        DateTime.now().millisecondsSinceEpoch,
      );
      debugPrint('Payload de notification stocké: $payload');
    } catch (e) {
      debugPrint('Erreur lors du stockage du payload de notification: $e');
    }
  }

  // Récupérer le dernier payload de notification
  Future<String?> getLastNotificationPayload() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('last_notification_payload');
    } catch (e) {
      debugPrint(
        'Erreur lors de la récupération du payload de notification: $e',
      );
      return null;
    }
  }

  // Récupérer le temps du dernier payload de notification
  Future<DateTime?> getLastNotificationTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt('last_notification_time');
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      debugPrint('Erreur lors de la récupération du temps de notification: $e');
      return null;
    }
  }

  // Afficher une notification
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    int id = 0,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Configurer les détails de la notification pour Android
      AndroidNotificationDetails androidPlatformChannelSpecifics =
          const AndroidNotificationDetails(
            'hcp_crm_channel',
            'HCP CRM Notifications',
            channelDescription: 'Notifications pour l\'application HCP CRM',
            importance: Importance.max,
            priority: Priority.high,
            showWhen: true,
          );

      // Configurer les détails de la notification pour iOS
      DarwinNotificationDetails iOSPlatformChannelSpecifics =
          const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      // Configurer les détails de la notification pour toutes les plateformes
      NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Afficher la notification
      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      debugPrint('Notification affichée: $title - $body');
    } catch (e) {
      debugPrint('Erreur lors de l\'affichage de la notification: $e');
    }
  }

  // Planifier une notification
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int id = 0,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Configurer les détails de la notification pour Android
      AndroidNotificationDetails androidPlatformChannelSpecifics =
          const AndroidNotificationDetails(
            'hcp_crm_scheduled_channel',
            'HCP CRM Scheduled Notifications',
            channelDescription:
                'Notifications planifiées pour l\'application HCP CRM',
            importance: Importance.max,
            priority: Priority.high,
            showWhen: true,
          );

      // Configurer les détails de la notification pour iOS
      DarwinNotificationDetails iOSPlatformChannelSpecifics =
          const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          );

      // Configurer les détails de la notification pour toutes les plateformes
      NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Planifier la notification
      await _flutterLocalNotificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        platformChannelSpecifics,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
        payload: payload,
      );

      debugPrint(
        'Notification planifiée: $title - $body pour le ${scheduledDate.toString()}',
      );
    } catch (e) {
      debugPrint('Erreur lors de la planification de la notification: $e');
    }
  }

  // Annuler toutes les notifications
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
    debugPrint('Toutes les notifications ont été annulées');
  }

  // Annuler une notification spécifique
  Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
    debugPrint('Notification $id annulée');
  }
}
