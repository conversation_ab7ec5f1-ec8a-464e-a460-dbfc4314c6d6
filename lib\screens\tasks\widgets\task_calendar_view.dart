import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../../models/task/task.dart';
import '../../../theme/neon_theme.dart';

// Provider pour la date sélectionnée dans le calendrier
final selectedCalendarDateProvider = StateProvider<DateTime>(
  (ref) => DateTime.now(),
);

class TaskCalendarView extends ConsumerWidget {
  final List<Task> tasks;

  const TaskCalendarView({super.key, required this.tasks});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = ref.watch(selectedCalendarDateProvider);

    // Regrouper les tâches par date
    final tasksByDate = <DateTime, List<Task>>{};
    for (final task in tasks) {
      final date = DateTime(
        task.dueDate.year,
        task.dueDate.month,
        task.dueDate.day,
      );
      if (tasksByDate[date] == null) {
        tasksByDate[date] = [];
      }
      tasksByDate[date]!.add(task);
    }

    // Tâches pour la date sélectionnée
    final selectedDateTasks =
        tasksByDate[DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
        )] ??
        [];

    return Column(
      children: [
        // Calendrier
        Card(
          color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: TableCalendar(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: selectedDate,
              selectedDayPredicate: (day) {
                return isSameDay(selectedDate, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                ref.read(selectedCalendarDateProvider.notifier).state =
                    selectedDay;
              },
              calendarStyle: CalendarStyle(
                defaultTextStyle: const TextStyle(color: Colors.white),
                weekendTextStyle: const TextStyle(color: Colors.white70),
                outsideTextStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 77),
                ), // 0.3 * 255 ≈ 77
                selectedDecoration: const BoxDecoration(
                  color: NeonTheme.neonPurple,
                  shape: BoxShape.circle,
                ),
                todayDecoration: BoxDecoration(
                  color: NeonTheme.neonPurple.withValues(
                    alpha: 77,
                  ), // 0.3 * 255 ≈ 77
                  shape: BoxShape.circle,
                ),
                markerDecoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              headerStyle: const HeaderStyle(
                titleTextStyle: TextStyle(color: Colors.white, fontSize: 18),
                formatButtonVisible: false,
                leftChevronIcon: Icon(Icons.chevron_left, color: Colors.white),
                rightChevronIcon: Icon(
                  Icons.chevron_right,
                  color: Colors.white,
                ),
              ),
              daysOfWeekStyle: const DaysOfWeekStyle(
                weekdayStyle: TextStyle(color: Colors.white),
                weekendStyle: TextStyle(color: Colors.white70),
              ),
              eventLoader: (day) {
                return tasksByDate[DateTime(day.year, day.month, day.day)] ??
                    [];
              },
            ),
          ),
        ),

        // Liste des tâches pour la date sélectionnée
        Expanded(
          child:
              selectedDateTasks.isEmpty
                  ? Center(
                    child: Text(
                      'Aucune tâche pour le ${DateFormat('dd/MM/yyyy').format(selectedDate)}',
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: selectedDateTasks.length,
                    itemBuilder: (context, index) {
                      final task = selectedDateTasks[index];
                      return _buildTaskItem(context, task);
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildTaskItem(BuildContext context, Task task) {
    final Color priorityColor = _getPriorityColor(task.priority);
    final bool isOverdue = task.isOverdue;

    return Card(
      color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isOverdue ? Colors.red : priorityColor,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          context.push('/tasks/details/${task.id}');
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Indicateur de statut
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: _getStatusColor(task.status),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 16),

              // Informations de la tâche
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      task.title,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        decoration:
                            task.status == TaskStatus.completed
                                ? TextDecoration.lineThrough
                                : null,
                      ),
                    ),
                    if (task.assignedToName != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Assigné à: ${task.assignedToName}',
                          style: TextStyle(
                            color: Colors.white.withValues(
                              alpha: 179,
                            ), // 0.7 * 255 ≈ 179
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Heure de la tâche
              Text(
                DateFormat('HH:mm').format(task.dueDate),
                style: TextStyle(
                  color: isOverdue ? Colors.red : Colors.white,
                  fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.low:
        return Colors.green;
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
    }
  }
}
