import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:uuid/uuid.dart'; // Non utilisé
import '../../models/ai/ai_message.dart';
// import '../../models/messaging/message.dart'; // Non utilisé
// import '../../models/messaging/conversation.dart'; // Non utilisé
// import '../../models/invoice/invoice.dart'; // Non utilisé
// import '../../models/invoice/invoice_item.dart'; // Non utilisé
import '../ai/ai_service.dart';
import '../customer/customer_service.dart';
import '../messaging/messaging_service.dart';
// import '../invoice/invoice_service.dart'; // Service non disponible
import '../product/product_service.dart';

// Provider pour le service de génération de devis
final quoteGeneratorServiceProvider = Provider<QuoteGeneratorService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  final customerService = ref.watch(customerServiceProvider);
  final messagingService = ref.watch(messagingServiceProvider);
  // final invoiceService = ref.watch(invoiceServiceProvider); // Service non disponible
  final productService = ref.watch(productServiceProvider);

  return QuoteGeneratorService(
    aiService: aiService,
    customerService: customerService,
    messagingService: messagingService,
    // invoiceService: invoiceService, // Service non disponible
    productService: productService,
  );
});

class QuoteGeneratorService {
  final AIService aiService;
  final CustomerService customerService;
  final MessagingService messagingService;
  // final InvoiceService invoiceService; // Service non disponible
  final ProductService productService;

  QuoteGeneratorService({
    required this.aiService,
    required this.customerService,
    required this.messagingService,
    // required this.invoiceService, // Service non disponible
    required this.productService,
  });

  // Analyser un message pour détecter une demande de devis
  Future<bool> isQuoteRequest(String message) async {
    final prompt = '''
    Analyse le message suivant et détermine s'il s'agit d'une demande de devis ou de prix.
    Réponds uniquement par "OUI" ou "NON".

    Message: "$message"
    ''';

    final response = await aiService.generateText([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    return response.toLowerCase().contains('oui');
  }

  // Extraire les informations de produits d'un message
  Future<List<Map<String, dynamic>>> extractProductInfo(String message) async {
    try {
      // Récupérer tous les produits disponibles
      final allProducts = await productService.getProducts();

      // Construire une liste de produits pour le contexte
      String productsContext = 'Produits disponibles:\n';
      for (final product in allProducts) {
        productsContext +=
            '- ${product.name}: ${product.price} FCFA (${product.category})\n';
      }

      // Prompt pour extraire les informations de produits
      final prompt = '''
      Tu es un assistant spécialisé dans l'extraction d'informations de commande.

      $productsContext

      Analyse le message suivant et extrait les produits mentionnés, leurs quantités et toute spécification particulière.
      Si un produit n'est pas dans la liste mais semble être une coque de téléphone, essaie de le faire correspondre au produit le plus proche.

      Message: "$message"

      Réponds au format JSON comme ceci:
      [
        {
          "product_name": "nom du produit",
          "quantity": quantité (nombre),
          "specifications": "spécifications particulières (couleur, modèle, etc.)"
        },
        ...
      ]

      Si aucun produit n'est mentionné, renvoie un tableau vide [].
      ''';

      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      // Extraire le JSON de la réponse
      String jsonStr = response;
      if (response.contains('[') && response.contains(']')) {
        jsonStr = response.substring(
          response.indexOf('['),
          response.lastIndexOf(']') + 1,
        );
      }

      // Décoder le JSON
      final List<dynamic> extractedProducts = jsonDecode(jsonStr);
      return extractedProducts.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'extraction des informations de produits: $e',
      );
      return [];
    }
  }

  // Future<Invoice?> generateQuoteFromMessage(
  //   Message message,
  //   Conversation conversation,
  // ) async {
  //   try {
  //     // Vérifier si c'est une demande de devis
  //     final isQuote = await isQuoteRequest(message.content);
  //     if (!isQuote) return null;
  //
  //     // Récupérer le client
  //     final customer = await customerService.getCustomerByContactId(
  //       message.sender,
  //     );
  //     if (customer == null) return null;
  //
  //     // Extraire les informations de produits
  //     final extractedProducts = await extractProductInfo(message.content);
  //     if (extractedProducts.isEmpty) return null;
  //
  //     // Récupérer les produits correspondants
  //     final allProducts = await productService.getProducts();
  //     final List<InvoiceItem> invoiceItems = [];
  //
  //     for (final extractedProduct in extractedProducts) {
  //       final productName = extractedProduct['product_name'] as String;
  //       final quantity = (extractedProduct['quantity'] as num).toInt();
  //       final specifications = extractedProduct['specifications'] as String?;
  //
  //       // Trouver le produit correspondant
  //       final matchingProducts =
  //           allProducts
  //               .where(
  //                 (p) =>
  //                     p.name.toLowerCase().contains(productName.toLowerCase()),
  //               )
  //               .toList();
  //
  //       if (matchingProducts.isNotEmpty) {
  //         final product = matchingProducts.first;
  //
  //         // Créer un élément de facture
  //         final invoiceItem = InvoiceItem(
  //           id: const Uuid().v4(),
  //           productId: product.id,
  //           productName: product.name,
  //           quantity: quantity,
  //           unitPrice: product.price,
  //           total: product.price * quantity,
  //           description: specifications ?? '',
  //         );
  //
  //         invoiceItems.add(invoiceItem);
  //       }
  //     }
  //
  //     if (invoiceItems.isEmpty) return null;
  //
  //     // Calculer le total
  //     final total = invoiceItems.fold<double>(
  //       0,
  //       (sum, item) => sum + item.total,
  //     );
  //
  //     // Créer le devis
  //     final invoice = Invoice(
  //       id: const Uuid().v4(),
  //       customerId: customer.id,
  //       customerName: customer.name,
  //       customerEmail: customer.email,
  //       customerPhone: customer.phone,
  //       date: DateTime.now(),
  //       dueDate: DateTime.now().add(const Duration(days: 7)),
  //       items: invoiceItems,
  //       subtotal: total,
  //       tax: 0,
  //       discount: 0,
  //       total: total,
  //       notes: 'Devis généré automatiquement',
  //       status: InvoiceStatus.draft,
  //       type: InvoiceType.quote,
  //     );
  //
  //     // Enregistrer le devis
  //     // await invoiceService.addInvoice(invoice); // Functionality removed
  //
  //     return invoice;
  //   } catch (e) {
  //     debugPrint('Erreur lors de la génération du devis: $e');
  //     return null;
  //   }
  // }

  // // Envoyer un devis au client via la messagerie
  // Future<void> sendQuoteToCustomer(
  //   Invoice invoice,
  //   Conversation conversation,
  // ) async {
  //   try {
  //     // Générer un lien vers le devis
  //     // final quoteLink = await invoiceService.generateInvoiceLink(invoice.id); // Functionality removed
  //     const quoteLink = "#"; // Placeholder
  //
  //     // Préparer le contenu du message
  //     final messageContent = '''
  //     Voici le devis que vous avez demandé:
  //
  //     Devis N°: ${invoice.id.substring(0, 8).toUpperCase()}
  //     Date: ${_formatDate(invoice.date)}
  //     Validité: ${_formatDate(invoice.dueDate)}
  //
  //     ${invoice.items.map((item) => '- ${item.quantity}x ${item.productName} (${item.unitPrice} FCFA): ${item.total} FCFA').join('\n')}
  //
  //     Total: ${invoice.total} FCFA
  //
  //     Vous pouvez consulter et télécharger votre devis ici: $quoteLink
  //
  //     Pour confirmer votre commande, il vous suffit de répondre "Je confirme" à ce message.
  //     ''';
  //
  //     // Envoyer le message
  //     await messagingService.sendMessage(
  //       contactId: conversation.participantId,
  //       content: messageContent,
  //       channel: conversation.channel,
  //     );
  //
  //     // Mettre à jour la conversation
  //     await messagingService.updateConversation(
  //       conversation.copyWith(
  //         lastMessage: messageContent,
  //         lastMessageTime: DateTime.now(),
  //       ),
  //     );
  //   } catch (e) {
  //     debugPrint('Erreur lors de l\'envoi du devis: $e');
  //   }
  // }

  // // Vérifier si un message est une confirmation de commande
  // Future<bool> isOrderConfirmation(String message) async {
  //   final prompt = '''
  //   Analyse le message suivant et détermine s'il s'agit d'une confirmation de commande ou d'acceptation de devis.
  //   Réponds uniquement par "OUI" ou "NON".
  //
  //   Message: "$message"
  //   ''';
  //
  //   final response = await aiService.generateText([
  //     AIMessage(role: AIMessageRole.system, content: prompt),
  //   ]);
  //
  //   return response.toLowerCase().contains('oui');
  // }

  // // Convertir un devis en commande
  // Future<Invoice?> convertQuoteToOrder(String quoteId) async {
  //   try {
  //     // Récupérer le devis
  //     // final quote = await invoiceService.getInvoiceById(quoteId); // Functionality removed
  //     // if (quote == null || quote.type != InvoiceType.quote) { // Functionality removed
  //     //   return null;
  //     // }
  //
  //     // Créer une commande à partir du devis
  //     // final order = quote.copyWith( // Functionality removed
  //     //   id: const Uuid().v4(),
  //     //   date: DateTime.now(),
  //     //   dueDate: DateTime.now().add(const Duration(days: 7)),
  //     //   status: InvoiceStatus.pending,
  //     //   type: InvoiceType.invoice,
  //     //   notes:
  //     //       'Commande générée à partir du devis ${quote.id.substring(0, 8).toUpperCase()}',
  //     // );
  //
  //     // Enregistrer la commande
  //     // await invoiceService.addInvoice(order); // Functionality removed
  //
  //     // Mettre à jour le statut du devis
  //     // final updatedQuote = quote.copyWith(status: InvoiceStatus.converted); // Functionality removed
  //
  //     // await invoiceService.updateInvoice(updatedQuote); // Functionality removed
  //
  //     // return order; // Functionality removed
  //     return null; // Placeholder
  //   } catch (e) {
  //     debugPrint('Erreur lors de la conversion du devis en commande: $e');
  //     return null;
  //   }
  // }

  // Méthodes temporaires pour corriger les erreurs de compilation

  Future<dynamic> generateQuoteFromMessage(
    String message,
    dynamic conversation,
  ) async {
    // TODO: Implémenter la génération de devis à partir d'un message
    debugPrint('generateQuoteFromMessage appelée avec: $message');
    return null;
  }

  Future<void> sendQuoteToCustomer(dynamic quote, dynamic conversation) async {
    // TODO: Implémenter l'envoi de devis au client
    debugPrint('sendQuoteToCustomer appelée');
  }

  Future<bool> isOrderConfirmation(String message) async {
    // TODO: Implémenter la détection de confirmation de commande
    debugPrint('isOrderConfirmation appelée avec: $message');
    return false;
  }

  Future<dynamic> convertQuoteToOrder(String quoteId) async {
    // TODO: Implémenter la conversion de devis en commande
    debugPrint('convertQuoteToOrder appelée avec: $quoteId');
    return null;
  }
}
