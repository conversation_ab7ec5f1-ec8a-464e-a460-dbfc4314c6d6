Write-Host "Fixing flutter_sms build.gradle file..."

$gradleFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\build.gradle"

if (-not (Test-Path $gradleFile)) {
    Write-Host "File not found: $gradleFile"
    exit 1
}

Write-Host "Original build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Adding namespace to build.gradle..."

$content = Get-Content $gradleFile
$newContent = $content -replace 'apply plugin: "com.android.library"', 'apply plugin: "com.android.library"

android {
    namespace "com.babariviere.sms"
}'

$newContent | Set-Content $gradleFile

Write-Host ""
Write-Host "Modified build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Fix completed!"
