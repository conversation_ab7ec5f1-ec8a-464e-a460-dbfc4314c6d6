import 'package:flutter/material.dart';

// Application Bill Splitter – Style Futur Night Mode
// Titre de thème : “Orix Purple Split”

// 🎨 Palette de couleurs :
const Color orixFondGeneral = Color(0xFF2B2543); // Fond général
const Color orixCartes = Color(0xFF3C2E59); // Cartes
const Color orixAccentPrincipal = Color(0xFFE3B487); // Accent saumon remplacé
const Color orixAccentSecondaire = Color(0xFF75E3DD); // Accent cyan
const Color orixSidebarFond = Color(0xFF3B2D52); // Sidebar fond
const Color orixSidebarIcones = Color(0xFFDAD9DE); // Sidebar icônes

// 🖍️ Couleurs de texte :
const Color textPrincipalOrix = Color(
  0xFFE8E6F0,
); // Texte principal blanc cassé
const Color textSecondaireOrix =
    orixSidebarIcones; // Texte secondaire (utilise la couleur des icônes de la sidebar)
const Color textSurBoutonPrincipalOrix =
    orixFondGeneral; // Texte sur boutons principaux (anciennement violetNuit)

class OrixPurpleSplitTheme {
  static final ThemeData themeData = ThemeData(
    brightness: Brightness.dark,
    primaryColor: orixAccentPrincipal, // Couleur d'accent principale
    scaffoldBackgroundColor: orixFondGeneral,
    colorScheme: const ColorScheme.dark(
      primary: orixAccentPrincipal,
      secondary: orixAccentSecondaire,
      surface: orixCartes,
      error: Colors.redAccent, // À définir selon les besoins
      onPrimary: textSurBoutonPrincipalOrix, // Texte sur couleur primaire
      onSecondary: textPrincipalOrix, // Texte sur couleur secondaire
      onSurface: textPrincipalOrix, // Texte sur le fond principal
      onError: textPrincipalOrix,
      brightness: Brightness.dark,
    ),
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      displayMedium: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      displaySmall: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      headlineSmall: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      titleLarge: TextStyle(
        color: textPrincipalOrix,
        fontWeight: FontWeight.bold,
      ),
      bodyLarge: TextStyle(color: textPrincipalOrix),
      bodyMedium: TextStyle(color: textPrincipalOrix),
      labelLarge: TextStyle(
        color: textSurBoutonPrincipalOrix,
        fontWeight: FontWeight.bold,
      ), // Pour les boutons principaux
      bodySmall: TextStyle(
        color: textSecondaireOrix,
      ), // Placeholder, texte secondaire
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor:
          orixFondGeneral, // En-têtes (peut être ajusté si un header distinct est voulu)
      foregroundColor:
          textPrincipalOrix, // Couleur du titre et des icônes de l'AppBar
      elevation: 0, // Style moderne, sans ombre ou ombre subtile
      titleTextStyle: TextStyle(
        color: textPrincipalOrix,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
    buttonTheme: const ButtonThemeData(
      buttonColor: orixAccentPrincipal,
      textTheme: ButtonTextTheme.primary,
      colorScheme: ColorScheme.dark(
        primary: orixAccentPrincipal,
        secondary: orixAccentSecondaire,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: orixAccentPrincipal,
        foregroundColor: textSurBoutonPrincipalOrix,
        textStyle: const TextStyle(fontWeight: FontWeight.bold),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: textPrincipalOrix, // Texte pour boutons secondaires
        backgroundColor: orixCartes, // Fond pour boutons secondaires
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: orixAccentSecondaire.withValues(
              alpha: 128,
            ), // withOpacity(0.5),
          ), // Bordure optionnelle
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: orixCartes, // Fond des zones de saisie
      hintStyle: TextStyle(
        color: textSecondaireOrix.withValues(alpha: 179),
      ), // 0.7 * 255 ≈ 179
      labelStyle: const TextStyle(color: textSecondaireOrix),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: BorderSide(
          color: orixCartes.withValues(alpha: 128),
          width: 1,
        ), // 0.5 * 255 ≈ 128
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10),
        borderSide: const BorderSide(color: orixAccentPrincipal, width: 1.5),
      ),
      prefixIconColor: textPrincipalOrix.withValues(
        alpha: 204,
      ), // 0.8 * 255 ≈ 204
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    ),
    cardTheme: CardTheme(
      color: orixCartes, // Zones secondaires
      elevation: 0, // Pas de glow ou contour doux -> elevation: 0 ou faible
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        // side: BorderSide(color: orixAccentSecondaire.withValues(alpha: 77) // withOpacity(0.3), width: 1), // Contour doux optionnel
      ),
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
    ),
    iconTheme: const IconThemeData(
      color: textPrincipalOrix, // Couleur par défaut des icônes
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith<Color?>((
        // TODO: Vérifier si orixAccentPrincipal est trop fort ici // TODO: Vérifier si orixAccentPrincipal est trop fort ici
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return orixAccentPrincipal;
        }
        return orixSidebarIcones; // Utilise la couleur des icônes de la sidebar pour le non sélectionné
      }),
      trackColor: WidgetStateProperty.resolveWith<Color?>((
        // TODO: Vérifier si orixAccentPrincipal est trop fort ici // TODO: Vérifier si orixAccentPrincipal est trop fort ici
        Set<WidgetState> states,
      ) {
        if (states.contains(WidgetState.selected)) {
          return orixAccentSecondaire.withValues(alpha: 204); // 0.8 * 255 ≈ 204
        }
        return orixFondGeneral.withValues(alpha: 128); // 0.5 * 255 ≈ 128
      }),
    ),
    bottomNavigationBarTheme: BottomNavigationBarThemeData(
      backgroundColor: orixFondGeneral,
      selectedItemColor: orixAccentPrincipal,
      unselectedItemColor: orixSidebarIcones.withValues(
        alpha: 179,
      ), // 0.7 * 255 ≈ 179
      type: BottomNavigationBarType.fixed,
    ),
    // Spécificités pour "Split Now" button (à gérer au niveau du widget si besoin de style très particulier)
  );

  // Couleurs spécifiques au thème
  static const Color primaryTextColor = textPrincipalOrix;
  static const Color secondaryTextColor = textSecondaireOrix;
  static const Color headerColor = orixFondGeneral; // anciennement bleuMinuit
  static const Color accentColor =
      orixAccentPrincipal; // anciennement saumonDoux
  static const Color disabledIconColor =
      orixSidebarIcones; // anciennement grisNuageux
  static const Color lightAccentAreaColor =
      orixAccentSecondaire; // anciennement cremePale, maintenant cyan

  // Gradients (si on décide d'en ajouter plus tard)
  static const LinearGradient mainGradient = LinearGradient(
    colors: [
      orixFondGeneral,
      Color(0xFF221E38),
    ], // Ajustement léger pour le dégradé
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  static const LinearGradient sidebarGradient = LinearGradient(
    colors: [orixSidebarFond, Color(0xFF30254A)], // Ajustement pour la sidebar
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
