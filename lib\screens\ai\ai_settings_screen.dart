import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/ai/ai_config.dart';
import '../../models/ai/ai_provider.dart';
import '../../services/ai/ai_service.dart';
import '../../providers/theme_provider.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';
import '../../widgets/neon_dropdown.dart';

class AISettingsScreen extends ConsumerStatefulWidget {
  const AISettingsScreen({super.key});

  @override
  ConsumerState<AISettingsScreen> createState() => _AISettingsScreenState();
}

class _AISettingsScreenState extends ConsumerState<AISettingsScreen> {
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _organizationIdController =
      TextEditingController();
  final TextEditingController _supabaseUrlController = TextEditingController();
  final TextEditingController _supabaseKeyController = TextEditingController();
  final TextEditingController _systemPromptController = TextEditingController();

  AIProviderType _selectedProvider = AIProviderType.openai;
  String _selectedModel = 'gpt-3.5-turbo';
  double _temperature = 0.7;
  int _maxTokens = 1000;
  bool _isLoading = false;
  bool _showApiKey = false;
  bool _showSupabaseKey = false;

  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _organizationIdController.dispose();
    _supabaseUrlController.dispose();
    _supabaseKeyController.dispose();
    _systemPromptController.dispose();
    super.dispose();
  }

  // Charger la configuration
  Future<void> _loadConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Charger la configuration AI
      final aiConfig = ref.read(aiConfigProvider);

      _apiKeyController.text = aiConfig.apiKey;
      _organizationIdController.text = aiConfig.organizationId ?? '';
      _selectedProvider = aiConfig.provider;
      _selectedModel = aiConfig.model;
      _temperature = aiConfig.temperature;
      _maxTokens = aiConfig.maxTokens;
      _systemPromptController.text = aiConfig.systemPrompt;

      // Charger la configuration Supabase
      // Note: Ces propriétés ne sont pas encore implémentées dans SupabaseService
      // Nous utilisons des valeurs par défaut pour l'instant
      _supabaseUrlController.text = '';
      _supabaseKeyController.text = '';
    } catch (e) {
      debugPrint('Erreur lors du chargement de la configuration: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Sauvegarder la configuration
  Future<void> _saveConfig() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Mettre à jour la configuration AI
      final aiConfigNotifier = ref.read(aiConfigProvider.notifier);

      final newConfig = AIConfig(
        provider: _selectedProvider,
        apiKey: _apiKeyController.text,
        organizationId:
            _organizationIdController.text.isEmpty
                ? null
                : _organizationIdController.text,
        model: _selectedModel,
        temperature: _temperature,
        maxTokens: _maxTokens,
        systemPrompt: _systemPromptController.text,
        enableAutoResponses: true,
        additionalConfig: {
          'supabaseUrl': _supabaseUrlController.text,
          'supabaseKey': _supabaseKeyController.text,
        },
      );

      aiConfigNotifier.updateConfig(newConfig);

      // Mettre à jour la configuration Supabase
      // Note: La méthode saveConfig n'est pas encore implémentée dans SupabaseService
      // Nous stockons les valeurs dans additionalConfig pour l'instant
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('supabase_url', _supabaseUrlController.text);
      await prefs.setString('supabase_key', _supabaseKeyController.text);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration sauvegardée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde de la configuration: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres IA'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child:
            _isLoading
                ? Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Section Fournisseur IA
                      _buildSectionTitle('Fournisseur IA', primaryColor),
                      const SizedBox(height: 16),
                      NeonDropdown<AIProviderType>(
                        value: _selectedProvider,
                        items:
                            AIProviderType.values.map((provider) {
                              return DropdownMenuItem<AIProviderType>(
                                value: provider,
                                child: Text(_getProviderName(provider)),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedProvider = value;
                              _selectedModel =
                                  AIConfig.getDefaultModelForProvider(value);
                            });
                          }
                        },
                        width: double.infinity,
                      ),
                      const SizedBox(height: 16),

                      // Section Clé API
                      _buildSectionTitle('Clé API', primaryColor),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: NeonTextField(
                              controller: _apiKeyController,
                              labelText: 'Clé API',
                              obscureText: !_showApiKey,
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              _showApiKey
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: primaryColor,
                            ),
                            onPressed: () {
                              setState(() {
                                _showApiKey = !_showApiKey;
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      NeonTextField(
                        controller: _organizationIdController,
                        labelText: 'ID Organisation (optionnel)',
                      ),
                      const SizedBox(height: 16),

                      // Section Modèle
                      _buildSectionTitle('Modèle', primaryColor),
                      const SizedBox(height: 16),
                      NeonDropdown<String>(
                        value: _selectedModel,
                        items:
                            _getModelsForProvider(_selectedProvider).map((
                              model,
                            ) {
                              return DropdownMenuItem<String>(
                                value: model,
                                child: Text(model),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedModel = value;
                            });
                          }
                        },
                        width: double.infinity,
                      ),
                      const SizedBox(height: 16),

                      // Section Paramètres
                      _buildSectionTitle('Paramètres', primaryColor),
                      const SizedBox(height: 16),
                      Text(
                        'Température: ${_temperature.toStringAsFixed(1)}',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Slider(
                        value: _temperature,
                        min: 0.0,
                        max: 1.0,
                        divisions: 10,
                        activeColor: secondaryColor,
                        inactiveColor: secondaryColor.withValues(
                          alpha: 77,
                        ), // 0.3 * 255 ≈ 77
                        onChanged: (value) {
                          setState(() {
                            _temperature = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Tokens maximum: $_maxTokens',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Slider(
                        value: _maxTokens.toDouble(),
                        min: 100,
                        max: 4000,
                        divisions: 39,
                        activeColor: secondaryColor,
                        inactiveColor: secondaryColor.withValues(
                          alpha: 77,
                        ), // 0.3 * 255 ≈ 77
                        onChanged: (value) {
                          setState(() {
                            _maxTokens = value.toInt();
                          });
                        },
                      ),
                      const SizedBox(height: 16),

                      // Section Prompt système
                      _buildSectionTitle('Prompt système', primaryColor),
                      const SizedBox(height: 16),
                      NeonTextField(
                        controller: _systemPromptController,
                        labelText: 'Prompt système',
                        maxLines: 5,
                      ),
                      const SizedBox(height: 24),

                      // Section Supabase
                      _buildSectionTitle('Intégration Supabase', primaryColor),
                      const SizedBox(height: 16),
                      NeonTextField(
                        controller: _supabaseUrlController,
                        labelText: 'URL Supabase',
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: NeonTextField(
                              controller: _supabaseKeyController,
                              labelText: 'Clé Supabase',
                              obscureText: !_showSupabaseKey,
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              _showSupabaseKey
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                              color: primaryColor,
                            ),
                            onPressed: () {
                              setState(() {
                                _showSupabaseKey = !_showSupabaseKey;
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Bouton de sauvegarde
                      Center(
                        child: NeonButton(
                          text: 'Sauvegarder',
                          icon: Icons.save,
                          color: primaryColor,
                          onPressed: _saveConfig,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
      ),
    );
  }

  // Construire un titre de section
  Widget _buildSectionTitle(String title, Color color) {
    return Text(
      title,
      style: TextStyle(
        color: color,
        fontSize: 18,
        fontWeight: FontWeight.bold,
        shadows: [
          Shadow(
            color: color.withValues(alpha: 128), // 0.5 * 255 ≈ 128
            blurRadius: 5,
            offset: const Offset(0, 0),
          ),
        ],
      ),
    );
  }

  // Obtenir le nom du fournisseur
  String _getProviderName(AIProviderType provider) {
    switch (provider) {
      case AIProviderType.openai:
        return 'OpenAI';
      case AIProviderType.anthropic:
        return 'Anthropic';
      case AIProviderType.gemini:
        return 'Google Gemini';
      case AIProviderType.mistral:
        return 'Mistral AI';
      case AIProviderType.groq:
        return 'Groq';
      case AIProviderType.deepseek:
        return 'DeepSeek';
    }
  }

  // Obtenir les modèles pour un fournisseur
  List<String> _getModelsForProvider(AIProviderType provider) {
    switch (provider) {
      case AIProviderType.openai:
        return ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'];
      case AIProviderType.anthropic:
        return [
          'claude-3-haiku-20240307',
          'claude-3-sonnet-20240229',
          'claude-3-opus-20240229',
        ];
      case AIProviderType.gemini:
        return ['gemini-pro', 'gemini-1.5-pro', 'gemini-1.5-flash'];
      case AIProviderType.mistral:
        return ['mistral-tiny', 'mistral-small', 'mistral-medium'];
      case AIProviderType.groq:
        return ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768'];
      case AIProviderType.deepseek:
        return ['deepseek-chat', 'deepseek-coder'];
    }
  }
}
