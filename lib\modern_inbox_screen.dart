import 'package:flutter/material.dart';

class Message {
  final String id;
  final String senderId;
  final String receiverId;
  final String content;
  final DateTime timestamp;
  final bool isRead;
  final bool isSent;

  Message({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.content,
    required this.timestamp,
    this.isRead = false,
    this.isSent = true,
  });
}

class Contact {
  final String id;
  final String name;
  final String avatarUrl;
  final bool isOnline;
  final DateTime lastSeen;
  final List<Message> messages;
  final int unreadCount;

  Contact({
    required this.id,
    required this.name,
    required this.avatarUrl,
    this.isOnline = false,
    required this.lastSeen,
    required this.messages,
    this.unreadCount = 0,
  });
}

class ModernInboxScreen extends StatefulWidget {
  const ModernInboxScreen({super.key});

  @override
  State<ModernInboxScreen> createState() => _ModernInboxScreenState();
}

class _ModernInboxScreenState extends State<ModernInboxScreen> {
  final TextEditingController _messageController = TextEditingController();
  String _currentContactId = '';
  final List<Contact> _contacts = [
    Contact(
      id: '1',
      name: '<PERSON>',
      avatarUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
      isOnline: true,
      lastSeen: DateTime.now(),
      messages: [
        Message(
          id: '1',
          senderId: '1',
          receiverId: 'me',
          content: 'Bonjour, comment ça va aujourd\'hui?',
          timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
          isRead: true,
        ),
        Message(
          id: '2',
          senderId: 'me',
          receiverId: '1',
          content: 'Très bien merci, et toi?',
          timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 1)),
          isRead: true,
        ),
        Message(
          id: '3',
          senderId: '1',
          receiverId: 'me',
          content: 'Je vais bien aussi. On se voit demain pour la réunion?',
          timestamp: DateTime.now().subtract(const Duration(hours: 5)),
          isRead: false,
        ),
      ],
      unreadCount: 1,
    ),
    Contact(
      id: '2',
      name: 'Marie Martin',
      avatarUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(minutes: 30)),
      messages: [
        Message(
          id: '4',
          senderId: '2',
          receiverId: 'me',
          content: 'As-tu terminé le rapport pour le client?',
          timestamp: DateTime.now().subtract(const Duration(days: 2)),
          isRead: true,
        ),
        Message(
          id: '5',
          senderId: 'me',
          receiverId: '2',
          content: 'Oui, je l\'ai envoyé hier soir.',
          timestamp: DateTime.now().subtract(const Duration(days: 1)),
          isRead: true,
        ),
      ],
      unreadCount: 0,
    ),
    Contact(
      id: '3',
      name: 'Pierre Durand',
      avatarUrl: 'https://randomuser.me/api/portraits/men/3.jpg',
      isOnline: true,
      lastSeen: DateTime.now(),
      messages: [
        Message(
          id: '6',
          senderId: '3',
          receiverId: 'me',
          content: 'Peux-tu m\'envoyer les dernières mises à jour?',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
          isRead: false,
        ),
      ],
      unreadCount: 1,
    ),
    Contact(
      id: '4',
      name: 'Sophie Petit',
      avatarUrl: 'https://randomuser.me/api/portraits/women/4.jpg',
      isOnline: false,
      lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      messages: [
        Message(
          id: '7',
          senderId: 'me',
          receiverId: '4',
          content: 'Bonjour Sophie, as-tu reçu mon email?',
          timestamp: DateTime.now().subtract(const Duration(days: 3)),
          isRead: true,
        ),
        Message(
          id: '8',
          senderId: '4',
          receiverId: 'me',
          content: 'Oui, je vais le regarder aujourd\'hui.',
          timestamp: DateTime.now().subtract(const Duration(days: 2)),
          isRead: true,
        ),
      ],
      unreadCount: 0,
    ),
    Contact(
      id: '5',
      name: 'Lucas Bernard',
      avatarUrl: 'https://randomuser.me/api/portraits/men/5.jpg',
      isOnline: true,
      lastSeen: DateTime.now(),
      messages: [
        Message(
          id: '9',
          senderId: '5',
          receiverId: 'me',
          content: 'On déjeune ensemble demain?',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          isRead: false,
        ),
      ],
      unreadCount: 1,
    ),
  ];

  @override
  void initState() {
    super.initState();
    if (_contacts.isNotEmpty) {
      _currentContactId = _contacts[0].id;
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      final newMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        senderId: 'me',
        receiverId: _currentContactId,
        content: _messageController.text,
        timestamp: DateTime.now(),
        isRead: false,
      );

      for (int i = 0; i < _contacts.length; i++) {
        if (_contacts[i].id == _currentContactId) {
          final updatedMessages = List<Message>.from(_contacts[i].messages)
            ..add(newMessage);
          _contacts[i] = Contact(
            id: _contacts[i].id,
            name: _contacts[i].name,
            avatarUrl: _contacts[i].avatarUrl,
            isOnline: _contacts[i].isOnline,
            lastSeen: _contacts[i].lastSeen,
            messages: updatedMessages,
            unreadCount: _contacts[i].unreadCount,
          );
          break;
        }
      }
    });

    _messageController.clear();
  }

  Contact? _getCurrentContact() {
    for (final contact in _contacts) {
      if (contact.id == _currentContactId) {
        return contact;
      }
    }
    return null;
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(time.year, time.month, time.day);

    if (messageDate == today) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == yesterday) {
      return 'Hier';
    } else {
      return '${time.day}/${time.month}/${time.year}';
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentContact = _getCurrentContact();

    return Scaffold(
      body: Row(
        children: [
          // Liste des contacts (panneau de gauche)
          Container(
            width: 300,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                right: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Column(
              children: [
                // En-tête de la liste des contacts
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6A5ACD),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(
                          alpha: 26,
                        ), // 0.1 * 255 ≈ 26
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.white,
                        child: Icon(Icons.person, color: Color(0xFF6A5ACD)),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Messagerie',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.search, color: Colors.white),
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ),
                // Liste des contacts
                Expanded(
                  child: ListView.builder(
                    itemCount: _contacts.length,
                    itemBuilder: (context, index) {
                      final contact = _contacts[index];
                      final lastMessage =
                          contact.messages.isNotEmpty
                              ? contact.messages.last
                              : null;
                      final isSelected = contact.id == _currentContactId;

                      return InkWell(
                        onTap: () {
                          setState(() {
                            _currentContactId = contact.id;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? Colors.grey.shade100
                                    : Colors.transparent,
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              Stack(
                                children: [
                                  CircleAvatar(
                                    radius: 24,
                                    backgroundImage: NetworkImage(
                                      contact.avatarUrl,
                                    ),
                                  ),
                                  if (contact.isOnline)
                                    Positioned(
                                      right: 0,
                                      bottom: 0,
                                      child: Container(
                                        width: 12,
                                        height: 12,
                                        decoration: BoxDecoration(
                                          color: Colors.green,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white,
                                            width: 2,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          contact.name,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        if (lastMessage != null)
                                          Text(
                                            _formatTime(lastMessage.timestamp),
                                            style: TextStyle(
                                              color:
                                                  contact.unreadCount > 0
                                                      ? const Color(0xFF6A5ACD)
                                                      : Colors.grey,
                                              fontSize: 12,
                                            ),
                                          ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            lastMessage?.content ?? '',
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              color:
                                                  contact.unreadCount > 0
                                                      ? Colors.black
                                                      : Colors.grey,
                                              fontWeight:
                                                  contact.unreadCount > 0
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                        if (contact.unreadCount > 0)
                                          Container(
                                            margin: const EdgeInsets.only(
                                              left: 8,
                                            ),
                                            padding: const EdgeInsets.all(6),
                                            decoration: const BoxDecoration(
                                              color: Color(0xFF6A5ACD),
                                              shape: BoxShape.circle,
                                            ),
                                            child: Text(
                                              contact.unreadCount.toString(),
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          // Zone de conversation (panneau de droite)
          Expanded(
            child:
                currentContact == null
                    ? const Center(child: Text('Sélectionnez une conversation'))
                    : Column(
                      children: [
                        // En-tête de la conversation
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(
                                  alpha: 13,
                                ), // 0.05 * 255 ≈ 13
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Stack(
                                children: [
                                  CircleAvatar(
                                    radius: 20,
                                    backgroundImage: NetworkImage(
                                      currentContact.avatarUrl,
                                    ),
                                  ),
                                  if (currentContact.isOnline)
                                    Positioned(
                                      right: 0,
                                      bottom: 0,
                                      child: Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: Colors.green,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white,
                                            width: 2,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      currentContact.name,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    Text(
                                      currentContact.isOnline
                                          ? 'En ligne'
                                          : 'Vu(e) ${_formatTime(currentContact.lastSeen)}',
                                      style: TextStyle(
                                        color:
                                            currentContact.isOnline
                                                ? Colors.green
                                                : Colors.grey,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.phone),
                                onPressed: () {},
                              ),
                              IconButton(
                                icon: const Icon(Icons.videocam),
                                onPressed: () {},
                              ),
                              IconButton(
                                icon: const Icon(Icons.more_vert),
                                onPressed: () {},
                              ),
                            ],
                          ),
                        ),
                        // Zone des messages
                        Expanded(
                          child: Container(
                            color: Colors.grey.shade100,
                            padding: const EdgeInsets.all(16),
                            child: ListView.builder(
                              reverse: true,
                              itemCount: currentContact.messages.length,
                              itemBuilder: (context, index) {
                                final message =
                                    currentContact.messages[currentContact
                                            .messages
                                            .length -
                                        1 -
                                        index];
                                final isMe = message.senderId == 'me';

                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Row(
                                    mainAxisAlignment:
                                        isMe
                                            ? MainAxisAlignment.end
                                            : MainAxisAlignment.start,
                                    children: [
                                      if (!isMe)
                                        CircleAvatar(
                                          radius: 16,
                                          backgroundImage: NetworkImage(
                                            currentContact.avatarUrl,
                                          ),
                                        ),
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 10,
                                        ),
                                        constraints: BoxConstraints(
                                          maxWidth:
                                              MediaQuery.of(
                                                context,
                                              ).size.width *
                                              0.5,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              isMe
                                                  ? const Color(0xFF6A5ACD)
                                                  : Colors.white,
                                          borderRadius: BorderRadius.circular(
                                            20,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withValues(
                                                alpha: 13, // 0.05 * 255 ≈ 13
                                              ),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              message.content,
                                              style: TextStyle(
                                                color:
                                                    isMe
                                                        ? Colors.white
                                                        : Colors.black,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  _formatTime(
                                                    message.timestamp,
                                                  ),
                                                  style: TextStyle(
                                                    color:
                                                        isMe
                                                            ? Colors.white
                                                                .withValues(
                                                                  alpha:
                                                                      179, // 0.7 * 255 ≈ 179
                                                                )
                                                            : Colors.grey,
                                                    fontSize: 10,
                                                  ),
                                                ),
                                                if (isMe) ...[
                                                  const SizedBox(width: 4),
                                                  Icon(
                                                    message.isRead
                                                        ? Icons.done_all
                                                        : Icons.done,
                                                    size: 12,
                                                    color:
                                                        message.isRead
                                                            ? Colors.white
                                                                .withValues(
                                                                  alpha:
                                                                      179, // 0.7 * 255 ≈ 179
                                                                )
                                                            : Colors.white
                                                                .withValues(
                                                                  alpha:
                                                                      128, // 0.5 * 255 ≈ 128
                                                                ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        // Zone de saisie de message
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(
                                  alpha: 13,
                                ), // 0.05 * 255 ≈ 13
                                blurRadius: 4,
                                offset: const Offset(0, -2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.attach_file),
                                onPressed: () {},
                              ),
                              Expanded(
                                child: TextField(
                                  controller: _messageController,
                                  decoration: InputDecoration(
                                    hintText: 'Écrivez un message...',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(30),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor: Colors.grey.shade100,
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 10,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                icon: const Icon(Icons.mic),
                                onPressed: () {},
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.send,
                                  color: Color(0xFF6A5ACD),
                                ),
                                onPressed: _sendMessage,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
          ),
        ],
      ),
    );
  }
}
