Write-Host "Fixing flutter_sms Gradle version..."

$gradleFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\build.gradle"

if (-not (Test-Path $gradleFile)) {
    Write-Host "File not found: $gradleFile"
    exit 1
}

Write-Host "Original build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Updating Gradle version in build.gradle..."

$content = Get-Content $gradleFile -Raw
$updatedContent = $content -replace "classpath 'com.android.tools.build:gradle:3.5.4'", "classpath 'com.android.tools.build:gradle:7.0.2'"

$updatedContent | Set-Content $gradleFile

Write-Host ""
Write-Host "Modified build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Fix completed!"
