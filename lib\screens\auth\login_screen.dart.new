import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/auth_service.dart';
import '../../theme/neon_theme.dart';

final loginLoadingProvider = StateProvider<bool>((ref) => false);
final loginErrorProvider = StateProvider<String?>((ref) => null);

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _codeController = TextEditingController();
  bool _obscurePassword = true;
  bool _useCode = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  // Méthode pour gérer la navigation vers le tableau de bord
  void _navigateToDashboard() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.go('/dashboard');
      }
    });
  }

  // Méthode pour gérer l'erreur de connexion
  void _handleLoginError(String errorMessage) {
    ref.read(loginErrorProvider.notifier).state = errorMessage;
  }

  // Méthode pour gérer la connexion
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    // Activer l'indicateur de chargement
    ref.read(loginLoadingProvider.notifier).state = true;
    ref.read(loginErrorProvider.notifier).state = null;

    try {
      // Tenter de se connecter
      final user = _useCode
          ? await ref.read(authServiceProvider).loginWithCode(
                _codeController.text.trim(),
              )
          : await ref.read(authServiceProvider).login(
                _emailController.text.trim(),
                _passwordController.text,
              );

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Désactiver l'indicateur de chargement
      ref.read(loginLoadingProvider.notifier).state = false;

      // Vérifier si la connexion a réussi
      if (user != null) {
        _navigateToDashboard();
      } else {
        _handleLoginError(_useCode
            ? 'Code d\'accès incorrect. Veuillez réessayer.'
            : 'Identifiants incorrects. Veuillez réessayer.');
      }
    } catch (e) {
      // Gérer les erreurs
      if (mounted) {
        ref.read(loginLoadingProvider.notifier).state = false;
        _handleLoginError('Erreur de connexion: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(loginLoadingProvider);
    final error = ref.watch(loginErrorProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: NeonTheme.mainGradient,
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo et titre
                Icon(
                  Icons.business_center,
                  size: 80,
                  color: NeonTheme.neonCyan,
                  shadows: [
                    Shadow(
                      color: NeonTheme.neonCyan.withValues(alpha: 204), // 0.8 * 255 ≈ 204
                      blurRadius: 12.0,
                    ),
                    Shadow(
                      color: NeonTheme.neonCyan.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                      blurRadius: 24.0,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'HCP-DESIGN CRM',
                  textAlign: TextAlign.center,
                  style: NeonTheme.neonTextStyle(NeonTheme.neonCyan).copyWith(
                    fontSize: 28,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Connectez-vous pour accéder à votre espace',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 204), // 0.8 * 255 ≈ 204
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 32),

                // Formulaire de connexion
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Afficher l'erreur s'il y en a une
                      if (error != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade900.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade800),
                          ),
                          child: Text(
                            error,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Bouton pour basculer entre connexion par email/mot de passe et code
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildToggleButton(
                            text: 'Email',
                            isSelected: !_useCode,
                            onPressed: () => setState(() => _useCode = false),
                          ),
                          const SizedBox(width: 16),
                          _buildToggleButton(
                            text: 'Code',
                            isSelected: _useCode,
                            onPressed: () => setState(() => _useCode = true),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      if (_useCode)
                        // Champ code
                        _buildTextField(
                          controller: _codeController,
                          label: 'Code d\'accès',
                          hint: 'Entrez votre code d\'accès',
                          icon: Icons.vpn_key,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer votre code d\'accès';
                            }
                            return null;
                          },
                        )
                      else ...[
                        // Champ email
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email',
                          hint: 'Entrez votre email',
                          icon: Icons.email,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer votre email';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                .hasMatch(value)) {
                              return 'Veuillez entrer un email valide';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Champ mot de passe
                        _buildTextField(
                          controller: _passwordController,
                          label: 'Mot de passe',
                          hint: 'Entrez votre mot de passe',
                          icon: Icons.lock,
                          obscureText: _obscurePassword,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: NeonTheme.neonPurple,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Veuillez entrer votre mot de passe';
                            }
                            return null;
                          },
                        ),
                      ],
                      const SizedBox(height: 24),

                      // Bouton de connexion
                      _buildNeonButton(
                        text: 'Se connecter',
                        isLoading: isLoading,
                        onPressed: _login,
                      ),
                      const SizedBox(height: 16),

                      // Lien vers l'inscription
                      TextButton(
                        onPressed: () {
                          context.go('/register');
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: NeonTheme.neonCyan,
                        ),
                        child: const Text('Pas encore de compte ? Inscrivez-vous'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 51), // 0.2 * 255 ≈ 51
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: NeonTheme.neonPurple.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(icon, color: NeonTheme.neonCyan),
          suffixIcon: suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          labelStyle: TextStyle(color: Colors.white.withValues(alpha: 179)), // 0.7 * 255 ≈ 179
          hintStyle: TextStyle(color: Colors.white.withValues(alpha: 77)), // 0.3 * 255 ≈ 77
        ),
        style: const TextStyle(color: Colors.white),
        keyboardType: keyboardType,
        obscureText: obscureText,
        validator: validator,
      ),
    );
  }

  Widget _buildNeonButton({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: NeonTheme.activeGradient,
        borderRadius: BorderRadius.circular(12),
        boxShadow: NeonTheme.neonShadow(NeonTheme.neonCyan),
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              )
            : Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
      ),
    );
  }

  Widget _buildToggleButton({
    required String text,
    required bool isSelected,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.black.withValues(alpha: 77) // 0.3 * 255 ≈ 77
              : Colors.transparent,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: isSelected ? NeonTheme.neonCyan : Colors.white.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            width: 1.5,
          ),
          boxShadow: isSelected
              ? NeonTheme.neonShadow(NeonTheme.neonCyan, intensity: 0.5)
              : null,
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? NeonTheme.neonCyan : Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
