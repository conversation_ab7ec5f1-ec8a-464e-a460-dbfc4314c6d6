// lib/theme/tz2_theme.dart
import 'package:flutter/material.dart';

class TZ2Theme {
  // Palette principale (Zomo App - Stockage de fichiers)
  static const Color cloudyViolet = Color(0xFFA074FF);
  static const Color radiantPink = Color(0xFFC49CFF);
  static const Color airyLavenderStart = Color(0xFFF2E9FF);
  static const Color airyLavenderEnd = Color(0xFFF5F0FF);

  // Éléments d’interface
  static const Color freshMintGreen = Color(
    0xFF3BE0BB,
  ); // Boutons Start Now, Create
  static const Color iconPink = Color(0xFFF98AF0); // Icône principale dossier
  static const Color pulsedViolet = Color(0xFFC97EFF); // Barre de chargement

  // Blocs de fichiers
  static const Color sandYellow = Color(0xFFFFF2D6); // Bloc Picture
  static const Color powderPink = Color(0xFFFFE9E9); // Bloc Video
  static const Color nuancedBlue = Color(0xFFE2ECFF); // Bloc Apps

  // Couleurs globales et textes
  static const Color primaryTextLight = Color(
    0xFFFFFFFF,
  ); // Texte blanc/gris clair sur fonds sombres/dégradés
  static const Color primaryTextDark = Color(
    0xFF000000,
  ); // Texte noir sur fonds clairs
  static const Color secondaryTextLight = Color(0xFFE0E0E0); // Texte gris clair

  // Dégradés
  static const LinearGradient zomoGradient = LinearGradient(
    colors: [cloudyViolet, radiantPink],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient airyLavenderBackground = LinearGradient(
    colors: [airyLavenderStart, airyLavenderEnd],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient buttonGradient = LinearGradient(
    colors: [
      freshMintGreen,
      Color(0xFF30D1B0),
    ], // Léger dégradé pour les boutons
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  // Ombres (subtiles pour un look moderne)
  static final BoxShadow subtleShadow = BoxShadow(
    color: Colors.black.withValues(alpha: 20), // 0.08 * 255 ≈ 20
    blurRadius: 10,
    spreadRadius: 2,
    offset: const Offset(0, 4),
  );

  static final BoxShadow buttonShadow = BoxShadow(
    color: freshMintGreen.withValues(alpha: 77), // 0.3 * 255 ≈ 77
    blurRadius: 8,
    spreadRadius: 1,
    offset: const Offset(0, 3),
  );

  // Thème principal
  static final ThemeData themeData = ThemeData(
    brightness:
        Brightness
            .light, // Base claire, mais les fonds peuvent être des dégradés
    primaryColor: cloudyViolet,
    scaffoldBackgroundColor:
        airyLavenderEnd, // Couleur de repli si le dégradé n'est pas appliqué partout
    colorScheme: const ColorScheme.light(
      primary: cloudyViolet,
      secondary: freshMintGreen,
      surface: primaryTextLight, // Fond général
      error: Colors.redAccent, // Couleur d'erreur standard
      onPrimary: primaryTextLight, // Texte sur couleur primaire
      onSecondary:
          primaryTextLight, // Texte sur couleur secondaire (boutons verts)
      onSurface: primaryTextDark, // Texte sur fond général clair
      onError: primaryTextLight,
    ),
    textTheme: _textTheme,
    appBarTheme: _appBarTheme,
    elevatedButtonTheme: _elevatedButtonTheme,
    inputDecorationTheme: _inputDecorationTheme,
    cardTheme: _cardTheme,
    iconTheme: const IconThemeData(
      color: iconPink,
    ), // Couleur par défaut des icônes
    chipTheme: _chipTheme, // Pour d'éventuels tags/chips
    textButtonTheme: _textButtonTheme,
    outlinedButtonTheme: _outlinedButtonTheme,
  );

  // Styles de texte
  static final TextTheme _textTheme = TextTheme(
    displayLarge: const TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: primaryTextDark,
      letterSpacing: -0.5,
    ),
    displayMedium: const TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.bold,
      color: primaryTextDark,
      letterSpacing: -0.5,
    ),
    displaySmall: const TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: primaryTextDark,
      letterSpacing: -0.5,
    ),
    headlineMedium: const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: primaryTextDark,
    ),
    headlineSmall: const TextStyle(
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: primaryTextDark,
    ),
    titleLarge: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: primaryTextDark,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      color: primaryTextDark.withValues(alpha: 217), // 0.85 * 255 ≈ 217
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      color: primaryTextDark.withValues(alpha: 191), // 0.75 * 255 ≈ 191
    ),
    // Pour les textes sur fonds colorés/dégradés, utiliser .copyWith(color: primaryTextLight)
    labelLarge: const TextStyle(
      fontSize: 15,
      fontWeight: FontWeight.bold,
      color: primaryTextLight,
      letterSpacing: 0.5,
    ), // Texte des boutons principaux
    bodySmall: TextStyle(
      fontSize: 12,
      color: primaryTextDark.withValues(alpha: 166), // 0.65 * 255 ≈ 166
    ),
  );

  // Style de l'AppBar
  static final AppBarTheme _appBarTheme = AppBarTheme(
    backgroundColor:
        Colors
            .transparent, // Souvent transparente pour laisser voir le dégradé du scaffold
    foregroundColor: primaryTextDark, // Couleur du titre et des icônes
    elevation: 0,
    centerTitle: true,
    iconTheme: const IconThemeData(color: primaryTextDark, size: 26),
    titleTextStyle: _textTheme.headlineSmall?.copyWith(
      color: primaryTextDark,
      fontWeight: FontWeight.bold,
    ),
  );

  // Style des ElevatedButton (pour Start Now, Create)
  static final ElevatedButtonThemeData
  _elevatedButtonTheme = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.transparent,
      foregroundColor: primaryTextLight,
      elevation: 0, // L'ombre est gérée par la décoration du Container
      padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 15),
      textStyle: _textTheme.labelLarge,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      // Pour appliquer le dégradé, il faut wrapper le bouton dans un Container avec BoxDecoration
    ),
  );

  // Style des TextButton (pour Cancel)
  static final TextButtonThemeData _textButtonTheme = TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: primaryTextDark, // Texte noir
      backgroundColor: primaryTextLight, // Fond blanc
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
      textStyle: _textTheme.labelLarge?.copyWith(
        color: primaryTextDark,
        fontWeight: FontWeight.w600,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(
          color: Colors.grey.shade300,
          width: 1,
        ), // Bordure légère
      ),
    ),
  );

  // Style des OutlinedButton (si nécessaire)
  static final OutlinedButtonThemeData _outlinedButtonTheme =
      OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: freshMintGreen,
          side: const BorderSide(color: freshMintGreen, width: 1.5),
          textStyle: _textTheme.labelLarge?.copyWith(color: freshMintGreen),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

  // Style des InputDecoration (pour TextField, si utilisé)
  static final InputDecorationTheme _inputDecorationTheme =
      InputDecorationTheme(
        filled: true,
        fillColor: primaryTextLight.withValues(alpha: 230), // 0.9 * 255 ≈ 230
        hintStyle: _textTheme.bodyMedium?.copyWith(
          color: primaryTextDark.withValues(alpha: 128), // 0.5 * 255 ≈ 128
        ),
        labelStyle: _textTheme.bodyMedium?.copyWith(color: cloudyViolet),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: cloudyViolet, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
      );

  // Style des Card (pour Cloud Storage, blocs de fichiers)
  static final CardTheme _cardTheme = CardTheme(
    elevation: 0, // L'ombre est gérée par le BoxDecoration du Container
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    color: Colors.transparent, // La couleur/dégradé vient du BoxDecoration
    margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
  );

  // Style des Chip (si utilisé)
  static final ChipThemeData _chipTheme = ChipThemeData(
    backgroundColor: freshMintGreen.withValues(alpha: 38), // 0.15 * 255 ≈ 38
    labelStyle: _textTheme.bodySmall?.copyWith(
      color: freshMintGreen,
      fontWeight: FontWeight.bold,
    ),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
    elevation: 0,
    pressElevation: 0,
  );

  // Méthodes utilitaires pour les couleurs de blocs de fichiers
  static Color getFileBlockColor(String type) {
    switch (type.toLowerCase()) {
      case 'picture':
        return sandYellow;
      case 'video':
        return powderPink;
      case 'apps':
        return nuancedBlue;
      default:
        return primaryTextLight; // Couleur de fond par défaut pour les cartes
    }
  }

  static Color getFileBlockTextColor(Color backgroundColor) {
    // Tous les blocs de fichiers ont du texte noir selon la palette
    return primaryTextDark;
  }

  // Décoration pour les boutons avec dégradé (Start Now, Create)
  static BoxDecoration gradientButtonDecoration = BoxDecoration(
    gradient: buttonGradient,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [buttonShadow],
  );

  // Décoration pour la carte Cloud Storage
  static BoxDecoration cloudStorageCardDecoration = BoxDecoration(
    gradient: zomoGradient,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [subtleShadow],
  );

  // Décoration pour les blocs de fichiers
  static BoxDecoration fileBlockDecoration(Color color) {
    return BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [subtleShadow.scale(0.8)], // Ombre un peu plus petite
    );
  }
}
