import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:uuid/uuid.dart';
import '../../services/ai/auto_response_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class AutoResponseScreen extends ConsumerStatefulWidget {
  const AutoResponseScreen({super.key});

  @override
  ConsumerState<AutoResponseScreen> createState() => _AutoResponseScreenState();
}

class _AutoResponseScreenState extends ConsumerState<AutoResponseScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Charger les déclencheurs
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final triggers = ref.watch(autoResponseTriggersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Réponses automatiques'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Container(
                decoration: const BoxDecoration(
                  gradient: NeonTheme.secondaryGradient,
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Expanded(
                            child: Text(
                              'Déclencheurs de réponses automatiques',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          NeonButton(
                            text: 'Ajouter',
                            icon: FontAwesomeIcons.plus,
                            color: NeonTheme.neonGreen,
                            small: true,
                            onPressed: () {
                              _showTriggerDialog(context);
                            },
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child:
                          triggers.isEmpty
                              ? const Center(
                                child: Text(
                                  'Aucun déclencheur configuré',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                              )
                              : ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: triggers.length,
                                itemBuilder: (context, index) {
                                  final trigger = triggers[index];
                                  return _buildTriggerCard(context, trigger);
                                },
                              ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildTriggerCard(BuildContext context, AutoResponseTrigger trigger) {
    return NeonCard(
      color: trigger.isEnabled ? NeonTheme.neonPurple : Colors.grey,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    trigger.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: trigger.isEnabled,
                  onChanged: (value) {
                    ref
                        .read(autoResponseTriggersProvider.notifier)
                        .toggleTrigger(trigger.id);
                  },
                  activeColor: NeonTheme.neonPurple,
                  activeTrackColor: Colors.white.withValues(
                    alpha: 128,
                  ), // 0.5 * 255 ≈ 128
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Mots-clés:',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Wrap(
              spacing: 8,
              children:
                  trigger.keywords.map((keyword) {
                    return Chip(
                      label: Text(keyword),
                      backgroundColor: Colors.black.withValues(
                        alpha: 128,
                      ), // 0.5 * 255 ≈ 128
                      labelStyle: const TextStyle(color: Colors.white),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 8),
            if (trigger.useAI) ...[
              const Row(
                children: [
                  Icon(FontAwesomeIcons.robot, color: Colors.white, size: 14),
                  SizedBox(width: 8),
                  Text(
                    'Utilise l\'IA pour générer une réponse',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
              if (trigger.aiPrompt != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Prompt: ${trigger.aiPrompt}',
                  style: TextStyle(
                    color: Colors.white.withValues(
                      alpha: 179,
                    ), // 0.7 * 255 ≈ 179
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ] else ...[
              const Text(
                'Réponse:',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                trigger.response ?? '',
                style: const TextStyle(color: Colors.white),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.white),
                  onPressed: () {
                    _showTriggerDialog(context, trigger);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    _showDeleteDialog(context, trigger);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showTriggerDialog(
    BuildContext context, [
    AutoResponseTrigger? trigger,
  ]) {
    final isEditing = trigger != null;

    final nameController = TextEditingController(text: trigger?.name ?? '');
    final keywordsController = TextEditingController(
      text: trigger?.keywords.join(', ') ?? '',
    );
    final responseController = TextEditingController(
      text: trigger?.response ?? '',
    );
    final aiPromptController = TextEditingController(
      text: trigger?.aiPrompt ?? '',
    );

    bool useAI = trigger?.useAI ?? false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.black,
              title: Text(
                isEditing
                    ? 'Modifier le déclencheur'
                    : 'Ajouter un déclencheur',
                style: const TextStyle(color: Colors.white),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NeonTextField(
                      controller: nameController,
                      labelText: 'Nom',
                      hintText: 'Ex: Salutations',
                    ),
                    const SizedBox(height: 16),
                    NeonTextField(
                      controller: keywordsController,
                      labelText: 'Mots-clés (séparés par des virgules)',
                      hintText: 'Ex: bonjour, salut, hello',
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text(
                          'Utiliser l\'IA pour la réponse',
                          style: TextStyle(color: Colors.white),
                        ),
                        const Spacer(),
                        Switch(
                          value: useAI,
                          onChanged: (value) {
                            setState(() {
                              useAI = value;
                            });
                          },
                          activeColor: NeonTheme.neonPurple,
                          activeTrackColor: Colors.white.withValues(
                            alpha: 128,
                          ), // 0.5 * 255 ≈ 128
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (useAI) ...[
                      NeonTextField(
                        controller: aiPromptController,
                        labelText: 'Prompt pour l\'IA (optionnel)',
                        hintText: 'Instructions spécifiques pour l\'IA',
                        maxLines: 3,
                      ),
                    ] else ...[
                      NeonTextField(
                        controller: responseController,
                        labelText: 'Réponse automatique',
                        hintText: 'Ex: Bonjour ! Comment puis-je vous aider ?',
                        maxLines: 3,
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    'Annuler',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Valider les champs
                    if (nameController.text.trim().isEmpty ||
                        keywordsController.text.trim().isEmpty ||
                        (!useAI && responseController.text.trim().isEmpty)) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Veuillez remplir tous les champs obligatoires',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Extraire les mots-clés
                    final keywords =
                        keywordsController.text
                            .split(',')
                            .map((k) => k.trim())
                            .where((k) => k.isNotEmpty)
                            .toList();

                    final newTrigger = AutoResponseTrigger(
                      id: isEditing ? trigger.id : const Uuid().v4(),
                      name: nameController.text.trim(),
                      keywords: keywords,
                      response: useAI ? null : responseController.text.trim(),
                      useAI: useAI,
                      aiPrompt:
                          useAI && aiPromptController.text.trim().isNotEmpty
                              ? aiPromptController.text.trim()
                              : null,
                      isEnabled: isEditing ? trigger.isEnabled : true,
                    );

                    final notifier = ref.read(
                      autoResponseTriggersProvider.notifier,
                    );
                    if (isEditing) {
                      notifier.updateTrigger(newTrigger);
                    } else {
                      notifier.addTrigger(newTrigger);
                    }

                    Navigator.of(context).pop();
                  },
                  child: Text(
                    isEditing ? 'Mettre à jour' : 'Ajouter',
                    style: const TextStyle(color: NeonTheme.neonGreen),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteDialog(BuildContext context, AutoResponseTrigger trigger) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Supprimer le déclencheur',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer le déclencheur "${trigger.name}" ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                ref
                    .read(autoResponseTriggersProvider.notifier)
                    .removeTrigger(trigger.id);
                Navigator.of(context).pop();
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
