import 'dart:convert';

class AnalyticsData {
  final String id;
  final DateTime date;
  final AnalyticsType type;
  final Map<String, dynamic> data;

  AnalyticsData({
    required this.id,
    required this.date,
    required this.type,
    required this.data,
  });

  AnalyticsData copyWith({
    String? id,
    DateTime? date,
    AnalyticsType? type,
    Map<String, dynamic>? data,
  }) {
    return AnalyticsData(
      id: id ?? this.id,
      date: date ?? this.date,
      type: type ?? this.type,
      data: data ?? this.data,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.millisecondsSinceEpoch,
      'type': type.index,
      'data': data,
    };
  }

  factory AnalyticsData.fromMap(Map<String, dynamic> map) {
    return AnalyticsData(
      id: map['id'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
      type: AnalyticsType.values[map['type']],
      data: Map<String, dynamic>.from(map['data']),
    );
  }

  String toJson() => json.encode(toMap());

  factory AnalyticsData.fromJson(String source) => AnalyticsData.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AnalyticsData(id: $id, date: $date, type: $type, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is AnalyticsData &&
      other.id == id &&
      other.date == date &&
      other.type == type &&
      other.data.toString() == data.toString();
  }

  @override
  int get hashCode {
    return id.hashCode ^
      date.hashCode ^
      type.hashCode ^
      data.hashCode;
  }
}

enum AnalyticsType {
  sales,
  customers,
  inventory,
  messaging,
  tasks,
  custom
}

class SalesAnalytics {
  static Map<String, dynamic> createData({
    required double totalSales,
    required int orderCount,
    required double averageOrderValue,
    required Map<String, double> productSales,
    required Map<String, double> categorySales,
    required Map<String, int> salesByDay,
  }) {
    return {
      'totalSales': totalSales,
      'orderCount': orderCount,
      'averageOrderValue': averageOrderValue,
      'productSales': productSales,
      'categorySales': categorySales,
      'salesByDay': salesByDay,
    };
  }
}

class CustomerAnalytics {
  static Map<String, dynamic> createData({
    required int totalCustomers,
    required int newCustomers,
    required int returningCustomers,
    required double customerLifetimeValue,
    required Map<String, int> customersByRegion,
    required Map<String, int> customersBySource,
  }) {
    return {
      'totalCustomers': totalCustomers,
      'newCustomers': newCustomers,
      'returningCustomers': returningCustomers,
      'customerLifetimeValue': customerLifetimeValue,
      'customersByRegion': customersByRegion,
      'customersBySource': customersBySource,
    };
  }
}

class InventoryAnalytics {
  static Map<String, dynamic> createData({
    required int totalProducts,
    required int lowStockProducts,
    required int outOfStockProducts,
    required Map<String, int> stockByCategory,
    required Map<String, double> inventoryValue,
    required List<Map<String, dynamic>> topSellingProducts,
  }) {
    return {
      'totalProducts': totalProducts,
      'lowStockProducts': lowStockProducts,
      'outOfStockProducts': outOfStockProducts,
      'stockByCategory': stockByCategory,
      'inventoryValue': inventoryValue,
      'topSellingProducts': topSellingProducts,
    };
  }
}

class MessagingAnalytics {
  static Map<String, dynamic> createData({
    required int totalMessages,
    required int sentMessages,
    required int receivedMessages,
    required Map<String, int> messagesByChannel,
    required Map<String, int> messagesByDay,
    required double responseRate,
    required double averageResponseTime,
  }) {
    return {
      'totalMessages': totalMessages,
      'sentMessages': sentMessages,
      'receivedMessages': receivedMessages,
      'messagesByChannel': messagesByChannel,
      'messagesByDay': messagesByDay,
      'responseRate': responseRate,
      'averageResponseTime': averageResponseTime,
    };
  }
}

class TaskAnalytics {
  static Map<String, dynamic> createData({
    required int totalTasks,
    required int completedTasks,
    required int pendingTasks,
    required int overdueTasks,
    required Map<String, int> tasksByStatus,
    required Map<String, int> tasksByAssignee,
    required Map<String, int> tasksByPriority,
  }) {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'pendingTasks': pendingTasks,
      'overdueTasks': overdueTasks,
      'tasksByStatus': tasksByStatus,
      'tasksByAssignee': tasksByAssignee,
      'tasksByPriority': tasksByPriority,
    };
  }
}
