import 'dart:convert';
import 'gemini_model.dart';

/// Configuration pour l'API Gemini
class GeminiConfig {
  /// Clé API pour accéder à l'API Gemini
  final String apiKey;
  
  /// Modèle Gemini à utiliser
  final GeminiModel model;
  
  /// Température pour la génération (0.0 à 1.0)
  final double temperature;
  
  /// Nombre maximum de tokens en sortie
  final int maxOutputTokens;
  
  /// Constructeur
  GeminiConfig({
    required this.apiKey,
    required this.model,
    this.temperature = 0.7,
    this.maxOutputTokens = 2048,
  });
  
  /// Créer une configuration par défaut
  factory GeminiConfig.defaultConfig() {
    return GeminiConfig(
      apiKey: '',
      model: GeminiModel.geminiPro,
      temperature: 0.7,
      maxOutputTokens: 2048,
    );
  }
  
  /// Créer une copie avec des modifications
  GeminiConfig copyWith({
    String? apiKey,
    GeminiModel? model,
    double? temperature,
    int? maxOutputTokens,
  }) {
    return GeminiConfig(
      apiKey: apiKey ?? this.apiKey,
      model: model ?? this.model,
      temperature: temperature ?? this.temperature,
      maxOutputTokens: maxOutputTokens ?? this.maxOutputTokens,
    );
  }
  
  /// Convertir en Map
  Map<String, dynamic> toMap() {
    return {
      'apiKey': apiKey,
      'model': model.index,
      'temperature': temperature,
      'maxOutputTokens': maxOutputTokens,
    };
  }
  
  /// Créer à partir d'une Map
  factory GeminiConfig.fromMap(Map<String, dynamic> map) {
    return GeminiConfig(
      apiKey: map['apiKey'] ?? '',
      model: GeminiModel.values[map['model'] ?? 0],
      temperature: map['temperature']?.toDouble() ?? 0.7,
      maxOutputTokens: map['maxOutputTokens']?.toInt() ?? 2048,
    );
  }
  
  /// Convertir en JSON
  String toJson() => json.encode(toMap());
  
  /// Créer à partir de JSON
  factory GeminiConfig.fromJson(String source) => 
      GeminiConfig.fromMap(json.decode(source));
}
