import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class CategoriesScreen extends ConsumerWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Catégories'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: categoriesAsync.when(
          data: (categories) {
            return _buildCategoriesList(context, ref, categories);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: NeonTheme.neonPurple,
        onPressed: () {
          _showCategoryDialog(context, ref);
        },
        child: const Icon(Icons.add, color: Colors.black),
      ),
    );
  }

  Widget _buildCategoriesList(
    BuildContext context,
    WidgetRef ref,
    List<ProductCategory> categories,
  ) {
    if (categories.isEmpty) {
      return const Center(
        child: Text(
          'Aucune catégorie trouvée',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(context, ref, category);
      },
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    WidgetRef ref,
    ProductCategory category,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: NeonCard(
        color: NeonTheme.neonPurple,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Prix: ${category.price.toStringAsFixed(0)} FCFA',
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 179,
                          ), // 0.7 * 255 ≈ 179
                          fontSize: 16,
                        ),
                      ),
                      if (category.description != null &&
                          category.description!.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          category.description!,
                          style: TextStyle(
                            color: Colors.white.withValues(
                              alpha: 179,
                            ), // 0.7 * 255 ≈ 179
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.edit,
                    color: NeonTheme.neonTurquoise,
                    size: 20,
                  ),
                  onPressed: () {
                    _showCategoryDialog(context, ref, category);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red, size: 20),
                  onPressed: () {
                    _showDeleteConfirmation(context, ref, category);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCategoryDialog(
    BuildContext context,
    WidgetRef ref, [
    ProductCategory? category,
  ]) {
    final isEdit = category != null;
    final nameController = TextEditingController(
      text: isEdit ? category.name : '',
    );
    final priceController = TextEditingController(
      text: isEdit ? category.price.toString() : '',
    );
    final descriptionController = TextEditingController(
      text: isEdit ? category.description ?? '' : '',
    );
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: Text(
            isEdit ? 'Modifier la Catégorie' : 'Ajouter une Catégorie',
            style: const TextStyle(color: Colors.white),
          ),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonTextField(
                  controller: nameController,
                  labelText: 'Nom de la Catégorie',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un nom';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                NeonTextField(
                  controller: priceController,
                  labelText: 'Prix (FCFA)',
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un prix';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Veuillez entrer un nombre valide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                NeonTextField(
                  controller: descriptionController,
                  labelText: 'Description (optionnelle)',
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                if (!formKey.currentState!.validate()) return;

                final inventoryService = ref.read(inventoryServiceProvider);

                final updatedCategory = ProductCategory(
                  id: isEdit ? category.id : const Uuid().v4(),
                  name: nameController.text,
                  price: double.parse(priceController.text),
                  description:
                      descriptionController.text.isNotEmpty
                          ? descriptionController.text
                          : null,
                  createdAt: isEdit ? category.createdAt : DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                if (isEdit) {
                  await inventoryService.updateCategory(updatedCategory);
                } else {
                  await inventoryService.addCategory(updatedCategory);
                }

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(categoriesProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: Text(
                isEdit ? 'Mettre à jour' : 'Ajouter',
                style: const TextStyle(color: NeonTheme.neonPurple),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    ProductCategory category,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Confirmer la suppression',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la catégorie ${category.name} ? Cette action supprimera également tous les produits associés à cette catégorie.',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final inventoryService = ref.read(inventoryServiceProvider);

                // Supprimer la catégorie
                await inventoryService.deleteCategory(category.id);

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(categoriesProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
