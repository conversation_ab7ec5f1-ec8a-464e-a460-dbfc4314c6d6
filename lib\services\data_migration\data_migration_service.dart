import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';

// Provider pour le service de migration de données
final dataMigrationServiceProvider = Provider<DataMigrationService>((ref) {
  return DataMigrationService();
});

/// Service responsable de la migration des données lors des mises à jour de l'application
class DataMigrationService {
  static const String _versionKey = 'app_version';
  static const String _dataBackupKey = 'data_backup';
  
  /// Vérifie si une migration est nécessaire et l'exécute si besoin
  Future<void> checkAndMigrate() async {
    try {
      // Obtenir la version actuelle de l'application
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      
      // Obtenir la version stockée
      final prefs = await SharedPreferences.getInstance();
      final storedVersion = prefs.getString(_versionKey);
      
      debugPrint('Version actuelle: $currentVersion');
      debugPrint('Version stockée: $storedVersion');
      
      // Si c'est la première installation ou une mise à jour
      if (storedVersion == null) {
        // Première installation, sauvegarder la version
        await prefs.setString(_versionKey, currentVersion);
        debugPrint('Première installation, version sauvegardée: $currentVersion');
        return;
      }
      
      // Si c'est une mise à jour
      if (storedVersion != currentVersion) {
        debugPrint('Mise à jour détectée: $storedVersion -> $currentVersion');
        
        // Sauvegarder les données existantes
        await _backupExistingData();
        
        // Mettre à jour la version stockée
        await prefs.setString(_versionKey, currentVersion);
        debugPrint('Version mise à jour: $currentVersion');
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification de migration: $e');
    }
  }
  
  /// Sauvegarde toutes les données existantes
  Future<void> _backupExistingData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Récupérer toutes les clés et valeurs
      final allKeys = prefs.getKeys();
      final dataMap = <String, dynamic>{};
      
      // Exclure certaines clés qui ne doivent pas être sauvegardées
      final excludedKeys = [_versionKey, _dataBackupKey];
      
      for (final key in allKeys) {
        if (excludedKeys.contains(key)) continue;
        
        // Récupérer la valeur en fonction du type
        if (prefs.containsKey(key)) {
          if (prefs.getString(key) != null) {
            dataMap[key] = prefs.getString(key);
          } else if (prefs.getBool(key) != null) {
            dataMap[key] = prefs.getBool(key);
          } else if (prefs.getInt(key) != null) {
            dataMap[key] = prefs.getInt(key);
          } else if (prefs.getDouble(key) != null) {
            dataMap[key] = prefs.getDouble(key);
          } else if (prefs.getStringList(key) != null) {
            dataMap[key] = prefs.getStringList(key);
          }
        }
      }
      
      // Sauvegarder la sauvegarde
      final backupJson = json.encode(dataMap);
      await prefs.setString(_dataBackupKey, backupJson);
      
      debugPrint('Sauvegarde des données effectuée: ${dataMap.keys.length} clés');
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des données: $e');
    }
  }
  
  /// Restaure les données à partir de la sauvegarde
  Future<bool> restoreFromBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final backupJson = prefs.getString(_dataBackupKey);
      
      if (backupJson == null) {
        debugPrint('Aucune sauvegarde trouvée');
        return false;
      }
      
      final dataMap = json.decode(backupJson) as Map<String, dynamic>;
      
      // Restaurer chaque clé
      for (final entry in dataMap.entries) {
        final key = entry.key;
        final value = entry.value;
        
        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        }
      }
      
      debugPrint('Restauration des données effectuée: ${dataMap.keys.length} clés');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la restauration des données: $e');
      return false;
    }
  }
}
