import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:ncrm/providers/theme_provider.dart';
import '../theme/neon_theme.dart';
// Import supprimé car non utilisé

class NeonAppScaffold extends ConsumerStatefulWidget {
  final Widget child;
  final String title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final bool showDrawer;

  const NeonAppScaffold({
    super.key,
    required this.child,
    required this.title,
    this.actions,
    this.floatingActionButton,
    this.showDrawer = true,
  });

  @override
  ConsumerState<NeonAppScaffold> createState() => _NeonAppScaffoldState();
}

class _NeonAppScaffoldState extends ConsumerState<NeonAppScaffold> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final appTheme = ref.watch(themeProvider);
    final theme = appTheme.themeData;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Image.asset('assets/images/logo/logo.png', height: 30, width: 30),
            const SizedBox(width: 10),
            Text(
              'HCP-DESIGN CRM',
              style: TextStyle(
                color:
                    appTheme
                        .primaryColor, // Utiliser la couleur primaire du thème
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ],
        ),
        backgroundColor:
            theme.appBarTheme.backgroundColor ??
            theme
                .primaryColorDark, // Utiliser la couleur de fond de l'AppBar du thème
        foregroundColor:
            theme.appBarTheme.foregroundColor ??
            theme
                .colorScheme
                .onPrimary, // Utiliser la couleur du texte de l'AppBar du thème
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color:
                  theme.appBarTheme.foregroundColor ??
                  theme.colorScheme.onPrimary,
            ),
            onPressed: () {},
          ),
          IconButton(
            icon: Icon(
              Icons.settings_outlined,
              color:
                  theme.appBarTheme.foregroundColor ??
                  theme.colorScheme.onPrimary,
            ),
            onPressed: () {},
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: CircleAvatar(
              radius: 16,
              backgroundColor:
                  appTheme
                      .primaryColor, // Utiliser la couleur primaire du thème
              child: Text(
                'A',
                style: TextStyle(
                  color:
                      theme
                          .colorScheme
                          .onPrimary, // Utiliser la couleur sur primaire du thème
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient:
              appTheme.mainGradient, // Utiliser le dégradé principal du thème
          color:
              theme
                  .scaffoldBackgroundColor, // Ou une couleur de fond si pas de dégradé
        ),
        child: Row(
          children: [
            // Barre latérale toujours visible
            _buildDrawer(),
            // Contenu principal
            Expanded(
              child: Container(
                // Le fond du contenu principal héritera du dégradé/couleur du corps
                // ou vous pouvez définir une couleur spécifique si nécessaire, par exemple theme.canvasColor
                child: widget.child,
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  Widget _buildDrawer() {
    final appTheme = ref.watch(themeProvider);
    final theme = appTheme.themeData;

    return Container(
      width: 70,
      decoration: BoxDecoration(
        gradient:
            appTheme
                .sidebarGradient, // Utiliser le dégradé de la sidebar du thème
        color:
            theme.drawerTheme.backgroundColor ??
            theme.colorScheme.surface, // Couleur de fond du drawer
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(70), // Ombre plus subtile
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
        border: Border(
          right: BorderSide(color: theme.dividerColor.withAlpha(100), width: 1),
        ),
      ),
      child: Column(
        children: [
          // Avatar de l'utilisateur en haut
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      appTheme
                          .primaryColor, // Utiliser la couleur primaire du thème
                  child: Text(
                    'A',
                    style: TextStyle(
                      color:
                          theme
                              .colorScheme
                              .onPrimary, // Utiliser la couleur sur primaire du thème
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'administrateur',
                  style: TextStyle(
                    color:
                        theme.textTheme.bodySmall?.color?.withAlpha(180) ??
                        Colors.white.withAlpha(180),
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Divider(
            color: theme.dividerColor.withAlpha(150),
            height: 1,
            thickness: 1,
          ),
          // Icônes de la barre latérale
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.dashboard_outlined,
                  title: 'Tableau de bord',
                  index: 0,
                  route: '/dashboard',
                ),
                _buildDrawerItem(
                  icon: Icons.message_outlined,
                  title: 'Messagerie',
                  index: 1,
                  route: '/messaging',
                ),
                _buildDrawerItem(
                  icon: Icons.people_outline,
                  title: 'Clients',
                  index: 2,
                  route: '/customers',
                ),
                _buildDrawerItem(
                  icon: Icons.check_circle_outline,
                  title: 'Tâches',
                  index: 3,
                  route: '/tasks',
                ),
                _buildDrawerItem(
                  icon: Icons.trending_up,
                  title: 'Opportunités',
                  index: 4,
                  route: '/opportunities',
                ),
                _buildDrawerItem(
                  icon: FontAwesomeIcons.boxOpen,
                  title: 'Inventaire',
                  index: 5,
                  route: '/inventory',
                ),
                _buildDrawerItem(
                  icon: Icons.book_outlined,
                  title: 'Base de connaissance',
                  index: 6, // Adjusted index
                  route: '/knowledge',
                ),
              ],
            ),
          ),
          // Bouton de paramètres en bas
          _buildDrawerItem(
            icon: Icons.settings_outlined,
            title: 'Paramètres',
            index: 7, // Adjusted index
            route: '/settings',
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
    required String route,
  }) {
    // Déterminer si l'élément est sélectionné en fonction de la route actuelle
    final currentRoute = GoRouterState.of(context).matchedLocation;
    final isSelected = currentRoute.startsWith(route);

    // Mettre à jour l'index sélectionné si nécessaire
    if (isSelected && _selectedIndex != index) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _selectedIndex = index;
        });
      });
    }

    final color = isSelected ? NeonTheme.neonCyan : Colors.white.withAlpha(150);

    // Déterminer la couleur de fond en fonction de la sélection
    Color? backgroundColor = Colors.transparent;
    if (isSelected) {
      backgroundColor = NeonTheme.neonCyan.withAlpha(30);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedIndex = index;
          });

          if (route == '/logout') {
            // Logique de déconnexion
          } else {
            context.go(route);
          }
        },
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border(
              left: BorderSide(
                color: isSelected ? NeonTheme.neonCyan : Colors.transparent,
                width: 3,
              ),
            ),
          ),
          child: Tooltip(
            message: title,
            preferBelow: false,
            child: Center(child: Icon(icon, color: color, size: 24)),
          ),
        ),
      ),
    );
  }
}
