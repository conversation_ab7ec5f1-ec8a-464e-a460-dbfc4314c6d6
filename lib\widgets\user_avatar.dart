import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/theme_provider.dart';

class UserAvatar extends ConsumerStatefulWidget {
  final String? imageUrl;
  final String name;
  final double size;

  const UserAvatar({
    super.key,
    this.imageUrl,
    required this.name,
    this.size = 40,
  });

  @override
  ConsumerState<UserAvatar> createState() => _UserAvatarState();
}

class _UserAvatarState extends ConsumerState<UserAvatar> {
  bool _hasImageError = false;

  @override
  void didUpdateWidget(UserAvatar oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Réinitialiser l'état d'erreur si l'URL de l'image a changé
    if (oldWidget.imageUrl != widget.imageUrl) {
      debugPrint(
        'UserAvatar: URL de l\'image changée de ${oldWidget.imageUrl} vers ${widget.imageUrl}',
      );
      setState(() {
        _hasImageError = false;
      });

      // Vider le cache d'images pour forcer le rechargement
      if (widget.imageUrl != null && widget.imageUrl!.isNotEmpty) {
        _clearImageCache();
      }
    }
  }

  void _clearImageCache() {
    try {
      // Vider le cache d'images pour forcer le rechargement
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      debugPrint('UserAvatar: Cache d\'images vidé');
    } catch (e) {
      debugPrint('UserAvatar: Erreur lors du vidage du cache: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Si l'URL de l'image est fournie et non vide, et qu'il n'y a pas d'erreur
    if (widget.imageUrl != null &&
        widget.imageUrl!.isNotEmpty &&
        !_hasImageError) {
      debugPrint('UserAvatar: Affichage de l\'image: ${widget.imageUrl}');

      // Vérifier si c'est un chemin de fichier local ou une URL
      if (widget.imageUrl!.startsWith('http://') ||
          widget.imageUrl!.startsWith('https://')) {
        // C'est une URL réseau
        debugPrint('UserAvatar: C\'est une URL réseau: ${widget.imageUrl}');
        return CircleAvatar(
          radius: widget.size / 2,
          backgroundColor: Colors.black12,
          backgroundImage: NetworkImage(widget.imageUrl!),
          onBackgroundImageError: (exception, stackTrace) {
            debugPrint('Erreur de chargement de l\'image réseau: $exception');
            debugPrint('URL de l\'image: ${widget.imageUrl}');
            debugPrint('Stack trace: $stackTrace');
            // Marquer qu'il y a une erreur et déclencher un rebuild
            if (mounted) {
              setState(() {
                _hasImageError = true;
              });
            }
          },
          child: null,
        );
      } else {
        // C'est un chemin de fichier local
        debugPrint(
          'UserAvatar: C\'est un chemin de fichier local: ${widget.imageUrl}',
        );
        final file = File(widget.imageUrl!);

        // Vérifier si le fichier existe
        final fileExists = file.existsSync();
        debugPrint('UserAvatar: Le fichier existe: $fileExists');

        if (fileExists) {
          return CircleAvatar(
            radius: widget.size / 2,
            backgroundColor: Colors.black12,
            backgroundImage: FileImage(file),
            onBackgroundImageError: (exception, stackTrace) {
              debugPrint('Erreur de chargement de l\'image locale: $exception');
              debugPrint('Chemin du fichier: ${file.path}');
              debugPrint('Stack trace: $stackTrace');
              // Marquer qu'il y a une erreur et déclencher un rebuild
              if (mounted) {
                setState(() {
                  _hasImageError = true;
                });
              }
            },
            child: null,
          );
        } else {
          debugPrint(
            'UserAvatar: Le fichier n\'existe pas, affichage des initiales. Chemin: ${file.path}',
          );
        }
      }
    } else {
      debugPrint(
        'UserAvatar: Pas d\'URL d\'image ou erreur, affichage des initiales',
      );
    }

    // Si l'image n'est pas disponible, afficher les initiales
    final initials = _getInitials(widget.name);
    final color = _getAvatarColor(widget.name);

    return CircleAvatar(
      radius: widget.size / 2,
      backgroundColor: color,
      child: Text(
        initials,
        style: TextStyle(
          color:
              ref.watch(isDarkThemeProvider)
                  ? Colors.white
                  : ref.watch(primaryTextColorProvider),
          fontWeight: FontWeight.bold,
          fontSize: widget.size * 0.4,
        ),
      ),
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '';

    final parts = name.trim().split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.length == 1 && parts[0].isNotEmpty) {
      return parts[0][0].toUpperCase();
    }

    return '';
  }

  Color _getAvatarColor(String name) {
    // Générer une couleur basée sur le nom
    final colors = [
      Colors.red,
      Colors.pink,
      Colors.purple,
      Colors.deepPurple,
      Colors.indigo,
      Colors.blue,
      Colors.lightBlue,
      Colors.cyan,
      Colors.teal,
      Colors.green,
      Colors.lightGreen,
      Colors.lime,
      Colors.amber,
      Colors.orange,
      Colors.deepOrange,
      Colors.brown,
    ];

    // Utiliser la somme des codes de caractères pour déterminer l'index
    int sum = 0;
    for (int i = 0; i < name.length; i++) {
      sum += name.codeUnitAt(i);
    }

    return colors[sum % colors.length];
  }
}
