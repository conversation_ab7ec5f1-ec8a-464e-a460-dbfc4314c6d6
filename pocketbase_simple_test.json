{"collections": [{"name": "contacts", "type": "base", "system": false, "schema": [{"name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "email", "type": "email", "system": false, "required": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"name": "phone", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]}