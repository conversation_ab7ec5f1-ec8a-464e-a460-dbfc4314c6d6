import 'dart:convert';
import 'message.dart';

class Contact {
  final String id;
  final String name;
  final String? phoneNumber;
  final String? email;
  final String? avatarUrl;
  final bool isOnline;
  final DateTime lastSeen;
  final Map<MessageChannel, bool> channels;
  final int unreadCount;
  final bool isFavorite;
  final Map<String, dynamic>? metadata;

  Contact({
    required this.id,
    required this.name,
    this.phoneNumber,
    this.email,
    this.avatarUrl,
    this.isOnline = false,
    required this.lastSeen,
    required this.channels,
    this.unreadCount = 0,
    this.isFavorite = false,
    this.metadata,
  });

  Contact copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    String? avatarUrl,
    bool? isOnline,
    DateTime? lastSeen,
    Map<MessageChannel, bool>? channels,
    int? unreadCount,
    bool? isFavorite,
    Map<String, dynamic>? metadata,
  }) {
    return Contact(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      channels: channels ?? this.channels,
      unreadCount: unreadCount ?? this.unreadCount,
      isFavorite: isFavorite ?? this.isFavorite,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toMap() {
    final channelsMap = {};
    channels.forEach((key, value) {
      channelsMap[key.index.toString()] = value;
    });

    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'avatarUrl': avatarUrl,
      'isOnline': isOnline,
      'lastSeen': lastSeen.millisecondsSinceEpoch,
      'channels': channelsMap,
      'unreadCount': unreadCount,
      'isFavorite': isFavorite,
      'metadata': metadata,
    };
  }

  factory Contact.fromMap(Map<String, dynamic> map) {
    final channelsMap = <MessageChannel, bool>{};
    if (map['channels'] != null) {
      (map['channels'] as Map<String, dynamic>).forEach((key, value) {
        channelsMap[MessageChannel.values[int.parse(key)]] = value;
      });
    }

    return Contact(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      phoneNumber: map['phoneNumber'],
      email: map['email'],
      avatarUrl: map['avatarUrl'],
      isOnline: map['isOnline'] ?? false,
      lastSeen: DateTime.fromMillisecondsSinceEpoch(map['lastSeen']),
      channels: channelsMap,
      unreadCount: map['unreadCount'] ?? 0,
      isFavorite: map['isFavorite'] ?? false,
      metadata: map['metadata'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Contact.fromJson(String source) => Contact.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Contact(id: $id, name: $name, phoneNumber: $phoneNumber, email: $email, avatarUrl: $avatarUrl, isOnline: $isOnline, lastSeen: $lastSeen, channels: $channels, unreadCount: $unreadCount, isFavorite: $isFavorite, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Contact &&
      other.id == id &&
      other.name == name &&
      other.phoneNumber == phoneNumber &&
      other.email == email &&
      other.avatarUrl == avatarUrl &&
      other.isOnline == isOnline &&
      other.lastSeen == lastSeen &&
      other.unreadCount == unreadCount &&
      other.isFavorite == isFavorite;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      phoneNumber.hashCode ^
      email.hashCode ^
      avatarUrl.hashCode ^
      isOnline.hashCode ^
      lastSeen.hashCode ^
      unreadCount.hashCode ^
      isFavorite.hashCode;
  }
}
