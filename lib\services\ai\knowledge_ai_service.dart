import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ai/ai_message.dart';
import '../../models/knowledge_article.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/conversation.dart';
import '../../models/customer/customer.dart';
import '../knowledge_service.dart';
import '../ai/ai_service.dart';
import '../messaging/messaging_service.dart';
import '../customer/customer_service.dart';

// Provider pour le service d'IA basé sur la base de connaissances
final knowledgeAIServiceProvider = Provider<KnowledgeAIService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  final knowledgeService = ref.watch(knowledgeServiceProvider);
  final messagingService = ref.watch(messagingServiceProvider);
  final customerService = ref.watch(customerServiceProvider);

  return KnowledgeAIService(
    aiService: aiService,
    knowledgeService: knowledgeService,
    messagingService: messagingService,
    customerService: customerService,
  );
});

// Énumération des intentions client
enum CustomerIntent {
  purchase,
  information,
  support,
  quote,
  complaint,
  tracking,
  other,
}

// Énumération des types de réponses automatiques
enum AutoResponseType {
  welcome,
  faq,
  intent,
  lead,
  followUp,
  quote,
  support,
  custom,
}

class KnowledgeAIService {
  final AIService aiService;
  final KnowledgeService knowledgeService;
  final MessagingService messagingService;
  final CustomerService customerService;

  KnowledgeAIService({
    required this.aiService,
    required this.knowledgeService,
    required this.messagingService,
    required this.customerService,
  });

  // Générer un message de bienvenue
  Future<Map<String, dynamic>> _generateWelcomeResponse(
    Customer customer,
  ) async {
    // Récupérer l'article de bienvenue dans la base de connaissances
    final welcomeArticles = await knowledgeService.searchArticles(
      'message de bienvenue',
    );

    String content;
    if (welcomeArticles.isNotEmpty) {
      // Utiliser l'article de bienvenue comme base
      content = welcomeArticles.first.content
          .replaceAll('{nom}', customer.name)
          .replaceAll('{client}', customer.name);
    } else {
      // Message par défaut si aucun article n'est trouvé
      content = '''
    Bonjour ${customer.name} 👋

    Merci de nous contacter! Je suis l'assistant virtuel de HCP-DESIGN.

    Comment puis-je vous aider aujourd'hui?
    - Information sur nos produits
    - Demande de devis
    - Suivi de commande
    - Service après-vente

    N'hésitez pas à me poser vos questions, je suis là pour vous aider!
    ''';
    }
    return {'content': content};
  }

  // Générer une réponse basée sur l'intention
  Future<Map<String, dynamic>> _generateResponseBasedOnIntent(
    String message,
    CustomerIntent intent,
    Conversation conversation,
    Customer customer,
  ) async {
    // Rechercher des articles pertinents dans la base de connaissances
    List<KnowledgeArticle> relevantArticles = [];
    String? mediaUrl;
    MessageType? mediaType;
    String? mediaName;

    // Exemple simple: si l'intention est information et le message contient "image produit"
    if (intent == CustomerIntent.information &&
        message.toLowerCase().contains('image produit')) {
      mediaUrl = 'https://example.com/product_image.jpg'; // URL d'exemple
      mediaType = MessageType.image;
      mediaName = 'product_image.jpg';
    }

    switch (intent) {
      case CustomerIntent.information:
        relevantArticles = await knowledgeService.searchArticles(message);
        break;
      case CustomerIntent.purchase:
        relevantArticles = await knowledgeService.getArticlesByCategory(
          'Produits',
        );
        break;
      case CustomerIntent.quote:
        relevantArticles = await knowledgeService.getArticlesByCategory(
          'Tarifs',
        );
        break;
      case CustomerIntent.support:
        relevantArticles = await knowledgeService.getArticlesByCategory(
          'Service client',
        );
        break;
      case CustomerIntent.tracking:
        relevantArticles = await knowledgeService.getArticlesByCategory(
          'Livraison',
        );
        break;
      case CustomerIntent.complaint:
        relevantArticles = await knowledgeService.getArticlesByCategory(
          'Service client',
        );
        break;
      case CustomerIntent.other:
        relevantArticles = await knowledgeService.searchArticles(message);
        break;
    }

    // Construire le contexte pour l'IA
    String context = '';
    if (relevantArticles.isNotEmpty) {
      for (final article in relevantArticles.take(3)) {
        context += '${article.title}:\n${article.content}\n\n';
      }
    }

    // Générer une réponse personnalisée avec l'IA
    final prompt = '''
    Tu es un assistant virtuel pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

    Informations sur le client:
    - Nom: ${customer.name}
    - Statut: ${customer.status.name}
    - Dernier contact: ${customer.lastContact.toString().split(' ')[0]}

    Contexte de la base de connaissances:
    $context

    Message du client: "$message"

    Réponds de manière professionnelle, concise et utile. Utilise les informations de la base de connaissances si pertinent.
    Si tu ne connais pas la réponse, propose de transférer à un conseiller humain.
    Personnalise ta réponse en fonction de l'intention détectée: ${intent.name}.
    ''';

    final responseText = await aiService.sendMessage([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    return {
      'content': responseText,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'mediaName': mediaName,
    };
  }

  // Analyser un message et générer une réponse automatique
  Future<Message?> processIncomingMessage(
    Message message,
    Conversation conversation,
  ) async {
    try {
      // 1. Vérifier si c'est un nouveau contact
      final isNewContact = await _isNewContact(message.sender);

      // 2. Créer ou mettre à jour le contact dans le CRM
      final customer = await _createOrUpdateCustomer(message, conversation);

      // 3. Déterminer l'intention du client
      final intent = await _detectCustomerIntent(message.content, conversation);

      // 4. Générer une réponse appropriée
      Map<String, dynamic> responseData;

      if (isNewContact) {
        // Message de bienvenue pour un nouveau contact
        responseData = await _generateWelcomeResponse(customer);
      } else {
        // Réponse basée sur l'intention et le contexte
        responseData = await _generateResponseBasedOnIntent(
          message.content,
          intent,
          conversation,
          customer,
        );
      }

      final String responseContent = responseData['content'] as String;
      final String? mediaUrl = responseData['mediaUrl'] as String?;
      final MessageType? mediaType = responseData['mediaType'] as MessageType?;
      final String? mediaName = responseData['mediaName'] as String?;
      // final int? mediaSize = responseData['mediaSize'] as int?; // mediaSize n'est pas encore géré ici

      // 5. Créer et envoyer la réponse
      final responseMessage = Message(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        contactId: message.contactId,
        content: responseContent,
        isUser: false,
        timestamp: DateTime.now(),
        channel: message.channel,
        status: MessageStatus.sent,
        isAIGenerated: true,
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        mediaName: mediaName,
        // mediaSize: mediaSize, // Décommenter si mediaSize est géré
      );

      // 6. Enregistrer la réponse dans la conversation (et l'envoyer via le service de messagerie)
      // La méthode sendMessage de MessagingService doit pouvoir gérer les médias
      await messagingService.sendMessage(
        contactId: message.contactId,
        content: responseContent, // Le contenu textuel est toujours envoyé
        channel: message.channel,
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        mediaName: mediaName,
        isAIGenerated: true, // Assurer que ce drapeau est passé
        // mediaSize: mediaSize, // Décommenter si mediaSize est géré
      );

      return responseMessage;
    } catch (e) {
      debugPrint('Erreur lors du traitement du message: $e');
      return null;
    }
  }

  // Vérifier si c'est un nouveau contact
  Future<bool> _isNewContact(String contactId) async {
    final customer = await customerService.getCustomerByContactId(contactId);
    return customer == null;
  }

  // Créer ou mettre à jour un client dans le CRM
  Future<Customer> _createOrUpdateCustomer(
    Message message,
    Conversation conversation,
  ) async {
    // Vérifier si le client existe déjà
    Customer? customer = await customerService.getCustomerByContactId(
      message.sender,
    );

    if (customer == null) {
      // Créer un nouveau client
      customer = Customer(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: conversation.participantName,
        contactId: message.sender,
        phone: message.sender.startsWith('+') ? message.sender : null,
        email: null,
        source: 'Chatbot - ${message.channel.toString().split('.').last}',
        createdAt: DateTime.now(),
        lastContact: DateTime.now(),
        status: CustomerStatus.lead,
        notes: 'Créé automatiquement par le chatbot',
        tags: [
          'nouveau',
          message.channel.toString().split('.').last.toLowerCase(),
        ],
      );

      await customerService.addCustomer(customer);
    } else {
      // Mettre à jour le client existant
      customer = customer.copyWith(lastContact: DateTime.now());

      await customerService.updateCustomer(customer);
    }

    return customer;
  }

  // Détecter l'intention du client
  Future<CustomerIntent> _detectCustomerIntent(
    String message,
    Conversation conversation,
  ) async {
    // Utiliser l'IA pour détecter l'intention
    final prompt = '''
    Analyse le message suivant et détermine l'intention principale du client.
    Réponds uniquement avec l'une des catégories suivantes:
    - PURCHASE: intention d'achat ou demande de produit
    - INFORMATION: demande d'information générale
    - SUPPORT: besoin d'assistance ou problème technique
    - QUOTE: demande de devis ou prix
    - COMPLAINT: réclamation ou insatisfaction
    - TRACKING: suivi de commande ou livraison
    - OTHER: autre intention

    Message: "$message"
    ''';

    final response = await aiService.sendMessage([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    // Analyser la réponse
    if (response.toLowerCase().contains('purchase')) {
      return CustomerIntent.purchase;
    }
    if (response.toLowerCase().contains('information')) {
      return CustomerIntent.information;
    }
    if (response.toLowerCase().contains('support')) {
      return CustomerIntent.support;
    }
    if (response.toLowerCase().contains('quote')) {
      return CustomerIntent.quote;
    }
    if (response.toLowerCase().contains('complaint')) {
      return CustomerIntent.complaint;
    }
    if (response.toLowerCase().contains('tracking')) {
      return CustomerIntent.tracking;
    }

    return CustomerIntent.other;
  }
}
