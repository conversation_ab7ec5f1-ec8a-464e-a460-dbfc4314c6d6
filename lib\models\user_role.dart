import 'dart:convert';

enum UserRoleType { admin, manager, employee, user }

class UserRole {
  final String id;
  final String name;
  final UserRoleType type;
  final List<String> permissions;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  UserRole({
    required this.id,
    required this.name,
    required this.type,
    required this.permissions,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  // Créer un rôle à partir d'un Map
  factory UserRole.fromMap(Map<String, dynamic> map) {
    return UserRole(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: UserRoleType.values.firstWhere(
        (e) => e.toString() == 'UserRoleType.${map['type']}',
        orElse: () => UserRoleType.user,
      ),
      permissions: List<String>.from(map['permissions'] ?? []),
      description: map['description'],
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  // Créer un rôle à partir d'un JSON
  factory UserRole.fromJson(String source) =>
      UserRole.fromMap(json.decode(source));

  // Convertir le rôle en Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.toString().split('.').last,
      'permissions': permissions,
      if (description != null) 'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Convertir le rôle en JSON
  String toJson() => json.encode(toMap());

  // Créer une copie du rôle avec des modifications
  UserRole copyWith({
    String? id,
    String? name,
    UserRoleType? type,
    List<String>? permissions,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserRole(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      permissions: permissions ?? this.permissions,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserRole(id: $id, name: $name, type: $type, permissions: $permissions)';
  }
}

// Liste des permissions disponibles dans l'application
class AppPermissions {
  // Permissions générales
  static const String viewDashboard = 'view_dashboard';
  static const String viewAnalytics = 'view_analytics';

  // Permissions liées aux utilisateurs
  static const String viewUsers = 'view_users';
  static const String createUser = 'create_user';
  static const String editUser = 'edit_user';
  static const String deleteUser = 'delete_user';
  static const String manageRoles = 'manage_roles';

  // Permissions liées à l'inventaire
  static const String viewInventory = 'view_inventory';
  static const String createProduct = 'create_product';
  static const String editProduct = 'edit_product';
  static const String deleteProduct = 'delete_product';
  static const String manageCategories = 'manage_categories';

  // Permissions liées à la facturation
  static const String viewInvoices = 'view_invoices';
  static const String createInvoice = 'create_invoice';
  static const String editInvoice = 'edit_invoice';
  static const String deleteInvoice = 'delete_invoice';

  // Permissions liées à la messagerie
  static const String viewMessages = 'view_messages';
  static const String sendMessages = 'send_messages';
  static const String configureMessaging = 'configure_messaging';

  // Permissions liées à l'IA
  static const String useAI = 'use_ai';
  static const String configureAI = 'configure_ai';

  // Permissions liées à la base de connaissances
  static const String viewKnowledge = 'view_knowledge';
  static const String createKnowledge = 'create_knowledge';
  static const String editKnowledge = 'edit_knowledge';
  static const String deleteKnowledge = 'delete_knowledge';

  // Permissions liées aux paramètres
  static const String viewSettings = 'view_settings';
  static const String editSettings = 'edit_settings';

  // Obtenir toutes les permissions
  static List<String> getAllPermissions() {
    return [
      viewDashboard,
      viewAnalytics,
      viewUsers,
      createUser,
      editUser,
      deleteUser,
      manageRoles,
      viewInventory,
      createProduct,
      editProduct,
      deleteProduct,
      manageCategories,
      viewInvoices,
      createInvoice,
      editInvoice,
      deleteInvoice,
      viewMessages,
      sendMessages,
      configureMessaging,
      useAI,
      configureAI,
      viewKnowledge,
      createKnowledge,
      editKnowledge,
      deleteKnowledge,
      viewSettings,
      editSettings,
    ];
  }

  // Obtenir les permissions pour un rôle spécifique
  static List<String> getPermissionsForRole(UserRoleType roleType) {
    switch (roleType) {
      case UserRoleType.admin:
        return getAllPermissions();
      case UserRoleType.manager:
        return [
          viewDashboard,
          viewAnalytics,
          viewUsers,
          createUser,
          editUser,
          viewInventory,
          createProduct,
          editProduct,
          deleteProduct,
          manageCategories,
          viewInvoices,
          createInvoice,
          editInvoice,
          deleteInvoice,
          viewMessages,
          sendMessages,
          useAI,
          viewKnowledge,
          createKnowledge,
          editKnowledge,
          deleteKnowledge,
          viewSettings,
        ];
      case UserRoleType.employee:
        return [
          viewDashboard,
          viewInventory,
          createProduct,
          editProduct,
          viewInvoices,
          createInvoice,
          viewMessages,
          sendMessages,
          useAI,
          viewKnowledge,
        ];
      case UserRoleType.user:
        return [
          viewDashboard,
          viewInventory,
          viewInvoices,
          viewMessages,
          sendMessages,
          useAI,
          viewKnowledge,
        ];
    }
  }
}
