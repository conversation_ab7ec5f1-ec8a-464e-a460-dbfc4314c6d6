import 'invoice_item.dart';

// Type de facture
enum InvoiceType {
  invoice, // Facture
  quote, // Devis
  receipt, // Reçu
  proforma, // Proforma
}

// Statut de la facture
enum InvoiceStatus {
  draft, // Brouillon
  pending, // En attente
  paid, // Payée
  overdue, // En retard
  cancelled, // Annulée
  refunded, // Remboursée
  converted, // Convertie (pour les devis)
}

class Invoice {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerEmail;
  final String? customerPhone;
  final DateTime date;
  final DateTime dueDate;
  final List<InvoiceItem> items;
  final double subtotal;
  final double tax;
  final double discount;
  final double total;
  final String? notes;
  final InvoiceStatus status;
  final InvoiceType type;
  final String? paymentMethod;
  final DateTime? paymentDate;
  final String? reference;
  final String? deliveryAddress;
  final String? deliveryDetails;
  final String? billingAddress;
  final Map<String, dynamic>? metadata;

  Invoice({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerEmail,
    this.customerPhone,
    required this.date,
    required this.dueDate,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.discount,
    required this.total,
    this.notes,
    required this.status,
    required this.type,
    this.paymentMethod,
    this.paymentDate,
    this.reference,
    this.deliveryAddress,
    this.deliveryDetails,
    this.billingAddress,
    this.metadata,
  });

  // Créer une copie avec des modifications
  Invoice copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    DateTime? date,
    DateTime? dueDate,
    List<InvoiceItem>? items,
    double? subtotal,
    double? tax,
    double? discount,
    double? total,
    String? notes,
    InvoiceStatus? status,
    InvoiceType? type,
    String? paymentMethod,
    DateTime? paymentDate,
    String? reference,
    String? deliveryAddress,
    String? deliveryDetails,
    String? billingAddress,
    Map<String, dynamic>? metadata,
  }) {
    return Invoice(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerEmail: customerEmail ?? this.customerEmail,
      customerPhone: customerPhone ?? this.customerPhone,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      type: type ?? this.type,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentDate: paymentDate ?? this.paymentDate,
      reference: reference ?? this.reference,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      deliveryDetails: deliveryDetails ?? this.deliveryDetails,
      billingAddress: billingAddress ?? this.billingAddress,
      metadata: metadata ?? this.metadata,
    );
  }

  // Conversion depuis JSON
  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'] as String,
      customerId: json['customer_id'] as String,
      customerName: json['customer_name'] as String,
      customerEmail: json['customer_email'] as String?,
      customerPhone: json['customer_phone'] as String?,
      date: DateTime.parse(json['date'] as String),
      dueDate: DateTime.parse(json['due_date'] as String),
      items:
          (json['items'] as List)
              .map((item) => InvoiceItem.fromJson(item as Map<String, dynamic>))
              .toList(),
      subtotal: (json['subtotal'] as num).toDouble(),
      tax: (json['tax'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      notes: json['notes'] as String?,
      status: InvoiceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => InvoiceStatus.draft,
      ),
      type: InvoiceType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InvoiceType.invoice,
      ),
      paymentMethod: json['payment_method'] as String?,
      paymentDate:
          json['payment_date'] != null
              ? DateTime.parse(json['payment_date'] as String)
              : null,
      reference: json['reference'] as String?,
      deliveryAddress: json['delivery_address'] as String?,
      deliveryDetails: json['delivery_details'] as String?,
      billingAddress: json['billing_address'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_email': customerEmail,
      'customer_phone': customerPhone,
      'date': date.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'discount': discount,
      'total': total,
      'notes': notes,
      'status': status.name,
      'type': type.name,
      'payment_method': paymentMethod,
      'payment_date': paymentDate?.toIso8601String(),
      'reference': reference,
      'delivery_address': deliveryAddress,
      'delivery_details': deliveryDetails,
      'billing_address': billingAddress,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'Invoice{id: $id, customerName: $customerName, total: $total, status: ${status.name}}';
  }
}
