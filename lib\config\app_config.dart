import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppConfig {
  // Configuration générale
  final String appName;
  final String appVersion;

  // Configuration de l'IA
  final String aiProvider; // 'openai', 'anthropic', 'gemini'
  final String aiApiKey;
  final String? aiOrganizationId; // Pour OpenAI
  final String aiModel;

  // Configuration de la base de données
  final String databaseUrl;
  final String? supabaseUrl;
  final String? supabaseKey;

  // Configuration des fonctionnalités
  final bool enableOfflineMode;
  final bool enablePushNotifications;
  final bool enableAnalytics;

  AppConfig({
    required this.appName,
    required this.appVersion,
    required this.aiProvider,
    required this.aiApiKey,
    this.aiOrganizationId,
    required this.aiModel,
    required this.databaseUrl,
    this.supabaseUrl,
    this.supabaseKey,
    required this.enableOfflineMode,
    required this.enablePushNotifications,
    required this.enableAnalytics,
  });

  // Charger la configuration depuis les variables d'environnement
  factory AppConfig.fromEnv() {
    return AppConfig(
      appName: dotenv.env['APP_NAME'] ?? 'HCP-DESIGN CRM',
      appVersion: dotenv.env['APP_VERSION'] ?? '1.0.0',
      aiProvider: dotenv.env['AI_PROVIDER'] ?? 'openai',
      aiApiKey: dotenv.env['AI_API_KEY'] ?? '',
      aiOrganizationId: dotenv.env['AI_ORGANIZATION_ID'],
      aiModel: dotenv.env['AI_MODEL'] ?? 'gpt-3.5-turbo',
      databaseUrl: dotenv.env['DATABASE_URL'] ?? '',
      supabaseUrl: dotenv.env['SUPABASE_URL'],
      supabaseKey: dotenv.env['SUPABASE_ANON_KEY'],
      enableOfflineMode: dotenv.env['ENABLE_OFFLINE_MODE'] == 'true',
      enablePushNotifications:
          dotenv.env['ENABLE_PUSH_NOTIFICATIONS'] == 'true',
      enableAnalytics: dotenv.env['ENABLE_ANALYTICS'] == 'true',
    );
  }

  // Créer une copie de la configuration avec des modifications
  AppConfig copyWith({
    String? appName,
    String? appVersion,
    String? aiProvider,
    String? aiApiKey,
    String? aiOrganizationId,
    String? aiModel,
    String? databaseUrl,
    String? supabaseUrl,
    String? supabaseKey,
    bool? enableOfflineMode,
    bool? enablePushNotifications,
    bool? enableAnalytics,
  }) {
    return AppConfig(
      appName: appName ?? this.appName,
      appVersion: appVersion ?? this.appVersion,
      aiProvider: aiProvider ?? this.aiProvider,
      aiApiKey: aiApiKey ?? this.aiApiKey,
      aiOrganizationId: aiOrganizationId ?? this.aiOrganizationId,
      aiModel: aiModel ?? this.aiModel,
      databaseUrl: databaseUrl ?? this.databaseUrl,
      supabaseUrl: supabaseUrl ?? this.supabaseUrl,
      supabaseKey: supabaseKey ?? this.supabaseKey,
      enableOfflineMode: enableOfflineMode ?? this.enableOfflineMode,
      enablePushNotifications:
          enablePushNotifications ?? this.enablePushNotifications,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
    );
  }
}

// Provider pour la configuration de l'application
final appConfigProvider = Provider<AppConfig>((ref) {
  return AppConfig.fromEnv();
});
