import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

class LowStockAlertsScreen extends ConsumerWidget {
  const LowStockAlertsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lowStockProductsAsync = ref.watch(lowStockProductsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alertes de Stock Faible'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: lowStockProductsAsync.when(
          data: (products) {
            return categoriesAsync.when(
              data: (categories) {
                return _buildAlertsList(context, products, categories);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error:
                  (error, stack) => Center(
                    child: Text(
                      'Erreur: $error',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
        ),
      ),
    );
  }

  Widget _buildAlertsList(
    BuildContext context,
    List<Product> products,
    List<ProductCategory> categories,
  ) {
    if (products.isEmpty) {
      return const Center(
        child: Text(
          'Aucun produit en stock faible',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      );
    }

    // Trier les produits par statut (critique d'abord, puis faible)
    products.sort((a, b) {
      if (a.stockStatus == StockStatus.critical &&
          b.stockStatus != StockStatus.critical) {
        return -1;
      } else if (a.stockStatus != StockStatus.critical &&
          b.stockStatus == StockStatus.critical) {
        return 1;
      } else {
        return a.quantity.compareTo(b.quantity);
      }
    });

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        final category = categories.firstWhere(
          (c) => c.id == product.categoryId,
          orElse:
              () => ProductCategory(
                id: '',
                name: 'Inconnue',
                price: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
        );

        return _buildAlertCard(context, product, category);
      },
    );
  }

  Widget _buildAlertCard(
    BuildContext context,
    Product product,
    ProductCategory category,
  ) {
    Color statusColor;
    IconData statusIcon;

    switch (product.stockStatus) {
      case StockStatus.critical:
        statusColor = Colors.red;
        statusIcon = Icons.error_outline;
        break;
      case StockStatus.low:
        statusColor = Colors.orange;
        statusIcon = Icons.warning_amber_outlined;
        break;
      default:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle_outline;
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: NeonCard(
        color: statusColor,
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                shape: BoxShape.circle,
              ),
              child: Icon(statusIcon, color: statusColor, size: 30),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Catégorie: ${category.name}',
                    style: TextStyle(
                      color: Colors.white.withValues(
                        alpha: 179,
                      ), // 0.7 * 255 ≈ 179
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        'Quantité: ${product.quantity}',
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 179,
                          ), // 0.7 * 255 ≈ 179
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(
                            alpha: 77,
                          ), // 0.3 * 255 ≈ 77
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          product.getStatusLabel(),
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            NeonButton(
              text: 'Voir',
              color: statusColor,
              onPressed: () {
                context.push('/inventory/product/${product.id}');
              },
              small: true,
            ),
            const SizedBox(width: 8),
            Consumer(
              builder: (context, ref, _) {
                return NeonButton(
                  text: 'Réapprovisionner',
                  color: Colors.green,
                  onPressed: () {
                    _showRestockDialog(context, ref, product);
                  },
                  small: true,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRestockDialog(
    BuildContext context,
    WidgetRef ref,
    Product product,
  ) {
    final TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Réapprovisionner le Stock',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Combien d\'unités souhaitez-vous ajouter à ${product.name} ?',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Quantité',
                  labelStyle: TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.green),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.green, width: 2),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity <= 0) return;

                final inventoryService = ref.read(inventoryServiceProvider);
                final updatedProduct = product.copyWith(
                  quantity: product.quantity + quantity,
                  updatedAt: DateTime.now(),
                );

                await inventoryService.updateProduct(updatedProduct);

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(lowStockProductsProvider);
                // ignore: unused_result
                ref.refresh(productsProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text(
                'Confirmer',
                style: TextStyle(color: Colors.green),
              ),
            ),
          ],
        );
      },
    );
  }
}
