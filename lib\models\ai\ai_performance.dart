import 'dart:convert';
import 'ai_provider.dart';

class AIPerformance {
  final AIProviderType provider;
  final String model;
  final int promptLength;
  final int responseLength;
  final int duration; // en millisecondes
  final DateTime timestamp;

  AIPerformance({
    required this.provider,
    required this.model,
    required this.promptLength,
    required this.responseLength,
    required this.duration,
    required this.timestamp,
  });

  // Calculer le coût approximatif (en USD)
  double get estimatedCost {
    switch (provider) {
      case AIProviderType.openai:
        if (model.contains('gpt-4')) {
          return (promptLength / 1000 * 0.03) + (responseLength / 1000 * 0.06);
        } else {
          return (promptLength / 1000 * 0.0015) + (responseLength / 1000 * 0.002);
        }
      case AIProviderType.anthropic:
        if (model.contains('opus')) {
          return (promptLength / 1000 * 0.015) + (responseLength / 1000 * 0.075);
        } else if (model.contains('sonnet')) {
          return (promptLength / 1000 * 0.003) + (responseLength / 1000 * 0.015);
        } else {
          return (promptLength / 1000 * 0.0005) + (responseLength / 1000 * 0.0025);
        }
      case AIProviderType.gemini:
        return (promptLength / 1000 * 0.0005) + (responseLength / 1000 * 0.0015);
      case AIProviderType.mistral:
        if (model.contains('large')) {
          return (promptLength / 1000 * 0.008) + (responseLength / 1000 * 0.024);
        } else if (model.contains('medium')) {
          return (promptLength / 1000 * 0.0027) + (responseLength / 1000 * 0.0081);
        } else {
          return (promptLength / 1000 * 0.0014) + (responseLength / 1000 * 0.0042);
        }
      case AIProviderType.groq:
        return (promptLength / 1000 * 0.0007) + (responseLength / 1000 * 0.0007);
      case AIProviderType.deepseek:
        return (promptLength / 1000 * 0.0014) + (responseLength / 1000 * 0.0028);
    }
  }

  // Calculer le taux de tokens par seconde
  double get tokensPerSecond {
    return responseLength / (duration / 1000);
  }

  AIPerformance copyWith({
    AIProviderType? provider,
    String? model,
    int? promptLength,
    int? responseLength,
    int? duration,
    DateTime? timestamp,
  }) {
    return AIPerformance(
      provider: provider ?? this.provider,
      model: model ?? this.model,
      promptLength: promptLength ?? this.promptLength,
      responseLength: responseLength ?? this.responseLength,
      duration: duration ?? this.duration,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'provider': provider.index,
      'model': model,
      'promptLength': promptLength,
      'responseLength': responseLength,
      'duration': duration,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  factory AIPerformance.fromMap(Map<String, dynamic> map) {
    return AIPerformance(
      provider: AIProviderType.values[map['provider']],
      model: map['model'] ?? '',
      promptLength: map['promptLength']?.toInt() ?? 0,
      responseLength: map['responseLength']?.toInt() ?? 0,
      duration: map['duration']?.toInt() ?? 0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
    );
  }

  String toJson() => json.encode(toMap());

  factory AIPerformance.fromJson(String source) => AIPerformance.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AIPerformance(provider: $provider, model: $model, promptLength: $promptLength, responseLength: $responseLength, duration: $duration, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is AIPerformance &&
      other.provider == provider &&
      other.model == model &&
      other.promptLength == promptLength &&
      other.responseLength == responseLength &&
      other.duration == duration &&
      other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return provider.hashCode ^
      model.hashCode ^
      promptLength.hashCode ^
      responseLength.hashCode ^
      duration.hashCode ^
      timestamp.hashCode;
  }
}
