import 'message.dart';

// Plateformes de messagerie supportées
enum MessagePlatform {
  whatsapp,
  facebook,
  instagram,
  telegram,
  email,
  sms,
  internal,
  app,
}

// Extension pour obtenir le nom d'affichage de la plateforme
extension MessagePlatformExtension on MessagePlatform {
  String get displayName {
    switch (this) {
      case MessagePlatform.whatsapp:
        return 'WhatsApp';
      case MessagePlatform.facebook:
        return 'Facebook Messenger';
      case MessagePlatform.instagram:
        return 'Instagram Direct';
      case MessagePlatform.telegram:
        return 'Telegram';
      case MessagePlatform.email:
        return 'Email';
      case MessagePlatform.sms:
        return 'SMS';
      case MessagePlatform.internal:
        return 'Messagerie interne';
      case MessagePlatform.app:
        return 'Application';
    }
  }

  // Convertir MessagePlatform en MessageChannel
  MessageChannel toMessageChannel() {
    switch (this) {
      case MessagePlatform.whatsapp:
        return MessageChannel.whatsapp;
      case MessagePlatform.facebook:
        return MessageChannel.facebook;
      case MessagePlatform.instagram:
        return MessageChannel.instagram;
      case MessagePlatform.telegram:
        // Pour la compatibilité, on utilise internal pour Telegram
        return MessageChannel.internal;
      case MessagePlatform.email:
        return MessageChannel.email;
      case MessagePlatform.sms:
        return MessageChannel.sms;
      case MessagePlatform.internal:
        return MessageChannel.internal;
      case MessagePlatform.app:
        return MessageChannel.app;
    }
  }
}
