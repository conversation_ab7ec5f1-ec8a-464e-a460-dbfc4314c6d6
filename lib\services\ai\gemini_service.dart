import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_generative_ai/google_generative_ai.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/ai/gemini_config.dart';
import '../../models/ai/gemini_model.dart';

// Provider pour le service Gemini
final geminiServiceProvider = Provider<GeminiService>((ref) {
  return GeminiService();
});

// Service pour interagir avec l'API Gemini
class GeminiService {
  late GenerativeModel _model;
  bool _isInitialized = false;
  GeminiConfig? _config;
  ChatSession? _chatSession;

  // Vérifier si le service est configuré
  bool get isConfigured => _config != null && _config!.apiKey.isNotEmpty;

  // Obtenir la configuration actuelle
  GeminiConfig? get config => _config;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Charger la configuration depuis les préférences
      await _loadConfig();

      if (_config != null && _config!.apiKey.isNotEmpty) {
        _initializeModel();
      } else {
        // Essayer de charger depuis les variables d'environnement
        final apiKey = dotenv.env['GEMINI_API_KEY'];
        if (apiKey != null && apiKey.isNotEmpty) {
          _config = GeminiConfig(apiKey: apiKey, model: GeminiModel.geminiPro);
          _initializeModel();
          await _saveConfig();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de l\'initialisation du service Gemini: $e');
      }
    }
  }

  // Initialiser le modèle avec la configuration actuelle
  void _initializeModel() {
    if (_config == null || _config!.apiKey.isEmpty) return;

    try {
      _model = GenerativeModel(
        model: _config!.model.apiString,
        apiKey: _config!.apiKey,
        generationConfig: GenerationConfig(
          temperature: _config!.temperature,
          maxOutputTokens: _config!.maxOutputTokens,
        ),
      );

      _isInitialized = true;
      if (kDebugMode) {
        print('Service Gemini initialisé avec succès');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de l\'initialisation du modèle Gemini: $e');
      }
    }
  }

  // Charger la configuration depuis les préférences
  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString('gemini_config');

      if (configJson != null) {
        _config = GeminiConfig.fromJson(configJson);
      } else {
        _config = GeminiConfig.defaultConfig();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors du chargement de la configuration Gemini: $e');
      }
      _config = GeminiConfig.defaultConfig();
    }
  }

  // Sauvegarder la configuration dans les préférences
  Future<void> _saveConfig() async {
    if (_config == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('gemini_config', _config!.toJson());
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la sauvegarde de la configuration Gemini: $e');
      }
    }
  }

  // Sauvegarder une nouvelle configuration
  Future<void> saveConfig(GeminiConfig config) async {
    _config = config;
    await _saveConfig();
    _initializeModel();

    // Réinitialiser la session de chat
    _resetChatSession();
  }

  // Réinitialiser la session de chat
  void resetChat() {
    _resetChatSession();
  }

  // Réinitialiser la session de chat (méthode privée)
  void _resetChatSession() {
    _chatSession = null;
  }

  // Générer du contenu avec Gemini
  Future<String> generateContent(String prompt) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!isConfigured) {
      return "Veuillez configurer l'API Gemini dans les paramètres.";
    }

    try {
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);

      return response.text ?? 'Aucune réponse générée';
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la génération de contenu: $e');
      }
      return 'Erreur: $e';
    }
  }

  // Envoyer un message dans une conversation
  Future<String> sendChatMessage(String message) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!isConfigured) {
      return "Veuillez configurer l'API Gemini dans les paramètres.";
    }

    try {
      // Créer une nouvelle session de chat si nécessaire
      _chatSession ??= _model.startChat();

      // Envoyer le message
      final response = await _chatSession!.sendMessage(Content.text(message));

      return response.text ?? 'Aucune réponse générée';
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de l\'envoi du message: $e');
      }
      return 'Erreur: $e';
    }
  }

  // Générer une réponse à un email
  Future<String> generateEmailResponse({
    required String clientName,
    required String subject,
    String? context,
    String tone = 'professionnel',
  }) async {
    final prompt = '''
    Génère une réponse par email à un client nommé $clientName.

    Sujet de l'email: $subject

    ${context != null ? 'Contexte: $context\n' : ''}

    La réponse doit être:
    1. Sur un ton $tone
    2. Professionnelle et courtoise
    3. Personnalisée pour le client
    4. En français
    5. Avec une formule de politesse adaptée

    Format: Inclure l'objet, la salutation, le corps du message et la signature.
    ''';

    return generateContent(prompt);
  }

  // Analyser le sentiment d'un texte
  Future<String> analyzeSentiment(String text) async {
    final prompt = '''
    Analyse le sentiment du texte suivant et classe-le comme positif, négatif ou neutre.
    Fournis également une brève explication de ton analyse.

    Texte à analyser:
    "$text"

    Format de la réponse:
    Sentiment: [Positif/Négatif/Neutre]
    Explication: [Brève explication]
    ''';

    return generateContent(prompt);
  }

  // Résumer un texte
  Future<String> summarizeText(String text, {int maxWords = 100}) async {
    final prompt = '''
    Résume le texte suivant en $maxWords mots maximum.

    Texte à résumer:
    "$text"
    ''';

    return generateContent(prompt);
  }

  // Extraire des informations clés d'un texte
  Future<String> extractKeyInformation(String text, List<String> fields) async {
    final fieldsStr = fields.map((f) => '- $f').join('\n');

    final prompt = '''
    Extrais les informations suivantes du texte ci-dessous:
    $fieldsStr

    Texte:
    "$text"

    Format de la réponse:
    Pour chaque champ, indique la valeur extraite ou "Non trouvé" si l'information n'est pas présente.
    ''';

    return generateContent(prompt);
  }

  // Générer une réponse à un message client
  Future<String> generateCustomerResponse(
    String customerMessage,
    String context,
  ) async {
    final prompt = '''
    En tant que représentant du service client, génère une réponse professionnelle et empathique au message suivant.

    Contexte sur le client:
    $context

    Message du client:
    "$customerMessage"

    La réponse doit être:
    1. Professionnelle et courtoise
    2. Personnalisée en fonction du contexte fourni
    3. Concise et claire
    4. En français
    ''';

    return generateContent(prompt);
  }
}
