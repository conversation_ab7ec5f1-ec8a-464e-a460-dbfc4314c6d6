import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import '../../models/user.dart';
import '../../services/auth_service.dart';
import '../../services/user_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';
import '../../widgets/user_avatar.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _bioController = TextEditingController();
  final _departmentController = TextEditingController();
  final _imagePicker = ImagePicker();
  File? _imageFile;
  bool _isLoading = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _bioController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  void _initializeControllers() {
    final user = ref.read(currentUserProvider);
    if (user != null) {
      _nameController.text = user.name;
      _emailController.text = user.email;
      _phoneController.text = user.phone ?? '';
      _bioController.text = user.bio ?? '';
      _departmentController.text = user.department ?? '';
    }
  }

  // Méthode pour forcer le rafraîchissement de l'interface utilisateur
  void _forceRefresh() {
    debugPrint('Forçage du rafraîchissement de l\'interface utilisateur');

    // Forcer la mise à jour du provider currentUserProvider avec la nouvelle méthode
    ref.read(currentUserProvider.notifier).forceUpdate();

    // Attendre un peu puis rafraîchir l'état local
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // Réinitialiser l'état d'erreur d'image si nécessaire
          _imageFile = null;
        });
      }
    });
  }

  // Méthode pour vérifier si l'URL de l'avatar est valide
  bool _isValidAvatarUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    // Vérifier si c'est une URL réseau valide
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // Vérifier si c'est une URL d'avatar valide et non une URL de produit
      if (url.contains('5000') ||
          url.contains('product') ||
          url.contains('coque')) {
        debugPrint('URL d\'avatar invalide détectée: $url');
        return false;
      }
      return true;
    }

    // Vérifier si c'est un chemin de fichier local valide
    final file = File(url);
    final fileExists = file.existsSync();

    if (!fileExists) {
      debugPrint('Fichier d\'avatar non trouvé: $url');
      return false;
    }

    return true;
  }

  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        setState(() {
          _imageFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection de l\'image: $e'),
          ),
        );
      }
    }
  }

  Future<String?> _saveImage() async {
    if (_imageFile == null) return null;

    try {
      debugPrint('Début de la sauvegarde de l\'image...');
      debugPrint('Chemin de l\'image source: ${_imageFile!.path}');

      // Sur le web, on ne peut pas sauvegarder de fichiers localement
      // On retourne simplement le chemin de l'image temporaire
      if (kIsWeb) {
        debugPrint('Plateforme web détectée, utilisation du chemin temporaire');
        return _imageFile!.path;
      }

      // Obtenir le répertoire de documents (seulement sur les plateformes natives)
      final appDir = await getApplicationDocumentsDirectory();
      debugPrint('Répertoire de documents: ${appDir.path}');

      final avatarsDir = Directory('${appDir.path}/avatars');
      debugPrint('Répertoire des avatars: ${avatarsDir.path}');

      // Créer le répertoire s'il n'existe pas
      if (!await avatarsDir.exists()) {
        debugPrint('Création du répertoire des avatars...');
        await avatarsDir.create(recursive: true);
      }

      // Générer un nom de fichier unique
      final fileName = 'avatar_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final savedImagePath = '${avatarsDir.path}/$fileName';
      debugPrint('Chemin de sauvegarde de l\'image: $savedImagePath');

      // Copier le fichier
      final savedFile = await _imageFile!.copy(savedImagePath);
      debugPrint('Image copiée avec succès: ${savedFile.path}');

      // Vérifier que le fichier existe
      final fileExists = await File(savedImagePath).exists();
      debugPrint('Le fichier existe: $fileExists');

      return savedImagePath;
    } catch (e, stackTrace) {
      debugPrint('Erreur lors de la sauvegarde de l\'image: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde de l\'image: $e'),
          ),
        );
      }
      return null;
    }
  }

  Future<void> _saveProfile() async {
    final user = ref.read(currentUserProvider);
    if (user == null) {
      debugPrint(
        'Utilisateur non connecté, impossible de sauvegarder le profil',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('Début de la sauvegarde du profil...');
      debugPrint('Utilisateur actuel: ${user.toString()}');

      // Sauvegarder l'image si elle a été modifiée
      String? avatarPath;
      if (_imageFile != null) {
        debugPrint('Image sélectionnée, sauvegarde en cours...');
        avatarPath = await _saveImage();

        // Si la sauvegarde de l'image a échoué, on continue sans mettre à jour l'avatar
        if (avatarPath == null) {
          debugPrint(
            'Impossible de sauvegarder l\'image, on continue sans mettre à jour l\'avatar',
          );
        } else {
          debugPrint('Image sauvegardée avec succès: $avatarPath');
        }
      } else {
        debugPrint(
          'Aucune nouvelle image sélectionnée, conservation de l\'avatar actuel: ${user.avatar}',
        );
      }

      // Mettre à jour l'utilisateur
      final updatedUser = user.copyWith(
        name: _nameController.text,
        email: _emailController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        bio: _bioController.text.isEmpty ? null : _bioController.text,
        department:
            _departmentController.text.isEmpty
                ? null
                : _departmentController.text,
        avatar: avatarPath ?? user.avatar,
      );

      debugPrint('Utilisateur mis à jour: ${updatedUser.toString()}');
      debugPrint('Nouvel avatar: ${updatedUser.avatar}');

      final userService = ref.read(userServiceProvider);
      debugPrint(
        'Appel du service utilisateur pour mettre à jour le profil...',
      );
      final success = await userService.updateUser(updatedUser);

      debugPrint('Résultat de la mise à jour: $success');

      if (success) {
        // Vérifier que l'utilisateur a bien été mis à jour
        final updatedCurrentUser = ref.read(currentUserProvider);
        debugPrint(
          'Utilisateur actuel après mise à jour: ${updatedCurrentUser?.toString()}',
        );
        debugPrint(
          'Avatar actuel après mise à jour: ${updatedCurrentUser?.avatar}',
        );

        // Forcer le rafraîchissement de l'interface utilisateur
        _forceRefresh();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profil mis à jour avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        debugPrint('Échec de la mise à jour du profil');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la mise à jour du profil'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e, stackTrace) {
      debugPrint('Erreur lors de la mise à jour du profil: $e');
      debugPrint('Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEditing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Utilisateur non connecté')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil Utilisateur'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _buildProfileHeader(user),
                      const SizedBox(height: 24),
                      _buildProfileDetails(user),
                      const SizedBox(height: 24),
                      if (_isEditing)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            NeonButton(
                              text: 'Annuler',
                              icon: FontAwesomeIcons.xmark,
                              color: Colors.red,
                              onPressed: () {
                                setState(() {
                                  _isEditing = false;
                                  _imageFile = null;
                                  _initializeControllers();
                                });
                              },
                            ),
                            const SizedBox(width: 16),
                            NeonButton(
                              text: 'Enregistrer',
                              icon: FontAwesomeIcons.floppyDisk,
                              color: NeonTheme.neonGreen,
                              onPressed: _saveProfile,
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildProfileHeader(User user) {
    return Column(
      children: [
        GestureDetector(
          onTap: _isEditing ? _pickImage : null,
          child: Stack(
            children: [
              Container(
                decoration: NeonTheme.avatarDecoration(NeonTheme.neonCyan),
                child:
                    _imageFile != null
                        ? CircleAvatar(
                          radius: 60,
                          backgroundImage: FileImage(_imageFile!),
                          onBackgroundImageError: (exception, stackTrace) {
                            debugPrint(
                              'Erreur de chargement de l\'image sélectionnée: $exception',
                            );
                            // Fallback to initials avatar
                          },
                        )
                        : UserAvatar(
                          imageUrl:
                              _isValidAvatarUrl(user.avatar)
                                  ? user.avatar
                                  : null,
                          name: user.name,
                          size: 120,
                        ),
              ),
              if (_isEditing)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: NeonTheme.neonCyan,
                      shape: BoxShape.circle,
                      boxShadow: NeonTheme.neonShadow(NeonTheme.neonCyan),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Text(
          user.name,
          style: NeonTheme.neonTextStyle(
            NeonTheme.neonCyan,
          ).copyWith(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          user.email,
          style: TextStyle(
            color: Colors.white.withAlpha(179), // 0.7 * 255 ≈ 179
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: NeonTheme.neonPurple.withAlpha(128), // 0.5 * 255 ≈ 128
              width: 1,
            ),
          ),
          child: Text(
            'Rôle: ${user.role}',
            style: const TextStyle(
              color: NeonTheme.neonPurple,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileDetails(User user) {
    return NeonCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations personnelles',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _isEditing ? _buildEditableFields() : _buildDisplayFields(user),
          ],
        ),
      ),
    );
  }

  Widget _buildEditableFields() {
    return Column(
      children: [
        NeonTextField(
          controller: _nameController,
          labelText: 'Nom',
          prefixIcon: Icons.person,
        ),
        const SizedBox(height: 16),
        NeonTextField(
          controller: _emailController,
          labelText: 'Email',
          prefixIcon: Icons.email,
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: 16),
        NeonTextField(
          controller: _phoneController,
          labelText: 'Téléphone',
          prefixIcon: Icons.phone,
          keyboardType: TextInputType.phone,
        ),
        const SizedBox(height: 16),
        NeonTextField(
          controller: _departmentController,
          labelText: 'Département',
          prefixIcon: Icons.business,
        ),
        const SizedBox(height: 16),
        NeonTextField(
          controller: _bioController,
          labelText: 'Biographie',
          prefixIcon: Icons.info,
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildDisplayFields(User user) {
    return Column(
      children: [
        _buildInfoRow(Icons.phone, 'Téléphone', user.phone ?? 'Non spécifié'),
        const SizedBox(height: 12),
        _buildInfoRow(
          Icons.business,
          'Département',
          user.department ?? 'Non spécifié',
        ),
        const SizedBox(height: 12),
        _buildInfoRow(Icons.info, 'Biographie', user.bio ?? 'Non spécifié'),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: NeonTheme.neonCyan, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: Colors.white.withAlpha(179), // 0.7 * 255 ≈ 179
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
