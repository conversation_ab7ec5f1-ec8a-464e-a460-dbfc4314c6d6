import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/auth_service.dart';
import '../../theme/neon_theme.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec salutation
            Container(
              decoration: BoxDecoration(
                gradient: NeonTheme.cardGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: NeonTheme.neonShadow(NeonTheme.neonCyan, intensity: 0.5),
                border: Border.all(
                  color: NeonTheme.neonCyan.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  width: 1.5,
                ),
              ),
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bonjour, ${user?.name ?? "Utilisateur"}',
                    style: NeonTheme.neonTextStyle(NeonTheme.neonCyan).copyWith(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Bienvenue dans votre tableau de bord CRM',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 230), // 0.9 * 255 ≈ 230
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: NeonTheme.neonPurple.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Rôle: ${user?.role ?? "Utilisateur"}',
                          style: TextStyle(
                            color: NeonTheme.neonPurple,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (user?.department != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: NeonTheme.neonTurquoise.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                              width: 1,
                            ),
                          ),
                          child: Text(
                            'Département: ${user?.department}',
                            style: TextStyle(
                              color: NeonTheme.neonTurquoise,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Statistiques
            Text(
              'Statistiques',
              style: NeonTheme.neonTextStyle(NeonTheme.neonPurple).copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildStatCard(
                  context,
                  title: 'Clients',
                  value: '42',
                  icon: Icons.people,
                  color: NeonTheme.neonCyan,
                  onTap: () => context.go('/customers'),
                ),
                _buildStatCard(
                  context,
                  title: 'Opportunités',
                  value: '15',
                  icon: Icons.trending_up,
                  color: NeonTheme.neonGreen,
                  onTap: () => context.go('/opportunities'),
                ),
                _buildStatCard(
                  context,
                  title: 'Tâches',
                  value: '8',
                  icon: Icons.check_circle,
                  color: NeonTheme.neonTurquoise,
                  onTap: () => context.go('/tasks'),
                ),
                _buildStatCard(
                  context,
                  title: 'Messages',
                  value: '3',
                  icon: Icons.message,
                  color: NeonTheme.neonPurple,
                  onTap: () => context.go('/inbox'),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Activités récentes
            Text(
              'Activités récentes',
              style: NeonTheme.neonTextStyle(NeonTheme.neonTurquoise).copyWith(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                gradient: NeonTheme.cardGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: NeonTheme.neonShadow(NeonTheme.neonTurquoise, intensity: 0.3),
                border: Border.all(
                  color: NeonTheme.neonTurquoise.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                  width: 1,
                ),
              ),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 5,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
                  height: 1,
                  indent: 70,
                  endIndent: 16,
                ),
                itemBuilder: (context, index) {
                  final colors = [
                    NeonTheme.neonCyan,
                    NeonTheme.neonPurple,
                    NeonTheme.neonGreen,
                    NeonTheme.neonTurquoise,
                    NeonTheme.neonPink,
                  ];
                  final icons = [
                    Icons.person_add,
                    Icons.edit_document,
                    Icons.call_made,
                    Icons.email,
                    Icons.task_alt,
                  ];
                  final titles = [
                    'Nouveau client ajouté',
                    'Contrat mis à jour',
                    'Appel effectué',
                    'Email envoyé',
                    'Tâche terminée',
                  ];
                  final descriptions = [
                    'Entreprise ABC a été ajoutée',
                    'Contrat #1234 a été mis à jour',
                    'Appel avec Jean Dupont',
                    'Email envoyé à Marie Martin',
                    'Préparation de la présentation',
                  ];
                  
                  return ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: colors[index % colors.length].withValues(alpha: 128), // 0.5 * 255 ≈ 128
                          width: 1,
                        ),
                        boxShadow: NeonTheme.neonShadow(
                          colors[index % colors.length],
                          intensity: 0.3,
                        ),
                      ),
                      child: Icon(
                        icons[index % icons.length],
                        color: colors[index % colors.length],
                        size: 20,
                      ),
                    ),
                    title: Text(
                      titles[index % titles.length],
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      descriptions[index % descriptions.length],
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                      ),
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Il y a ${index + 1}h',
                        style: TextStyle(
                          color: colors[index % colors.length],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: NeonTheme.cardGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: NeonTheme.neonShadow(color, intensity: 0.5),
        border: Border.all(
          color: color.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 40, color: color),
                const SizedBox(height: 8),
                Text(
                  value,
                  style: NeonTheme.neonTextStyle(color).copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 230), // 0.9 * 255 ≈ 230
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
