import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../models/task/task.dart';
import '../../../services/task/task_service.dart';
import '../../../widgets/neon_card.dart';
import '../../../providers/theme_provider.dart';
import 'task_progress_bar.dart';

class TaskListView extends ConsumerWidget {
  final List<Task> tasks;

  const TaskListView({super.key, required this.tasks});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appTheme = ref.watch(themeProvider);

    return tasks.isEmpty
        ? Center(
          child: Text(
            'Aucune tâche trouvée',
            style: TextStyle(
              color:
                  ref.watch(isDarkThemeProvider)
                      ? Colors.white
                      : ref.watch(primaryTextColorProvider),
              fontSize: 16,
            ),
          ),
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: tasks.length,
          itemBuilder: (context, index) {
            final task = tasks[index];
            return _buildTaskCard(context, task, ref, appTheme);
          },
        );
  }

  Widget _buildTaskCard(
    BuildContext context,
    Task task,
    WidgetRef ref,
    dynamic appTheme,
  ) {
    final Color priorityColor = _getPriorityColor(task.priority);
    final bool isOverdue = task.isOverdue;

    return GestureDetector(
      onTap: () {
        context.push('/tasks/details/${task.id}');
      },
      child: NeonCard(
        color: isOverdue ? Colors.red : priorityColor,
        margin: const EdgeInsets.only(bottom: 16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Indicateur de statut
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getStatusColor(task.status),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),

                  // Titre de la tâche
                  Expanded(
                    child: Text(
                      task.title,
                      style: TextStyle(
                        color:
                            ref.watch(isDarkThemeProvider)
                                ? Colors.white
                                : ref.watch(primaryTextColorProvider),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Menu d'actions
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    color: Colors.black,
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, color: Colors.white, size: 18),
                                SizedBox(width: 8),
                                Text(
                                  'Modifier',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'status',
                            child: Row(
                              children: [
                                Icon(Icons.sync, color: Colors.white, size: 18),
                                SizedBox(width: 8),
                                Text(
                                  'Changer le statut',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 18),
                                SizedBox(width: 8),
                                Text(
                                  'Supprimer',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          context.push('/tasks/edit/${task.id}');
                          break;
                        case 'status':
                          _showStatusChangeDialog(context, task, ref);
                          break;
                        case 'delete':
                          _showDeleteConfirmation(context, task, ref);
                          break;
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Description de la tâche
              Text(
                task.description,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 230), // 0.9 * 255 ≈ 230
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // Barre de progression
              TaskProgressBar(task: task),

              const SizedBox(height: 8),

              // Informations supplémentaires
              Row(
                children: [
                  // Date d'échéance
                  Icon(
                    Icons.calendar_today,
                    color: isOverdue ? Colors.red : Colors.white,
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('dd/MM/yyyy').format(task.dueDate),
                    style: TextStyle(
                      color: isOverdue ? Colors.red : Colors.white,
                      fontWeight:
                          isOverdue ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),

                  // Temps restant
                  if (!isOverdue &&
                      task.status != TaskStatus.completed &&
                      task.status != TaskStatus.cancelled)
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: Text(
                        _getRemainingTimeText(task),
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 179,
                          ), // 0.7 * 255 ≈ 179
                          fontSize: 12,
                        ),
                      ),
                    ),

                  const Spacer(),

                  // Assigné à
                  if (task.assignedToName != null)
                    Row(
                      children: [
                        const Icon(Icons.person, color: Colors.white, size: 14),
                        const SizedBox(width: 4),
                        Text(
                          task.assignedToName!,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                ],
              ),

              // Tags
              if (task.tags.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children:
                        task.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withValues(
                                alpha: 77,
                              ), // 0.3 * 255 ≈ 77
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                color: Colors.white.withValues(
                                  alpha: 230,
                                ), // 0.9 * 255 ≈ 230
                                fontSize: 12,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.low:
        return Colors.green;
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
    }
  }

  String _getRemainingTimeText(Task task) {
    if (task.daysRemaining > 0) {
      return 'Dans ${task.daysRemaining} jour${task.daysRemaining > 1 ? 's' : ''}';
    } else if (task.hoursRemaining > 0) {
      return 'Dans ${task.hoursRemaining} heure${task.hoursRemaining > 1 ? 's' : ''}';
    } else {
      return 'Aujourd\'hui';
    }
  }

  void _showStatusChangeDialog(BuildContext context, Task task, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Changer le statut',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatusOption(
                context,
                'À faire',
                TaskStatus.todo,
                task,
                ref,
              ),
              _buildStatusOption(
                context,
                'En cours',
                TaskStatus.inProgress,
                task,
                ref,
              ),
              _buildStatusOption(
                context,
                'Terminée',
                TaskStatus.completed,
                task,
                ref,
              ),
              _buildStatusOption(
                context,
                'Annulée',
                TaskStatus.cancelled,
                task,
                ref,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusOption(
    BuildContext context,
    String label,
    TaskStatus status,
    Task task,
    WidgetRef ref,
  ) {
    return ListTile(
      leading: Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          color: _getStatusColor(status),
          shape: BoxShape.circle,
        ),
      ),
      title: Text(label, style: const TextStyle(color: Colors.white)),
      selected: task.status == status,
      selectedTileColor: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
      onTap: () {
        ref.read(tasksProvider.notifier).updateTaskStatus(task.id, status);
        Navigator.of(context).pop();
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, Task task, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Supprimer la tâche',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la tâche "${task.title}" ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                ref.read(tasksProvider.notifier).deleteTask(task.id);
                Navigator.of(context).pop();
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
