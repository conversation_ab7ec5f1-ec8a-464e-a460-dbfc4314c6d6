import 'dart:convert';
import 'invoice_item.dart';

enum InvoiceStatus { paid, pending, cancelled }

enum DeliveryLocation {
  bingerville,
  cocody,
  rivieraPalmeraie,
  rivieraBonoumin,
  adjame,
  abobo,
  yopougon,
  treichville,
  marcory,
  koumassi,
  plateau,
  anyama,
  songon,
  grandBassam,
}

// Extension pour obtenir un nom affichable pour DeliveryLocation
extension DeliveryLocationExtension on DeliveryLocation {
  String get displayName {
    switch (this) {
      case DeliveryLocation.bingerville:
        return 'Bingerville';
      case DeliveryLocation.cocody:
        return 'Cocody (Angré, 2 Plateaux, Riviera 2 & 3)';
      case DeliveryLocation.rivieraPalmeraie:
        return 'Riviera Palmeraie';
      case DeliveryLocation.rivieraBonoumin:
        return 'Riviera Bonoumin';
      case DeliveryLocation.adjame:
        return 'Adjamé';
      case DeliveryLocation.abobo:
        return 'Abobo (commune centrale)';
      case DeliveryLocation.yopougon:
        return 'Yopougon (<PERSON><PERSON>, Toits Rouges, Niangon Sud)';
      case DeliveryLocation.treichville:
        return 'Treichville';
      case DeliveryLocation.marcory:
        return 'Marcory';
      case DeliveryLocation.koumassi:
        return 'Koumassi';
      case DeliveryLocation.plateau:
        return 'Plateau (Centre-ville)';
      case DeliveryLocation.anyama:
        return 'Anyama';
      case DeliveryLocation.songon:
        return 'Songon';
      case DeliveryLocation.grandBassam:
        return 'Grand-Bassam';
    }
  }
}

class Invoice {
  final String id;
  final String number;
  final String clientName;
  final String clientPhone;
  final List<InvoiceItem> items;
  final InvoiceStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DeliveryLocation deliveryLocation;
  final String? deliveryDetails;
  final double advancePayment;
  final String? paymentType;

  Invoice({
    required this.id,
    required this.number,
    required this.clientName,
    required this.clientPhone,
    required this.items,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.deliveryLocation,
    this.deliveryDetails,
    required this.advancePayment,
    this.paymentType,
  });

  // Obtenir le montant de la livraison en fonction de la localisation
  double get deliveryFee {
    switch (deliveryLocation) {
      // Tarif 1000 FCFA
      case DeliveryLocation.bingerville:
      case DeliveryLocation.cocody:
      case DeliveryLocation.rivieraPalmeraie:
      case DeliveryLocation.rivieraBonoumin:
        return 1000.0;

      // Tarif 1500 FCFA
      case DeliveryLocation.adjame:
      case DeliveryLocation.abobo:
      case DeliveryLocation.yopougon:
      case DeliveryLocation.treichville:
      case DeliveryLocation.marcory:
      case DeliveryLocation.koumassi:
      case DeliveryLocation.plateau:
        return 1500.0;

      // Tarif 2500 FCFA
      case DeliveryLocation.anyama:
      case DeliveryLocation.songon:
      case DeliveryLocation.grandBassam:
        return 2500.0;
    }
  }

  // Obtenir le nom de la localisation
  String get deliveryLocationName {
    // Utiliser l'extension displayName pour obtenir le nom
    return deliveryLocation.displayName;
  }

  // Obtenir le statut de la facture sous forme de texte
  String get statusText {
    switch (status) {
      case InvoiceStatus.paid:
        return 'Payée';
      case InvoiceStatus.pending:
        return 'En attente';
      case InvoiceStatus.cancelled:
        return 'Annulée';
    }
  }

  // Calculer le sous-total (somme des prix des articles)
  double get subtotal {
    return items.fold(0, (sum, item) => sum + item.total);
  }

  // Calculer le total (sous-total + frais de livraison - avance)
  double get total {
    return subtotal + deliveryFee - advancePayment;
  }

  Invoice copyWith({
    String? id,
    String? number,
    String? clientName,
    String? clientPhone,
    List<InvoiceItem>? items,
    DeliveryLocation? deliveryLocation,
    String? deliveryDetails,
    double? advancePayment,
    InvoiceStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? paymentType,
  }) {
    return Invoice(
      id: id ?? this.id,
      number: number ?? this.number,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      items: items ?? this.items,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      deliveryDetails: deliveryDetails ?? this.deliveryDetails,
      advancePayment: advancePayment ?? this.advancePayment,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      paymentType: paymentType ?? this.paymentType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'number': number,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'items': items.map((x) => x.toMap()).toList(),
      'deliveryLocation': deliveryLocation.index,
      'deliveryDetails': deliveryDetails,
      'advancePayment': advancePayment,
      'status': status.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'paymentType': paymentType,
    };
  }

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'] ?? '',
      number: map['number'] ?? '',
      clientName: map['clientName'] ?? '',
      clientPhone: map['clientPhone'] ?? '',
      items: List<InvoiceItem>.from(
        map['items']?.map((x) => InvoiceItem.fromMap(x)) ?? [],
      ),
      deliveryLocation: DeliveryLocation.values[map['deliveryLocation'] ?? 0],
      deliveryDetails: map['deliveryDetails'],
      advancePayment: map['advancePayment']?.toDouble() ?? 0.0,
      status: InvoiceStatus.values[map['status'] ?? 0],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
      paymentType: map['paymentType'],
    );
  }

  String toJson() => json.encode(toMap());

  factory Invoice.fromJson(String source) =>
      Invoice.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Invoice(id: $id, number: $number, clientName: $clientName, clientPhone: $clientPhone, items: $items, deliveryLocation: $deliveryLocation, deliveryDetails: $deliveryDetails, advancePayment: $advancePayment, status: $status, createdAt: $createdAt, updatedAt: $updatedAt, paymentType: $paymentType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Invoice &&
        other.id == id &&
        other.number == number &&
        other.clientName == clientName &&
        other.clientPhone == clientPhone &&
        other.items.length == items.length &&
        other.items.every((item) => items.contains(item)) &&
        other.deliveryLocation == deliveryLocation &&
        other.deliveryDetails == deliveryDetails &&
        other.advancePayment == advancePayment &&
        other.status == status &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.paymentType == paymentType;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        number.hashCode ^
        clientName.hashCode ^
        clientPhone.hashCode ^
        items.hashCode ^
        deliveryLocation.hashCode ^
        deliveryDetails.hashCode ^
        advancePayment.hashCode ^
        status.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        paymentType.hashCode;
  }
}

