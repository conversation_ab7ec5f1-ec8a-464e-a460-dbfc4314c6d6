import 'dart:convert';
import 'package:flutter/foundation.dart';

enum TaskPriority { low, medium, high }

enum TaskStatus { todo, inProgress, completed, cancelled }

class Task {
  static Task empty() {
    return Task(
      id: '',
      title: '',
      description: '',
      createdAt: DateTime.now(),
      updatedAt: null,
      dueDate: DateTime.now().add(const Duration(days: 1)),
      priority: TaskPriority.medium,
      status: TaskStatus.todo,
      tags: [],
      hasReminder: false,
      isRecurring: false,
    );
  }

  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime dueDate;
  final TaskPriority priority;
  final TaskStatus status;
  final List<String> tags;
  final String? assignedToId;
  final String? assignedToName;
  final List<TaskComment>? comments;
  final List<TaskAttachment>? attachments;
  final bool hasReminder;
  final DateTime? reminderTime;
  final bool isRecurring;
  final String? recurrencePattern;
  final String? parentTaskId;
  final List<String>? subtaskIds;

  Task({
    required this.id,
    required this.title,
    required this.description,
    required this.createdAt,
    this.updatedAt,
    required this.dueDate,
    required this.priority,
    required this.status,
    required this.tags,
    this.assignedToId,
    this.assignedToName,
    this.comments,
    this.attachments,
    required this.hasReminder,
    this.reminderTime,
    required this.isRecurring,
    this.recurrencePattern,
    this.parentTaskId,
    this.subtaskIds,
  });

  // Vérifier si la tâche est en retard
  bool get isOverdue {
    return status != TaskStatus.completed &&
        status != TaskStatus.cancelled &&
        dueDate.isBefore(DateTime.now());
  }

  // Calculer le temps restant en jours
  int get daysRemaining {
    final now = DateTime.now();
    return dueDate.difference(now).inDays;
  }

  // Calculer le temps restant en heures
  int get hoursRemaining {
    final now = DateTime.now();
    return dueDate.difference(now).inHours;
  }

  // Calculer le nombre de jours de retard
  int get daysOverdue {
    if (!isOverdue) return 0;
    final now = DateTime.now();
    return now.difference(dueDate).inDays + 1;
  }

  // Vérifier si la tâche est due aujourd'hui
  bool get isDueToday {
    final now = DateTime.now();
    return dueDate.year == now.year &&
        dueDate.month == now.month &&
        dueDate.day == now.day;
  }

  // Vérifier si la tâche est due cette semaine
  bool get isDueThisWeek {
    final now = DateTime.now();
    final endOfWeek = now.add(Duration(days: 7 - now.weekday));
    return dueDate.isAfter(now) &&
        dueDate.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  Task copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? dueDate,
    TaskPriority? priority,
    TaskStatus? status,
    List<String>? tags,
    String? assignedToId,
    String? assignedToName,
    List<TaskComment>? comments,
    List<TaskAttachment>? attachments,
    bool? hasReminder,
    DateTime? reminderTime,
    bool? isRecurring,
    String? recurrencePattern,
    String? parentTaskId,
    List<String>? subtaskIds,
  }) {
    return Task(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      dueDate: dueDate ?? this.dueDate,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      tags: tags ?? this.tags,
      assignedToId: assignedToId ?? this.assignedToId,
      assignedToName: assignedToName ?? this.assignedToName,
      comments: comments ?? this.comments,
      attachments: attachments ?? this.attachments,
      hasReminder: hasReminder ?? this.hasReminder,
      reminderTime: reminderTime ?? this.reminderTime,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      subtaskIds: subtaskIds ?? this.subtaskIds,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
      'dueDate': dueDate.millisecondsSinceEpoch,
      'priority': priority.index,
      'status': status.index,
      'tags': tags,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'comments': comments?.map((x) => x.toMap()).toList(),
      'attachments': attachments?.map((x) => x.toMap()).toList(),
      'hasReminder': hasReminder,
      'reminderTime': reminderTime?.millisecondsSinceEpoch,
      'isRecurring': isRecurring,
      'recurrencePattern': recurrencePattern,
      'parentTaskId': parentTaskId,
      'subtaskIds': subtaskIds,
    };
  }

  factory Task.fromMap(Map<String, dynamic> map) {
    return Task(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt:
          map['updatedAt'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt'])
              : null,
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      priority: TaskPriority.values[map['priority']],
      status: TaskStatus.values[map['status']],
      tags: List<String>.from(map['tags'] ?? []),
      assignedToId: map['assignedToId'],
      assignedToName: map['assignedToName'],
      comments:
          map['comments'] != null
              ? List<TaskComment>.from(
                map['comments']?.map((x) => TaskComment.fromMap(x)),
              )
              : null,
      attachments:
          map['attachments'] != null
              ? List<TaskAttachment>.from(
                map['attachments']?.map((x) => TaskAttachment.fromMap(x)),
              )
              : null,
      hasReminder: map['hasReminder'] ?? false,
      reminderTime:
          map['reminderTime'] != null
              ? DateTime.fromMillisecondsSinceEpoch(map['reminderTime'])
              : null,
      isRecurring: map['isRecurring'] ?? false,
      recurrencePattern: map['recurrencePattern'],
      parentTaskId: map['parentTaskId'],
      subtaskIds:
          map['subtaskIds'] != null
              ? List<String>.from(map['subtaskIds'])
              : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory Task.fromJson(String source) => Task.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Task(id: $id, title: $title, dueDate: $dueDate, priority: $priority, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Task &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.dueDate == dueDate &&
        other.priority == priority &&
        other.status == status &&
        listEquals(other.tags, tags) &&
        other.assignedToId == assignedToId &&
        other.assignedToName == assignedToName &&
        other.hasReminder == hasReminder &&
        other.reminderTime == reminderTime &&
        other.isRecurring == isRecurring &&
        other.recurrencePattern == recurrencePattern &&
        other.parentTaskId == parentTaskId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        description.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        dueDate.hashCode ^
        priority.hashCode ^
        status.hashCode ^
        tags.hashCode ^
        assignedToId.hashCode ^
        assignedToName.hashCode ^
        hasReminder.hashCode ^
        reminderTime.hashCode ^
        isRecurring.hashCode ^
        recurrencePattern.hashCode ^
        parentTaskId.hashCode;
  }
}

class TaskComment {
  final String id;
  final String content;
  final DateTime createdAt;
  final String authorId;
  final String authorName;

  TaskComment({
    required this.id,
    required this.content,
    required this.createdAt,
    required this.authorId,
    required this.authorName,
  });

  TaskComment copyWith({
    String? id,
    String? content,
    DateTime? createdAt,
    String? authorId,
    String? authorName,
  }) {
    return TaskComment(
      id: id ?? this.id,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'content': content,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'authorId': authorId,
      'authorName': authorName,
    };
  }

  factory TaskComment.fromMap(Map<String, dynamic> map) {
    return TaskComment(
      id: map['id'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory TaskComment.fromJson(String source) =>
      TaskComment.fromMap(json.decode(source));
}

class TaskAttachment {
  final String id;
  final String name;
  final String url;
  final String type;
  final int size;
  final DateTime uploadedAt;

  TaskAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    required this.uploadedAt,
  });

  TaskAttachment copyWith({
    String? id,
    String? name,
    String? url,
    String? type,
    int? size,
    DateTime? uploadedAt,
  }) {
    return TaskAttachment(
      id: id ?? this.id,
      name: name ?? this.name,
      url: url ?? this.url,
      type: type ?? this.type,
      size: size ?? this.size,
      uploadedAt: uploadedAt ?? this.uploadedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'size': size,
      'uploadedAt': uploadedAt.millisecondsSinceEpoch,
    };
  }

  factory TaskAttachment.fromMap(Map<String, dynamic> map) {
    return TaskAttachment(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      type: map['type'] ?? '',
      size: map['size']?.toInt() ?? 0,
      uploadedAt: DateTime.fromMillisecondsSinceEpoch(map['uploadedAt']),
    );
  }

  String toJson() => json.encode(toMap());

  factory TaskAttachment.fromJson(String source) =>
      TaskAttachment.fromMap(json.decode(source));
}
