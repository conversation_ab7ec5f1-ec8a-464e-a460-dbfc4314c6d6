import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import '../../models/customer/customer.dart';
import '../../services/customer/customer_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class AddCustomerScreen extends ConsumerStatefulWidget {
  const AddCustomerScreen({super.key});

  @override
  ConsumerState<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends ConsumerState<AddCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _sourceController = TextEditingController();
  final _notesController = TextEditingController();
  final _tagsController = TextEditingController();

  CustomerStatus _status = CustomerStatus.lead;
  LeadQualification _leadQualification = LeadQualification.cold;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _sourceController.dispose();
    _notesController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ajouter un client'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Nom
                NeonTextField(
                  controller: _nameController,
                  labelText: 'Nom',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un nom';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email
                NeonTextField(
                  controller: _emailController,
                  labelText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),

                // Téléphone
                NeonTextField(
                  controller: _phoneController,
                  labelText: 'Téléphone',
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),

                // Source
                NeonTextField(
                  controller: _sourceController,
                  labelText: 'Source (WhatsApp, Facebook, etc.)',
                ),
                const SizedBox(height: 16),

                // Statut
                const Text(
                  'Statut',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<CustomerStatus>(
                  value: _status,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: NeonTheme.neonPurple,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.black.withAlpha(153), // 0.6 * 255 ≈ 153
                  ),
                  dropdownColor: Colors.black,
                  items:
                      CustomerStatus.values.map((status) {
                        return DropdownMenuItem<CustomerStatus>(
                          value: status,
                          child: Text(
                            _getStatusLabel(status),
                            style: const TextStyle(color: Colors.white),
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _status = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Qualification (si c'est un lead)
                if (_status == CustomerStatus.lead) ...[
                  const Text(
                    'Qualification',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<LeadQualification>(
                    value: _leadQualification,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.white),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.white),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: NeonTheme.neonPurple,
                          width: 2,
                        ),
                      ),
                      filled: true,
                      fillColor: Colors.black.withAlpha(153), // 0.6 * 255 ≈ 153
                    ),
                    dropdownColor: Colors.black,
                    items:
                        LeadQualification.values.map((qualification) {
                          return DropdownMenuItem<LeadQualification>(
                            value: qualification,
                            child: Text(
                              _getQualificationLabel(qualification),
                              style: const TextStyle(color: Colors.white),
                            ),
                          );
                        }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _leadQualification = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                ],

                // Notes
                NeonTextField(
                  controller: _notesController,
                  labelText: 'Notes',
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // Tags
                NeonTextField(
                  controller: _tagsController,
                  labelText: 'Tags (séparés par des virgules)',
                ),
                const SizedBox(height: 24),

                // Bouton d'importation vCard
                Center(
                  child: NeonButton(
                    text: 'Importer vCard',
                    icon: FontAwesomeIcons.addressCard,
                    color: NeonTheme.neonTurquoise,
                    onPressed: _importVCard,
                  ),
                ),
                const SizedBox(height: 16),

                // Bouton de soumission
                Center(
                  child: NeonButton(
                    text: 'Ajouter le client',
                    icon: FontAwesomeIcons.userPlus,
                    color: NeonTheme.neonGreen,
                    isLoading: _isLoading,
                    onPressed: _submitForm,
                  ),
                ),

                if (_errorMessage != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(77), // 0.3 * 255 ≈ 77
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red),
                    ),
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getStatusLabel(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.lead:
        return 'Prospect';
      case CustomerStatus.customer:
        return 'Client';
      case CustomerStatus.vip:
        return 'Client VIP';
      case CustomerStatus.inactive:
        return 'Client inactif';
      case CustomerStatus.blocked:
        return 'Client bloqué';
    }
  }

  String _getQualificationLabel(LeadQualification qualification) {
    switch (qualification) {
      case LeadQualification.cold:
        return 'Froid';
      case LeadQualification.warm:
        return 'Tiède';
      case LeadQualification.hot:
        return 'Chaud';
      case LeadQualification.qualified:
        return 'Qualifié';
      case LeadQualification.unqualified:
        return 'Non qualifié';
    }
  }

  Future<void> _importVCard() async {
    try {
      // Vérifier les permissions
      if (!await FlutterContacts.requestPermission()) {
        setState(() {
          _errorMessage = 'Permission refusée pour accéder aux contacts';
        });
        return;
      }

      // Deux approches possibles :
      // 1. Sélectionner un fichier VCF
      // 2. Sélectionner un contact existant

      // Approche 1 : Sélectionner un fichier VCF
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['vcf'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final vcfString = await file.readAsString();

        // Importer le contact depuis le fichier VCF
        final contact = Contact.fromVCard(vcfString);

        setState(() {
          // Nom
          if (contact.name.first.isNotEmpty || contact.name.last.isNotEmpty) {
            _nameController.text =
                '${contact.name.first} ${contact.name.last}'.trim();
          }

          // Email
          if (contact.emails.isNotEmpty) {
            _emailController.text = contact.emails.first.address;
          }

          // Téléphone
          if (contact.phones.isNotEmpty) {
            _phoneController.text = contact.phones.first.number;
          }
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors de l\'importation du vCard: $e';
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Préparer les tags
      final tags =
          _tagsController.text.isEmpty
              ? <String>[]
              : _tagsController.text
                  .split(',')
                  .map((tag) => tag.trim())
                  .toList();

      // Créer le client
      final customer = Customer(
        id: const Uuid().v4(),
        name: _nameController.text,
        email: _emailController.text.isEmpty ? null : _emailController.text,
        phone: _phoneController.text.isEmpty ? null : _phoneController.text,
        source: _sourceController.text.isEmpty ? null : _sourceController.text,
        createdAt: DateTime.now(),
        lastContact: DateTime.now(),
        status: _status,
        leadQualification:
            _status == CustomerStatus.lead ? _leadQualification : null,
        notes: _notesController.text.isEmpty ? null : _notesController.text,
        tags: tags,
      );

      // Ajouter le client
      final customerService = ref.read(customerServiceProvider);
      await customerService.addCustomer(customer);

      // Rafraîchir la liste des clients
      final _ = ref.refresh(customersProvider);

      if (mounted) {
        context.pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur: $e';
        _isLoading = false;
      });
    }
  }
}
