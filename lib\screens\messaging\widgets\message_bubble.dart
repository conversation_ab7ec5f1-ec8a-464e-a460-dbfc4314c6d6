import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../../../models/messaging/message.dart';
import '../../../theme/neon_theme.dart';

class MessageBubble extends ConsumerWidget {
  final Message message;
  final bool showSender;
  final bool showTimestamp;

  const MessageBubble({
    super.key,
    required this.message,
    this.showSender = true,
    this.showTimestamp = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isUser = message.isUser;
    final isAI = message.isAIGenerated;

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(
          top: 4,
          bottom: 4,
          left: isUser ? 64 : 0,
          right: isUser ? 0 : 64,
        ),
        child: Column(
          crossAxisAlignment:
              isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            // Afficher l'expéditeur si nécessaire
            if (showSender && !isUser)
              Container(
                margin: const EdgeInsets.only(left: 12, bottom: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isAI)
                      const Icon(
                        FontAwesomeIcons.robot,
                        color: NeonTheme.secondaryAccent,
                        size: 12,
                      ),
                    const SizedBox(width: 4),
                    Text(
                      isAI ? 'Assistant IA' : 'Contact',
                      style: TextStyle(
                        color: isAI ? NeonTheme.secondaryAccent : Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

            // Bulle de message
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              margin: const EdgeInsets.only(
                top: 2,
              ), // Ajouter une marge pour éviter le chevauchement
              constraints: const BoxConstraints(
                maxWidth: 280,
              ), // Limiter la largeur pour une meilleure lisibilité
              decoration: BoxDecoration(
                color: _getBubbleColor(isUser, isAI),
                borderRadius: BorderRadius.circular(16).copyWith(
                  bottomLeft:
                      isUser
                          ? const Radius.circular(16)
                          : const Radius.circular(4),
                  bottomRight:
                      isUser
                          ? const Radius.circular(4)
                          : const Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Contenu du message
                  _buildMessageContent(),

                  // Afficher l'horodatage et le statut si nécessaire
                  if (showTimestamp)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            DateFormat('HH:mm').format(message.timestamp),
                            style: TextStyle(
                              color:
                                  isUser
                                      ? Colors.white.withValues(
                                        alpha: 179,
                                      ) // 0.7 * 255 ≈ 179
                                      : Colors.black.withValues(
                                        alpha: 128,
                                      ), // 0.5 * 255 ≈ 128
                              fontSize: 10,
                            ),
                          ),
                          if (isUser)
                            Padding(
                              padding: const EdgeInsets.only(left: 4),
                              child: _buildStatusIcon(),
                            ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageContent() {
    // Si le message contient un média
    if (message.mediaUrl != null && message.mediaType != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMediaContent(),
          if (message.content.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Text(
                message.content,
                style: TextStyle(
                  color: message.isUser ? Colors.white : Colors.black,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      );
    }

    // Sinon, afficher simplement le texte
    return Text(
      message.content,
      style: TextStyle(
        color: message.isUser ? Colors.white : Colors.black,
        fontSize: 16,
      ),
    );
  }

  Widget _buildMediaContent() {
    switch (message.mediaType) {
      case MessageType.image:
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            message.mediaUrl!,
            width: 200,
            height: 200,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: 200,
                height: 200,
                color: Colors.grey.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                child: const Center(child: CircularProgressIndicator()),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                width: 200,
                height: 200,
                color: Colors.grey.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                child: const Center(
                  child: Icon(Icons.error, color: Colors.white),
                ),
              );
            },
          ),
        );

      case MessageType.video:
        return Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 128), // 0.5 * 255 ≈ 128
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(Icons.play_circle_fill, color: Colors.white, size: 48),
          ),
        );

      case MessageType.audio:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.play_arrow, color: Colors.white),
              const SizedBox(width: 8),
              Container(
                width: 100,
                height: 2,
                color: Colors.white.withValues(alpha: 128), // 0.5 * 255 ≈ 128
              ),
              const SizedBox(width: 8),
              Text(
                '0:00',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );

      case MessageType.document:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.insert_drive_file, color: Colors.white),
              const SizedBox(width: 8),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.mediaName ?? 'Document',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (message.mediaSize != null)
                      Text(
                        _formatFileSize(message.mediaSize!),
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 179,
                          ), // 0.7 * 255 ≈ 179
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );

      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildStatusIcon() {
    switch (message.status) {
      case MessageStatus.sending:
        return Icon(
          Icons.access_time,
          color: Colors.white.withValues(alpha: 179),
          size: 12,
        );
      case MessageStatus.sent:
        return Icon(
          Icons.check,
          color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
          size: 12,
        );
      case MessageStatus.delivered:
        return Icon(
          Icons.done_all,
          color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
          size: 12,
        );
      case MessageStatus.read:
        return const Icon(Icons.done_all, color: Colors.blue, size: 12);
      case MessageStatus.failed:
        return const Icon(Icons.error, color: Colors.red, size: 12);
    }
  }

  Color _getBubbleColor(bool isUser, bool isAI) {
    if (isUser) {
      return NeonTheme.secondaryAccent;
    } else if (isAI) {
      return NeonTheme.primaryAccent.withAlpha(230); // 0.9 * 255 ≈ 230
    } else {
      return Colors.white;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
