{"collections": [{"name": "users", "type": "auth", "system": false, "schema": [{"name": "name", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "protected": false}}, {"name": "role", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["admin", "manager", "user"]}}], "listRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "viewRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "createRule": "@request.auth.role = \"admin\"", "updateRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "deleteRule": "@request.auth.role = \"admin\"", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": "@request.auth.role = \"admin\"", "minPasswordLength": 8, "onlyEmailDomains": null, "requireEmail": true}}, {"name": "contacts", "type": "base", "system": false, "schema": [{"name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "phone", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "email", "type": "email", "system": false, "required": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"name": "company", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": ["100x100"], "protected": false}}, {"name": "notes", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "owner", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "users", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"name": "messages", "type": "base", "system": false, "schema": [{"name": "contact_id", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "contacts", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"name": "content", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "is_user", "type": "bool", "system": false, "required": true, "unique": false, "options": {}}, {"name": "timestamp", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"name": "media_url", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "media_type", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["image", "video", "audio", "document"]}}, {"name": "channel", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["app", "whatsapp", "facebook", "sms", "email"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"name": "tasks", "type": "base", "system": false, "schema": [{"name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "contact_id", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "contacts", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"name": "due_date", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["pending", "in_progress", "completed"]}}, {"name": "priority", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["low", "medium", "high"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"name": "knowledge_base", "type": "base", "system": false, "schema": [{"name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "content", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "category", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"name": "tags", "type": "json", "system": false, "required": false, "unique": false, "options": {}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}]}