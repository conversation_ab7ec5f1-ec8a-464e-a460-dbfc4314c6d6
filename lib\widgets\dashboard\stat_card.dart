import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/theme_provider.dart';

class StatCard extends ConsumerWidget {
  final String title;
  final String value;
  final String change;
  final IconData icon;
  final Color? iconColor;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.change,
    required this.icon,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    final isPositiveChange = change.contains('+');
    final changeColor = isPositiveChange ? Colors.green : Colors.red;
    final color = iconColor ?? primaryColor;

    return Container(
      decoration: BoxDecoration(
        gradient: appTheme.mainGradient,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: secondaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color:
                        ref.watch(isDarkThemeProvider)
                            ? Colors.white
                            : ref.watch(primaryTextColorProvider),
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1 * 255),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color:
                    ref.watch(isDarkThemeProvider)
                        ? Colors.white
                        : ref.watch(primaryTextColorProvider),
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  isPositiveChange ? Icons.arrow_upward : Icons.arrow_downward,
                  color:
                      change.contains('priorité') ? Colors.orange : changeColor,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  change,
                  style: TextStyle(
                    color:
                        change.contains('priorité')
                            ? Colors.orange
                            : changeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
