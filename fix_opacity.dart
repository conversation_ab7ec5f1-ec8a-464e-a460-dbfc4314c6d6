import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  // Répertoire racine du projet
  final Directory rootDir = Directory('lib');

  // Parcourir tous les fichiers .dart
  await for (final FileSystemEntity entity in rootDir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      await processFile(entity);
    }
  }

  if (kDebugMode) {
    debugPrint('Terminé !');
  }
}

Future<void> processFile(File file) async {
  try {
    String content = await file.readAsString();

    // Remplacer withOpacity par withValues
    bool hasChanges = false;

    // Regex pour trouver les withOpacity avec une valeur numérique
    final regex = RegExp(r'withOpacity\(([0-9.]+)\)');

    content = content.replaceAllMapped(regex, (match) {
      hasChanges = true;
      final opacity = double.parse(match.group(1)!);
      final alpha = (opacity * 255).round();
      return 'withValues(alpha: $alpha) // ${match.group(0)}';
    });

    if (hasChanges) {
      await file.writeAsString(content);
      debugPrint('Opacity fixed successfully');
    }
  } catch (e) {
    debugPrint('Error fixing opacity: $e');
  }
}
