import 'dart:io';
// import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:path/path.dart' as path;
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class ProductFormScreen extends ConsumerStatefulWidget {
  final String? productId;

  const ProductFormScreen({super.key, this.productId});

  @override
  ConsumerState<ProductFormScreen> createState() => _ProductFormScreenState();
}

class _ProductFormScreenState extends ConsumerState<ProductFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  final _quantityController = TextEditingController();
  final _descriptionController = TextEditingController();

  String? _selectedCategoryId;
  bool _isLoading = false;
  bool _isEdit = false;
  Product? _originalProduct;

  // Variables pour l'image
  File? _imageFile;
  Uint8List? _imageBytes;
  final _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _isEdit = widget.productId != null;

    if (_isEdit) {
      _loadProduct();
    }
  }

  Future<void> _loadProduct() async {
    setState(() {
      _isLoading = true;
    });

    final inventoryService = ref.read(inventoryServiceProvider);
    final products = await inventoryService.getProducts();
    Product? product;
    try {
      product = products.firstWhere((p) => p.id == widget.productId);
    } catch (e) {
      product = null;
    }

    if (product != null) {
      _originalProduct = product;
      _nameController.text = product.name;
      _priceController.text = product.price.toString();
      _quantityController.text = product.quantity.toString();
      _descriptionController.text = product.description ?? '';
      _selectedCategoryId = product.categoryId;

      // Charger l'image existante si elle existe
      if (product.imageUrl != null && product.imageUrl!.isNotEmpty) {
        _loadExistingImage(product.imageUrl!);
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _quantityController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // Méthode pour charger une image existante
  Future<void> _loadExistingImage(String imageUrl) async {
    try {
      if (imageUrl.startsWith('data:image')) {
        // Image en base64
        final base64String = imageUrl.split(',')[1];
        final bytes = base64Decode(base64String);
        setState(() {
          _imageBytes = bytes;
          _imageFile = null;
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement de l\'image existante: $e');
    }
  }

  // Méthode pour sélectionner une image depuis la galerie
  Future<void> _pickImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        if (kIsWeb) {
          // Sur le web, on récupère les bytes de l'image
          final bytes = await pickedFile.readAsBytes();
          setState(() {
            _imageBytes = bytes;
            _imageFile = null;
          });
        } else {
          // Sur les autres plateformes, on utilise File
          setState(() {
            _imageFile = File(pickedFile.path);
            _imageBytes = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection de l\'image: $e'),
          ),
        );
      }
    }
  }

  // Méthode pour sauvegarder l'image localement
  Future<String?> _saveImage() async {
    if (_imageFile == null && _imageBytes == null) return null;

    try {
      // Toujours convertir en base64, quelle que soit la plateforme
      Uint8List bytes;
      if (_imageBytes != null) {
        bytes = _imageBytes!;
      } else {
        bytes = await _imageFile!.readAsBytes();
      }
      
      final base64String = base64Encode(bytes);
      return 'data:image/jpeg;base64,$base64String';
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sauvegarde de l\'image: $e')),
        );
      }
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEdit ? 'Modifier le Produit' : 'Ajouter un Produit'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : categoriesAsync.when(
                  data: (categories) {
                    if (categories.isEmpty) {
                      return const Center(
                        child: Text(
                          'Veuillez d\'abord créer une catégorie',
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      );
                    }

                    return _buildForm(context, categories);
                  },
                  loading:
                      () => const Center(child: CircularProgressIndicator()),
                  error:
                      (error, stack) => Center(
                        child: Text(
                          'Erreur: $error',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                ),
      ),
    );
  }

  Widget _buildForm(BuildContext context, List<ProductCategory> categories) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: NeonCard(
          color: NeonTheme.neonGreen,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Informations du Produit',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              NeonTextField(
                controller: _nameController,
                labelText: 'Nom du Produit',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer un nom';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategoryId,
                decoration: InputDecoration(
                  labelText: 'Catégorie',
                  labelStyle: const TextStyle(color: Colors.white),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: NeonTheme.neonGreen),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: NeonTheme.neonGreen),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: NeonTheme.neonGreen,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.black.withValues(
                    alpha: 153,
                  ), // 0.6 * 255 ≈ 153
                ),
                dropdownColor: Colors.black,
                style: const TextStyle(color: Colors.white),
                items:
                    categories.map((category) {
                      return DropdownMenuItem<String>(
                        value: category.id,
                        child: Text(category.name),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategoryId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez sélectionner une catégorie';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: NeonTextField(
                      controller: _priceController,
                      labelText: 'Prix (FCFA)',
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer un prix';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Veuillez entrer un nombre valide';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: NeonTextField(
                      controller: _quantityController,
                      labelText: 'Quantité',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer une quantité';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Veuillez entrer un nombre entier';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              NeonTextField(
                controller: _descriptionController,
                labelText: 'Description',
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  NeonButton(
                    text: 'Choisir image',
                    color: NeonTheme.neonCyan,
                    onPressed: _pickImage,
                  ),
                ],
              ),
              if (_imageFile != null ||
                  _imageBytes != null ||
                  (_originalProduct?.imageUrl != null &&
                      _originalProduct!.imageUrl!.isNotEmpty))
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child:
                        _imageBytes != null
                            ? Image.memory(
                              _imageBytes!,
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.image_not_supported,
                                  size: 100,
                                );
                              },
                            )
                            : _imageFile != null
                            ? Image.file(
                              _imageFile!,
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                            )
                            : _originalProduct?.imageUrl != null &&
                                _originalProduct!.imageUrl!.startsWith(
                                  'data:image',
                                )
                            ? Image.memory(
                              base64Decode(
                                _originalProduct!.imageUrl!.split(',')[1],
                              ),
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.image_not_supported,
                                  size: 100,
                                );
                              },
                            )
                            : const Icon(Icons.image_not_supported, size: 100),
                  ),
                ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  NeonButton(
                    text: 'Annuler',
                    color: Colors.grey,
                    onPressed: () {
                      context.pop();
                    },
                  ),
                  const SizedBox(width: 16),
                  NeonButton(
                    text: _isEdit ? 'Mettre à jour' : 'Ajouter',
                    color: NeonTheme.neonGreen,
                    onPressed: _saveProduct,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final inventoryService = ref.read(inventoryServiceProvider);
      final categories = await inventoryService.getCategories();
      ProductCategory? selectedCategory;
      try {
        selectedCategory = categories.firstWhere(
          (c) => c.id == _selectedCategoryId,
        );
      } catch (e) {
        selectedCategory = null;
      }

      if (selectedCategory == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Sauvegarder l'image si une image locale a été sélectionnée
      String? imageUrl;
      if (_imageFile != null || _imageBytes != null) {
        imageUrl = await _saveImage();
      } else if (_isEdit && _originalProduct?.imageUrl != null) {
        // Conserver l'image existante si aucune nouvelle image n'a été sélectionnée
        imageUrl = _originalProduct!.imageUrl;
      }

      final product = Product(
        id: _isEdit ? _originalProduct!.id : const Uuid().v4(),
        name: _nameController.text,
        categoryId: _selectedCategoryId!,
        categoryName: selectedCategory.name,
        price: double.parse(_priceController.text),
        quantity: int.parse(_quantityController.text),
        description:
            _descriptionController.text.isNotEmpty
                ? _descriptionController.text
                : null,
        imageUrl: imageUrl,
        createdAt: _isEdit ? _originalProduct!.createdAt : DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEdit) {
        await inventoryService.updateProduct(product);
      } else {
        await inventoryService.addProduct(product);
      }

      // Rafraîchir les données
      // ignore: unused_result
      ref.refresh(productsProvider);

      if (mounted) {
        context.pop();
      }
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde du produit: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}


