import 'dart:convert';

class User {
  final String id;
  final String name;
  final String email;
  final String role;
  final String? password; // Optionnel, utilisé uniquement pour l'inscription
  final String? avatar; // URL de l'avatar
  final String? phone; // Numéro de téléphone
  final String? bio; // Biographie
  final String? department; // Département

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.password,
    this.avatar,
    this.phone,
    this.bio,
    this.department,
  });

  // Créer un utilisateur à partir d'un Map
  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      role: map['role'] ?? 'user',
      password: map['password'],
      avatar: map['avatar'],
      phone: map['phone'],
      bio: map['bio'],
      department: map['department'],
    );
  }

  // Créer un utilisateur à partir d'un JSON
  factory User.fromJson(String source) => User.fromMap(json.decode(source));

  // Convertir l'utilisateur en Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      if (password != null) 'password': password,
      if (avatar != null) 'avatar': avatar,
      if (phone != null) 'phone': phone,
      if (bio != null) 'bio': bio,
      if (department != null) 'department': department,
    };
  }

  // Convertir l'utilisateur en JSON
  String toJson() => json.encode(toMap());

  // Créer une copie de l'utilisateur avec des modifications
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? password,
    String? avatar,
    String? phone,
    String? bio,
    String? department,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      password: password ?? this.password,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      bio: bio ?? this.bio,
      department: department ?? this.department,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role, department: $department)';
  }
}
