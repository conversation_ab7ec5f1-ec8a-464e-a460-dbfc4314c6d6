import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/messaging/message.dart';
import '../../models/customer/customer.dart';
import '../../models/invoice/invoice.dart';
import '../ai/knowledge_ai_service.dart';
import '../ai/auto_response_knowledge_service.dart';
import '../ai/quote_generator_service.dart';
import '../ai/support_service.dart';
import '../ai/customer_learning_service.dart';
import '../messaging/messaging_service.dart';

// Provider pour le service d'orchestration de l'IA
final aiOrchestratorServiceProvider = Provider<AIOrchestrator>((ref) {
  final knowledgeAIService = ref.watch(knowledgeAIServiceProvider);
  final autoResponseService = ref.watch(autoResponseKnowledgeServiceProvider);
  final quoteGeneratorService = ref.watch(quoteGeneratorServiceProvider);
  final supportService = ref.watch(supportServiceProvider);
  final customerLearningService = ref.watch(customerLearningServiceProvider);
  final messagingService = ref.watch(messagingServiceProvider);

  return AIOrchestrator(
    knowledgeAIService: knowledgeAIService,
    autoResponseService: autoResponseService,
    quoteGeneratorService: quoteGeneratorService,
    supportService: supportService,
    customerLearningService: customerLearningService,
    messagingService: messagingService,
  );
});

// Énumération des types de traitement
enum ProcessingType {
  autoResponse, // Réponse automatique
  quoteGeneration, // Génération de devis
  supportTicket, // Ticket de support
  customerLearning, // Apprentissage client
  humanHandoff, // Transfert à un humain
}

class AIOrchestrator {
  final KnowledgeAIService knowledgeAIService;
  final AutoResponseKnowledgeService autoResponseService;
  final QuoteGeneratorService quoteGeneratorService;
  final SupportService supportService;
  final CustomerLearningService customerLearningService;
  final MessagingService messagingService;

  // Paramètres de configuration
  bool _autoResponseEnabled = true;
  bool _quoteGenerationEnabled = true;
  bool _supportTicketEnabled = true;
  bool _customerLearningEnabled = true;

  AIOrchestrator({
    required this.knowledgeAIService,
    required this.autoResponseService,
    required this.quoteGeneratorService,
    required this.supportService,
    required this.customerLearningService,
    required this.messagingService,
  });

  // Activer/désactiver les fonctionnalités
  void setAutoResponseEnabled(bool enabled) {
    _autoResponseEnabled = enabled;
    autoResponseService.setAutoResponseEnabled(enabled);
  }

  void setQuoteGenerationEnabled(bool enabled) {
    _quoteGenerationEnabled = enabled;
  }

  void setSupportTicketEnabled(bool enabled) {
    _supportTicketEnabled = enabled;
  }

  void setCustomerLearningEnabled(bool enabled) {
    _customerLearningEnabled = enabled;
  }

  // Traiter un nouveau message
  Future<Map<String, dynamic>> processNewMessage(Message message) async {
    try {
      // Récupérer la conversation
      final conversation = await messagingService.getConversationById(
        message.conversationId,
      );
      if (conversation == null) {
        throw Exception('Conversation non trouvée');
      }

      // Résultats du traitement
      final results = <String, dynamic>{
        'processed': false,
        'response_generated': false,
        'processing_type': null,
        'response': null,
        'quote': null,
        'support_ticket': null,
      };

      // 1. Vérifier si c'est une demande de devis
      if (_quoteGenerationEnabled) {
        final isQuoteRequest = await quoteGeneratorService.isQuoteRequest(
          message.content,
        );

        if (isQuoteRequest) {
          // Générer un devis
          final quote = await quoteGeneratorService.generateQuoteFromMessage(
            message.content,
            conversation,
          );

          if (quote != null) {
            // Envoyer le devis au client
            await quoteGeneratorService.sendQuoteToCustomer(
              quote,
              conversation,
            );

            results['processed'] = true;
            results['response_generated'] = true;
            results['processing_type'] = ProcessingType.quoteGeneration;
            results['quote'] = quote;

            return results;
          }
        }
      }

      // 2. Vérifier si c'est une demande de support
      if (_supportTicketEnabled) {
        final isSupportRequest = await supportService.isSupportRequest(
          message.content,
        );

        if (isSupportRequest) {
          // Créer un ticket de support
          final ticket = await supportService.createTicketFromMessage(
            message,
            conversation,
          );

          if (ticket != null) {
            // Générer une réponse de support
            final response = await supportService.generateSupportResponse(
              ticket,
              message,
            );

            // Envoyer la réponse au client
            await supportService.sendSupportResponse(
              ticket,
              conversation,
              response,
            );

            results['processed'] = true;
            results['response_generated'] = true;
            results['processing_type'] = ProcessingType.supportTicket;
            results['support_ticket'] = ticket;
            results['response'] = response;

            return results;
          }
        }
      }

      // 3. Réponse automatique basée sur la base de connaissances
      if (_autoResponseEnabled) {
        final response = await knowledgeAIService.processIncomingMessage(
          message,
          conversation,
        );

        if (response != null) {
          results['processed'] = true;
          results['response_generated'] = true;
          results['processing_type'] = ProcessingType.autoResponse;
          results['response'] = response;

          return results;
        }
      }

      // 4. Apprentissage client (en arrière-plan)
      if (_customerLearningEnabled) {
        // Récupérer le client
        final customer = await knowledgeAIService.customerService
            .getCustomerByContactId(message.sender);

        if (customer != null) {
          // Analyser le sentiment du message
          final sentiment = await customerLearningService.analyzeSentiment(
            message.content,
          );

          // Mettre à jour les préférences du client en arrière-plan
          customerLearningService.extractCustomerPreferences(customer.id).then((
            preferences,
          ) {
            debugPrint('Préférences client mises à jour: $preferences');
          });

          results['sentiment'] = sentiment;
        }
      }

      return results;
    } catch (e) {
      debugPrint('Erreur lors du traitement du message: $e');
      return {'processed': false, 'error': e.toString()};
    }
  }

  // Planifier des tâches périodiques
  Future<void> schedulePeriodicTasks() async {
    try {
      // 1. Planifier des relances pour les conversations inactives
      if (_autoResponseEnabled) {
        await autoResponseService.scheduleFollowUps();
      }

      // 2. Qualifier automatiquement les leads
      if (_customerLearningEnabled) {
        final customers =
            await knowledgeAIService.customerService.getCustomers();

        for (final customer in customers) {
          if (customer.status == CustomerStatus.lead) {
            await autoResponseService.autoQualifyLead(customer.id);
          }
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la planification des tâches périodiques: $e');
    }
  }

  // Générer des suggestions personnalisées pour un client
  Future<Map<String, dynamic>> generatePersonalizedSuggestions(
    String customerId,
  ) async {
    try {
      // Récupérer le client
      final customer = await knowledgeAIService.customerService.getCustomerById(
        customerId,
      );
      if (customer == null) {
        throw Exception('Client non trouvé');
      }

      // Générer des suggestions de produits
      final productSuggestions = await customerLearningService
          .generatePersonalizedSuggestions(customerId);

      // Générer un message personnalisé
      final message = await customerLearningService.generatePersonalizedMessage(
        customerId,
        'special_offer',
      );

      return {
        'customer': customer,
        'product_suggestions': productSuggestions,
        'personalized_message': message,
      };
    } catch (e) {
      debugPrint(
        'Erreur lors de la génération des suggestions personnalisées: $e',
      );
      return {'error': e.toString()};
    }
  }

  // Vérifier si un message est une confirmation de commande
  Future<bool> isOrderConfirmation(String message) async {
    return quoteGeneratorService.isOrderConfirmation(message);
  }

  // Convertir un devis en commande
  Future<Invoice?> convertQuoteToOrder(String quoteId) async {
    final result = await quoteGeneratorService.convertQuoteToOrder(quoteId);
    return result as Invoice?;
  }
}
