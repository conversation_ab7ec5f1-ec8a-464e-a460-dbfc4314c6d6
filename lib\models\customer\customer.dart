// Modèle de client

// Statut du client
enum CustomerStatus {
  lead, // Prospect
  customer, // Client
  vip, // Client VIP
  inactive, // Client inactif
  blocked, // Client bloqué
}

// Niveau de qualification du lead
enum LeadQualification {
  cold, // Froid
  warm, // Tiède
  hot, // Chaud
  qualified, // Qualifié
  unqualified, // Non qualifié
}

class Customer {
  final String id;
  final String name;
  final String? contactId; // ID de contact (WhatsApp, Facebook, etc.)
  final String? phone;
  final String? email;
  final String? address; // Adresse du client
  final String? company; // Société du client
  final String?
  source; // Source du contact (WhatsApp, Facebook, Site web, etc.)
  final DateTime createdAt;
  final DateTime lastContact;
  final CustomerStatus status;
  final LeadQualification? leadQualification;
  final String? assignedTo; // ID du commercial assigné
  final String? notes;
  final List<String> tags;
  final Map<String, dynamic>?
  preferences; // Préférences client (produits, couleurs, etc.)
  final Map<String, dynamic>? customFields; // Champs personnalisés

  Customer({
    required this.id,
    required this.name,
    this.contactId,
    this.phone,
    this.email,
    this.address,
    this.company,
    this.source,
    required this.createdAt,
    required this.lastContact,
    required this.status,
    this.leadQualification,
    this.assignedTo,
    this.notes,
    required this.tags,
    this.preferences,
    this.customFields,
  });

  // Créer une copie avec des modifications
  Customer copyWith({
    String? id,
    String? name,
    String? contactId,
    String? phone,
    String? email,
    String? address,
    String? company,
    String? source,
    DateTime? createdAt,
    DateTime? lastContact,
    CustomerStatus? status,
    LeadQualification? leadQualification,
    String? assignedTo,
    String? notes,
    List<String>? tags,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? customFields,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      contactId: contactId ?? this.contactId,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      company: company ?? this.company,
      source: source ?? this.source,
      createdAt: createdAt ?? this.createdAt,
      lastContact: lastContact ?? this.lastContact,
      status: status ?? this.status,
      leadQualification: leadQualification ?? this.leadQualification,
      assignedTo: assignedTo ?? this.assignedTo,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
      preferences: preferences ?? this.preferences,
      customFields: customFields ?? this.customFields,
    );
  }

  // Conversion depuis JSON
  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'] as String,
      name: json['name'] as String,
      contactId: json['contact_id'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      address: json['address'] as String?,
      company: json['company'] as String?,
      source: json['source'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastContact: DateTime.parse(json['last_contact'] as String),
      status: CustomerStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => CustomerStatus.lead,
      ),
      leadQualification:
          json['lead_qualification'] != null
              ? LeadQualification.values.firstWhere(
                (e) => e.name == json['lead_qualification'],
                orElse: () => LeadQualification.cold,
              )
              : null,
      assignedTo: json['assigned_to'] as String?,
      notes: json['notes'] as String?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      preferences: json['preferences'] as Map<String, dynamic>?,
      customFields: json['custom_fields'] as Map<String, dynamic>?,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'contact_id': contactId,
      'phone': phone,
      'email': email,
      'address': address,
      'company': company,
      'source': source,
      'created_at': createdAt.toIso8601String(),
      'last_contact': lastContact.toIso8601String(),
      'status': status.name,
      'lead_qualification': leadQualification?.name,
      'assigned_to': assignedTo,
      'notes': notes,
      'tags': tags,
      'preferences': preferences,
      'custom_fields': customFields,
    };
  }

  // Créer un client vide
  factory Customer.empty() {
    return Customer(
      id: '',
      name: '',
      createdAt: DateTime.now(),
      lastContact: DateTime.now(),
      status: CustomerStatus.lead,
      tags: [],
    );
  }

  @override
  String toString() {
    return 'Customer{id: $id, name: $name, status: ${status.name}}';
  }
}
