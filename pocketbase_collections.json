{"collections": [{"id": "_pb_users_auth_", "name": "users", "type": "auth", "system": false, "schema": [{"id": "users_name", "name": "name", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "users_avatar", "name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": null, "protected": false}}, {"id": "users_role", "name": "role", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["admin", "manager", "user"]}}], "indexes": [], "listRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "viewRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "createRule": "@request.auth.role = \"admin\"", "updateRule": "id = @request.auth.id || @request.auth.role = \"admin\"", "deleteRule": "@request.auth.role = \"admin\"", "options": {"allowEmailAuth": true, "allowOAuth2Auth": true, "allowUsernameAuth": true, "exceptEmailDomains": null, "manageRule": "@request.auth.role = \"admin\"", "minPasswordLength": 8, "onlyEmailDomains": null, "requireEmail": true}}, {"id": "contacts", "name": "contacts", "type": "base", "system": false, "schema": [{"id": "contacts_name", "name": "name", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_phone", "name": "phone", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_email", "name": "email", "type": "email", "system": false, "required": false, "unique": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "contacts_company", "name": "company", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_avatar", "name": "avatar", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/svg+xml", "image/gif", "image/webp"], "thumbs": ["100x100"], "protected": false}}, {"id": "contacts_notes", "name": "notes", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_address", "name": "address", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_city", "name": "city", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_country", "name": "country", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_postal_code", "name": "postal_code", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_tags", "name": "tags", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "contacts_owner", "name": "owner", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "indexes": ["CREATE INDEX idx_contacts_name ON contacts (name)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\" && (@request.auth.role = \"admin\" || owner.id = @request.auth.id)", "options": {}}, {"id": "messages", "name": "messages", "type": "base", "system": false, "schema": [{"id": "messages_contact_id", "name": "contact_id", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "contacts", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "messages_content", "name": "content", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "messages_is_user", "name": "is_user", "type": "bool", "system": false, "required": true, "unique": false, "options": {}}, {"id": "messages_timestamp", "name": "timestamp", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "messages_media_url", "name": "media_url", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "messages_media_type", "name": "media_type", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["image", "video", "audio", "document"]}}, {"id": "messages_media_name", "name": "media_name", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "messages_media_size", "name": "media_size", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null}}, {"id": "messages_channel", "name": "channel", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["app", "whatsapp", "facebook", "sms", "email"]}}, {"id": "messages_status", "name": "status", "type": "select", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "values": ["sent", "delivered", "read", "failed"]}}, {"id": "messages_media_file", "name": "media_file", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 1, "maxSize": 10485760, "mimeTypes": [], "thumbs": [], "protected": false}}], "indexes": ["CREATE INDEX idx_messages_contact ON messages (contact_id)", "CREATE INDEX idx_messages_timestamp ON messages (timestamp)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "tasks", "name": "tasks", "type": "base", "system": false, "schema": [{"id": "tasks_title", "name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "tasks_description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "tasks_contact_id", "name": "contact_id", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "contacts", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "tasks_due_date", "name": "due_date", "type": "date", "system": false, "required": true, "unique": false, "options": {"min": "", "max": ""}}, {"id": "tasks_status", "name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["pending", "in_progress", "completed"]}}, {"id": "tasks_priority", "name": "priority", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["low", "medium", "high"]}}, {"id": "tasks_assigned_to", "name": "assigned_to", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "tasks_completed_at", "name": "completed_at", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}], "indexes": ["CREATE INDEX idx_tasks_due_date ON tasks (due_date)", "CREATE INDEX idx_tasks_status ON tasks (status)", "CREATE INDEX idx_tasks_contact ON tasks (contact_id)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "opportunities", "name": "opportunities", "type": "base", "system": false, "schema": [{"id": "opportunities_title", "name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "opportunities_contact_id", "name": "contact_id", "type": "relation", "system": false, "required": true, "unique": false, "options": {"collectionId": "contacts", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "opportunities_value", "name": "value", "type": "number", "system": false, "required": true, "unique": false, "options": {"min": 0, "max": null}}, {"id": "opportunities_status", "name": "status", "type": "select", "system": false, "required": true, "unique": false, "options": {"maxSelect": 1, "values": ["lead", "proposal", "negotiation", "won", "lost"]}}, {"id": "opportunities_notes", "name": "notes", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "opportunities_expected_close_date", "name": "expected_close_date", "type": "date", "system": false, "required": false, "unique": false, "options": {"min": "", "max": ""}}, {"id": "opportunities_owner", "name": "owner", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "opportunities_products", "name": "products", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "opportunities_probability", "name": "probability", "type": "number", "system": false, "required": false, "unique": false, "options": {"min": 0, "max": 100}}], "indexes": ["CREATE INDEX idx_opportunities_contact ON opportunities (contact_id)", "CREATE INDEX idx_opportunities_status ON opportunities (status)"], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "options": {}}, {"id": "knowledge_base", "name": "knowledge_base", "type": "base", "system": false, "schema": [{"id": "kb_title", "name": "title", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_content", "name": "content", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_category", "name": "category", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_tags", "name": "tags", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "kb_author", "name": "author", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "kb_is_public", "name": "is_public", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "kb_attachments", "name": "attachments", "type": "file", "system": false, "required": false, "unique": false, "options": {"maxSelect": 10, "maxSize": 10485760, "mimeTypes": [], "thumbs": [], "protected": false}}], "indexes": ["CREATE INDEX idx_kb_category ON knowledge_base (category)", "CREATE INDEX idx_kb_title ON knowledge_base (title)"], "listRule": "@request.auth.id != \"\" || is_public = true", "viewRule": "@request.auth.id != \"\" || is_public = true", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\" && (@request.auth.role = \"admin\" || author.id = @request.auth.id)", "deleteRule": "@request.auth.id != \"\" && (@request.auth.role = \"admin\" || author.id = @request.auth.id)", "options": {}}, {"id": "settings", "name": "settings", "type": "base", "system": false, "schema": [{"id": "settings_key", "name": "key", "type": "text", "system": false, "required": true, "unique": true, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "settings_value", "name": "value", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "settings_description", "name": "description", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "settings_is_system", "name": "is_system", "type": "bool", "system": false, "required": false, "unique": false, "options": {}}, {"id": "settings_user_id", "name": "user_id", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "indexes": ["CREATE UNIQUE INDEX idx_settings_key ON settings (key)"], "listRule": "@request.auth.id != \"\" && (user_id = @request.auth.id || user_id = null || @request.auth.role = \"admin\")", "viewRule": "@request.auth.id != \"\" && (user_id = @request.auth.id || user_id = null || @request.auth.role = \"admin\")", "createRule": "@request.auth.id != \"\" && (user_id = @request.auth.id || @request.auth.role = \"admin\")", "updateRule": "@request.auth.id != \"\" && (user_id = @request.auth.id || @request.auth.role = \"admin\")", "deleteRule": "@request.auth.id != \"\" && (user_id = @request.auth.id || @request.auth.role = \"admin\")", "options": {}}, {"id": "activity_log", "name": "activity_log", "type": "base", "system": false, "schema": [{"id": "activity_user_id", "name": "user_id", "type": "relation", "system": false, "required": false, "unique": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "activity_action", "name": "action", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "activity_entity_type", "name": "entity_type", "type": "text", "system": false, "required": true, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "activity_entity_id", "name": "entity_id", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "activity_details", "name": "details", "type": "json", "system": false, "required": false, "unique": false, "options": {}}, {"id": "activity_ip_address", "name": "ip_address", "type": "text", "system": false, "required": false, "unique": false, "options": {"min": null, "max": null, "pattern": ""}}], "indexes": ["CREATE INDEX idx_activity_user ON activity_log (user_id)", "CREATE INDEX idx_activity_entity ON activity_log (entity_type, entity_id)", "CREATE INDEX idx_activity_created ON activity_log (created)"], "listRule": "@request.auth.role = \"admin\"", "viewRule": "@request.auth.role = \"admin\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.role = \"admin\"", "deleteRule": "@request.auth.role = \"admin\"", "options": {}}]}