import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Provider pour le service d'intégrité des données
final dataIntegrityServiceProvider = Provider<DataIntegrityService>((ref) {
  // Ici, vous pourriez avoir besoin de passer des références à d'autres services
  // si DataIntegrityService doit interagir avec eux directement pour obtenir des données.
  // Par exemple:
  // final invoiceService = ref.watch(invoiceServiceProvider);
  // final taskService = ref.watch(taskServiceProvider);
  return DataIntegrityService(ref);
});

class DataIntegrityService {

  DataIntegrityService(Ref ref);

  // Clé secrète pour la signature HMAC (NE PAS stocker en dur dans le code en production)
  // Idéalement, cela devrait provenir d'une configuration sécurisée ou d'une variable d'environnement.
  static const String _secretKey = 'votre_cle_secrete_tres_complexe_ici';

  /// Génère une signature HMAC SHA-256 pour les données fournies.
  String _generateSignature(String data) {
    final key = utf8.encode(_secretKey);
    final bytes = utf8.encode(data);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return digest.toString();
  }

  /// Sauvegarde les données avec leur signature.
  Future<void> saveDataWithSignature(String key, String data) async {
    final prefs = await SharedPreferences.getInstance();
    final signature = _generateSignature(data);
    await prefs.setString(key, data);
    await prefs.setString('${key}_signature', signature);
    // print('Data saved for key: $key with signature: $signature');
  }

  /// Vérifie l'intégrité des données en comparant la signature stockée avec une nouvelle signature.
  Future<bool> verifyDataIntegrity(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(key);
    final storedSignature = prefs.getString('${key}_signature');

    if (data == null || storedSignature == null) {
      // print('No data or signature found for key: $key');
      return false; // Ou gérer comme une violation d'intégrité si les données sont attendues
    }

    final currentSignature = _generateSignature(data);
    final isValid = currentSignature == storedSignature;
    if (isValid) {
      // print('Data integrity verified for key: $key');
    } else {
      // print(
      //   'Data integrity FAILED for key: $key. Stored: $storedSignature, Current: $currentSignature',
      // );
    }
    return isValid;
  }

  /// Collecte les données de différents services, les sérialise et génère une signature globale.
  /// Cette méthode est un exemple et devra être adaptée à la structure exacte de vos données.
  Future<void> generateAndStoreOverallIntegritySignature() async {
    // Exemple de collecte de données (à adapter)
    // final invoices = await _ref.read(invoiceServiceProvider).getInvoices();
    // final tasks = await _ref.read(taskServiceProvider).getTasks(); // Supposant une méthode getTasks
    // final products = _ref.read(productsProvider);

    // Pour cet exemple, nous allons simuler des données JSON.
    // Dans un cas réel, vous construiriez cet objet à partir des données de vos services.
    final Map<String, dynamic> comprehensiveData = {
      'invoices_summary': {'count': 10, 'total_amount': 5000.0},
      'tasks_summary': {'count': 25, 'completed': 15},
      'inventory_summary': {'product_count': 100, 'low_stock_items': 5},
      // Ajoutez d'autres résumés de données pertinentes ici
    };

    final jsonData = jsonEncode(comprehensiveData);
    await saveDataWithSignature('overall_app_data', jsonData);
    // print('Overall app data integrity signature generated and stored.');
  }

  /// Vérifie la signature globale de l'intégrité des données de l'application.
  Future<bool> checkOverallIntegrity() async {
    return await verifyDataIntegrity('overall_app_data');
  }
}
