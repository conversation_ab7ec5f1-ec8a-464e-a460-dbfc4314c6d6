import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/task.dart';

class TaskService {
  // Données fictives pour les tâches
  final List<Task> _tasks = [
    Task(
      id: '1',
      title: 'Contacter le client XYZ',
      description: 'Appeler pour discuter du nouveau projet',
      dueDate: DateTime.now().add(const Duration(days: 2)),
      priority: TaskPriority.high,
      status: TaskStatus.pending,
      assignedTo: 'user1',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    Task(
      id: '2',
      title: 'Préparer la facture mensuelle',
      description: 'Générer les factures pour tous les clients actifs',
      dueDate: DateTime.now().add(const Duration(days: 5)),
      priority: TaskPriority.medium,
      status: TaskStatus.inProgress,
      assignedTo: 'user2',
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    Task(
      id: '3',
      title: 'Mettre à jour l\'inventaire',
      description: 'Vérifier les stocks et commander les produits manquants',
      dueDate: DateTime.now().add(const Duration(days: 1)),
      priority: TaskPriority.low,
      status: TaskStatus.completed,
      assignedTo: 'user1',
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
  ];

  // Obtenir toutes les tâches
  Future<List<Task>> getTasks() async {
    // Simuler un délai réseau
    await Future.delayed(const Duration(milliseconds: 300));
    return _tasks;
  }

  // Obtenir une tâche par ID
  Future<Task?> getTaskById(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _tasks.firstWhere((task) => task.id == id);
    } catch (e) {
      return null;
    }
  }

  // Ajouter une nouvelle tâche
  Future<bool> addTask(Task task) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final newTask = task.copyWith(
      id: const Uuid().v4(),
      createdAt: DateTime.now(),
    );
    _tasks.add(newTask);
    return true;
  }

  // Mettre à jour une tâche existante
  Future<bool> updateTask(Task task) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index >= 0) {
      _tasks[index] = task;
      return true;
    }
    return false;
  }

  // Supprimer une tâche
  Future<bool> deleteTask(String id) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final initialLength = _tasks.length;
    _tasks.removeWhere((task) => task.id == id);
    return _tasks.length < initialLength;
  }
}

// Provider pour le service de tâches
final taskServiceProvider = Provider<TaskService>((ref) {
  return TaskService();
});

// Provider pour la liste des tâches
final tasksProvider = FutureProvider<List<Task>>((ref) async {
  final taskService = ref.watch(taskServiceProvider);
  return taskService.getTasks();
});

// Provider pour une tâche spécifique
final taskProvider = FutureProvider.family<Task?, String>((ref, id) async {
  final taskService = ref.watch(taskServiceProvider);
  return taskService.getTaskById(id);
});
