import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ai/ai_message.dart';
import '../models/knowledge_article.dart';
import '../services/ai/ai_service.dart';
import '../services/knowledge_service.dart';
import '../services/inventory_service.dart';

// Provider pour le service de chat IA de la base de connaissances
final knowledgeAIChatServiceProvider = Provider<KnowledgeAIChatService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  final knowledgeService = ref.watch(knowledgeServiceProvider);
  final inventoryService = ref.watch(
    inventoryServiceProvider,
  ); // Ajout de InventoryService
  return KnowledgeAIChatService(aiService, knowledgeService, inventoryService);
});

// Provider pour les messages du chat
final knowledgeChatMessagesProvider = StateNotifierProvider<
  KnowledgeChatMessagesNotifier,
  List<KnowledgeChatMessage>
>((ref) {
  return KnowledgeChatMessagesNotifier();
});

// Provider pour l'état de chargement du chat
final knowledgeChatLoadingProvider = StateProvider<bool>((ref) => false);

// Modèle pour un message de chat
class KnowledgeChatMessage {
  final String id;
  final bool isUser;
  final String content;
  final DateTime timestamp;

  KnowledgeChatMessage({
    required this.id,
    required this.isUser,
    required this.content,
    required this.timestamp,
  });
}

// Notifier pour les messages du chat
class KnowledgeChatMessagesNotifier
    extends StateNotifier<List<KnowledgeChatMessage>> {
  KnowledgeChatMessagesNotifier() : super([]);

  void addUserMessage(String content) {
    final message = KnowledgeChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      isUser: true,
      content: content,
      timestamp: DateTime.now(),
    );
    state = [...state, message];
  }

  void addAIMessage(String content) {
    final message = KnowledgeChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      isUser: false,
      content: content,
      timestamp: DateTime.now(),
    );
    state = [...state, message];
  }

  void clearMessages() {
    state = [];
  }
}

// Service pour le chat IA de la base de connaissances
class KnowledgeAIChatService {
  final AIService _aiService;
  final KnowledgeService _knowledgeService;
  final InventoryService _inventoryService; // Ajout de InventoryService

  KnowledgeAIChatService(
    this._aiService,
    this._knowledgeService,
    this._inventoryService,
  ); // Mise à jour du constructeur

  // Méthode pour envoyer une question à l'IA
  Future<String> askQuestion(String question) async {
    try {
      // Détecter si la question concerne le stock
      if (question.toLowerCase().contains('stock') ||
          question.toLowerCase().contains('disponible') ||
          question.toLowerCase().contains('disponibilité')) {
        // Extraire le nom du produit de la question (simplification, pourrait être amélioré avec NLP)
        // Pour l'instant, on suppose que le nom du produit est après "stock de" ou "disponibilité de"
        String productName = '';
        if (question.toLowerCase().contains('stock de ')) {
          productName =
              question
                  .substring(
                    question.toLowerCase().indexOf('stock de ') +
                        'stock de '.length,
                  )
                  .trim();
        } else if (question.toLowerCase().contains('disponibilité de ')) {
          productName =
              question
                  .substring(
                    question.toLowerCase().indexOf('disponibilité de ') +
                        'disponibilité de '.length,
                  )
                  .trim();
        } else if (question.toLowerCase().contains('disponible pour ')) {
          productName =
              question
                  .substring(
                    question.toLowerCase().indexOf('disponible pour ') +
                        'disponible pour '.length,
                  )
                  .trim();
        } else {
          // Tentative de récupération du nom du produit si aucun mot clé spécifique n'est trouvé
          // Ceci est une heuristique simple et pourrait nécessiter une logique plus avancée
          final words = question.split(' ');
          // On suppose que le nom du produit pourrait être les derniers mots avant un point d'interrogation ou à la fin
          // Ou qu'il pourrait être un mot en majuscule ou une séquence de mots capitalisés
          // Pour cet exemple, nous allons juste prendre le dernier mot comme une simplification grossière
          // et inviter l'utilisateur à être plus précis si ce n'est pas suffisant.
          if (words.isNotEmpty) {
            productName = words.last.replaceAll('?', '');
          }
        }

        if (productName.isNotEmpty) {
          final products = await _inventoryService.getProducts();
          final foundProducts =
              products
                  .where(
                    (p) => p.name.toLowerCase().contains(
                      productName.toLowerCase(),
                    ),
                  )
                  .toList();

          if (foundProducts.isNotEmpty) {
            String response =
                'Voici les informations de stock pour les produits correspondant à "$productName":\n';
            for (var product in foundProducts) {
              if (product.quantity > 0) {
                response +=
                    '- ${product.name}: ${product.quantity} en stock.\n';
              } else {
                response +=
                    '- ${product.name}: Rupture de stock. Nous faisons notre maximum pour réapprovisionner rapidement. N\'hésitez pas à nous contacter pour connaître les alternatives ou les délais de réapprovisionnement.\n';
              }
            }
            return response;
          } else {
            return 'Désolé, je n\'ai pas trouvé de produit correspondant à "$productName" dans notre inventaire.';
          }
        } else {
          return 'Pouvez-vous préciser le nom du produit pour lequel vous souhaitez connaître le stock ?';
        }
      }

      // Récupérer tous les articles de la base de connaissances
      final articles = await _knowledgeService.getArticles();

      // Créer un contexte avec les articles pertinents
      final context = _createContext(articles, question);

      // Créer le prompt pour l'IA
      const chatbotName =
          'Daniel'; // Vous pouvez changer ce nom pour personnaliser le nom du chatbot
      final systemPrompt = '''
Tu es $chatbotName, l'assistant virtuel pour HCP-DESIGN, une entreprise spécialisée dans l'impression personnalisée et la création graphique.
Tu as accès à la base de connaissances de l'entreprise et tu dois utiliser ces informations pour répondre aux questions.

Voici les informations pertinentes de la base de connaissances:
$context

Réponds aux questions en te basant sur ces informations. Si tu ne trouves pas l'information dans la base de connaissances, dis-le clairement et suggère de contacter le service client pour plus d'informations.

Sois toujours professionnel, courtois et précis dans tes réponses.
''';

      // Envoyer la question à l'IA
      final messages = [AIMessage(role: AIMessageRole.user, content: question)];

      final response = await _aiService.sendMessage(
        messages,
        overrideSystemPrompt: systemPrompt,
      );

      return response;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi de la question à l\'IA: $e');

      // Vérifier si c'est une erreur de quota OpenAI
      if (e.toString().contains('insufficient_quota')) {
        return 'Votre compte OpenAI a dépassé son quota d\'utilisation. Veuillez vérifier votre plan et vos informations de facturation sur la plateforme OpenAI, ou essayez de configurer un autre fournisseur d\'IA dans les paramètres.';
      }

      return 'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.';
    }
  }

  // Méthode pour créer un contexte à partir des articles pertinents
  String _createContext(List<dynamic> articles, String question) {
    if (articles.isEmpty) {
      return "Aucun article disponible dans la base de connaissances.";
    }

    // Convertir les articles en KnowledgeArticle typés
    final typedArticles =
        articles
            .map((article) {
              if (article is KnowledgeArticle) {
                return article;
              }
              return null;
            })
            .whereType<KnowledgeArticle>()
            .toList();

    if (typedArticles.isEmpty) {
      return "Aucun article valide disponible dans la base de connaissances.";
    }

    // Filtrer les articles publiés uniquement
    final publishedArticles =
        typedArticles.where((article) => article.isPublished).toList();

    if (publishedArticles.isEmpty) {
      return "Aucun article publié disponible dans la base de connaissances.";
    }

    // Rechercher les articles pertinents (simple recherche par mots-clés)
    final keywords = _extractKeywords(question);
    final relevantArticles =
        publishedArticles.where((article) {
          final articleText =
              '${article.title} ${article.content} ${article.tags.join(' ')}';
          return keywords.any(
            (keyword) =>
                articleText.toLowerCase().contains(keyword.toLowerCase()),
          );
        }).toList();

    // Si aucun article pertinent n'est trouvé, utiliser tous les articles publiés
    final articlesToUse =
        relevantArticles.isEmpty ? publishedArticles : relevantArticles;

    // Limiter le nombre d'articles pour éviter de dépasser les limites de tokens
    final limitedArticles = articlesToUse.take(5).toList();

    // Créer le contexte
    final context = limitedArticles
        .map(
          (article) => '''
ARTICLE: ${article.title}
CATÉGORIE: ${article.category}
CONTENU: ${article.content}
TAGS: ${article.tags.join(', ')}
---
''',
        )
        .join('\n');

    return context;
  }

  // Méthode pour extraire les mots-clés d'une question
  List<String> _extractKeywords(String question) {
    // Liste de mots vides en français
    final stopWords = [
      'le',
      'la',
      'les',
      'un',
      'une',
      'des',
      'du',
      'de',
      'à',
      'au',
      'aux',
      'et',
      'ou',
      'mais',
      'donc',
      'car',
      'ni',
      'que',
      'qui',
      'quoi',
      'dont',
      'où',
      'comment',
      'pourquoi',
      'quand',
      'est-ce',
      'ce',
      'cette',
      'ces',
      'mon',
      'ma',
      'mes',
      'ton',
      'ta',
      'tes',
      'son',
      'sa',
      'ses',
      'notre',
      'nos',
      'votre',
      'vos',
      'leur',
      'leurs',
      'je',
      'tu',
      'il',
      'elle',
      'on',
      'nous',
      'vous',
      'ils',
      'elles',
      'moi',
      'toi',
      'lui',
      'eux',
      'pour',
      'par',
      'en',
      'dans',
      'sur',
      'sous',
      'avec',
      'sans',
      'chez',
      'entre',
    ];

    // Nettoyer la question et extraire les mots
    final words =
        question
            .replaceAll(RegExp(r'[^\w\s]'), ' ')
            .split(' ')
            .where(
              (word) =>
                  word.isNotEmpty && !stopWords.contains(word.toLowerCase()),
            )
            .toList();

    return words;
  }
}
