import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../models/ai/ai_performance.dart';
import '../../models/ai/ai_provider.dart';
import '../../services/ai/ai_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_button.dart';

class AIPerformanceScreen extends ConsumerWidget {
  const AIPerformanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final performances = ref.watch(aiPerformanceProvider);

    // Calculer les statistiques
    final totalRequests = performances.length;
    final totalTokens = performances.fold<int>(
      0,
      (sum, perf) => sum + perf.promptLength + perf.responseLength,
    );
    final totalCost = performances.fold<double>(
      0,
      (sum, perf) => sum + perf.estimatedCost,
    );
    final averageResponseTime =
        totalRequests > 0
            ? performances.fold<int>(0, (sum, perf) => sum + perf.duration) /
                totalRequests
            : 0;

    // Regrouper par fournisseur
    final providerCounts = <AIProviderType, int>{};
    for (final perf in performances) {
      providerCounts[perf.provider] = (providerCounts[perf.provider] ?? 0) + 1;
    }

    // Regrouper par jour pour le graphique
    final performancesByDay = <DateTime, List<AIPerformance>>{};
    for (final perf in performances) {
      final day = DateTime(
        perf.timestamp.year,
        perf.timestamp.month,
        perf.timestamp.day,
      );
      performancesByDay[day] = [...performancesByDay[day] ?? [], perf];
    }

    // Trier les jours
    final sortedDays = performancesByDay.keys.toList()..sort();

    // Préparer les données pour le graphique
    final requestsData = <FlSpot>[];
    final tokensData = <FlSpot>[];
    final costData = <FlSpot>[];

    for (int i = 0; i < sortedDays.length; i++) {
      final day = sortedDays[i];
      final dayPerformances = performancesByDay[day]!;

      requestsData.add(FlSpot(i.toDouble(), dayPerformances.length.toDouble()));

      final dayTokens = dayPerformances.fold<int>(
        0,
        (sum, perf) => sum + perf.promptLength + perf.responseLength,
      );
      tokensData.add(
        FlSpot(i.toDouble(), dayTokens.toDouble() / 1000),
      ); // en milliers

      final dayCost = dayPerformances.fold<double>(
        0,
        (sum, perf) => sum + perf.estimatedCost,
      );
      costData.add(FlSpot(i.toDouble(), dayCost));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Performances de l\'IA'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            performances.isEmpty
                ? const Center(
                  child: Text(
                    'Aucune donnée de performance disponible',
                    style: TextStyle(color: Colors.white, fontSize: 18),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Analyse des performances de l\'IA',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Statistiques générales
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              'Requêtes totales',
                              totalRequests.toString(),
                              FontAwesomeIcons.message,
                              NeonTheme.neonPurple,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildStatCard(
                              'Tokens totaux',
                              NumberFormat.compact().format(totalTokens),
                              FontAwesomeIcons.fileLines,
                              NeonTheme.neonCyan,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatCard(
                              'Coût estimé',
                              '\$${totalCost.toStringAsFixed(2)}',
                              FontAwesomeIcons.dollarSign,
                              NeonTheme.neonGreen,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildStatCard(
                              'Temps de réponse moyen',
                              '${(averageResponseTime / 1000).toStringAsFixed(2)} s',
                              FontAwesomeIcons.stopwatch,
                              NeonTheme.neonTurquoise,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Graphique des requêtes par jour
                      if (sortedDays.isNotEmpty) ...[
                        NeonCard(
                          color: NeonTheme.neonPurple,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Requêtes par jour',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  height: 200,
                                  child: LineChart(
                                    LineChartData(
                                      gridData: const FlGridData(show: false),
                                      titlesData: FlTitlesData(
                                        leftTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            getTitlesWidget: (value, meta) {
                                              return Text(
                                                value.toInt().toString(),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10,
                                                ),
                                              );
                                            },
                                            reservedSize: 30,
                                          ),
                                        ),
                                        bottomTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            getTitlesWidget: (value, meta) {
                                              if (value.toInt() >= 0 &&
                                                  value.toInt() <
                                                      sortedDays.length) {
                                                final day =
                                                    sortedDays[value.toInt()];
                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                        top: 8,
                                                      ),
                                                  child: Text(
                                                    DateFormat(
                                                      'dd/MM',
                                                    ).format(day),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 10,
                                                    ),
                                                  ),
                                                );
                                              }
                                              return const SizedBox.shrink();
                                            },
                                            reservedSize: 30,
                                          ),
                                        ),
                                        rightTitles: const AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: false,
                                          ),
                                        ),
                                        topTitles: const AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: false,
                                          ),
                                        ),
                                      ),
                                      borderData: FlBorderData(show: false),
                                      lineBarsData: [
                                        LineChartBarData(
                                          spots: requestsData,
                                          isCurved: true,
                                          color: NeonTheme.neonPurple,
                                          barWidth: 3,
                                          isStrokeCapRound: true,
                                          dotData: const FlDotData(show: false),
                                          belowBarData: BarAreaData(
                                            show: true,
                                            color: NeonTheme.neonPurple
                                                .withValues(
                                                  alpha: 51,
                                                ), // 0.2 * 255 ≈ 51
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Graphique des tokens par jour
                        NeonCard(
                          color: NeonTheme.neonCyan,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Tokens par jour (en milliers)',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  height: 200,
                                  child: LineChart(
                                    LineChartData(
                                      gridData: const FlGridData(show: false),
                                      titlesData: FlTitlesData(
                                        leftTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            getTitlesWidget: (value, meta) {
                                              return Text(
                                                value.toInt().toString(),
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 10,
                                                ),
                                              );
                                            },
                                            reservedSize: 30,
                                          ),
                                        ),
                                        bottomTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            getTitlesWidget: (value, meta) {
                                              if (value.toInt() >= 0 &&
                                                  value.toInt() <
                                                      sortedDays.length) {
                                                final day =
                                                    sortedDays[value.toInt()];
                                                return Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                        top: 8,
                                                      ),
                                                  child: Text(
                                                    DateFormat(
                                                      'dd/MM',
                                                    ).format(day),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 10,
                                                    ),
                                                  ),
                                                );
                                              }
                                              return const SizedBox.shrink();
                                            },
                                            reservedSize: 30,
                                          ),
                                        ),
                                        rightTitles: const AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: false,
                                          ),
                                        ),
                                        topTitles: const AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: false,
                                          ),
                                        ),
                                      ),
                                      borderData: FlBorderData(show: false),
                                      lineBarsData: [
                                        LineChartBarData(
                                          spots: tokensData,
                                          isCurved: true,
                                          color: NeonTheme.neonCyan,
                                          barWidth: 3,
                                          isStrokeCapRound: true,
                                          dotData: const FlDotData(show: false),
                                          belowBarData: BarAreaData(
                                            show: true,
                                            color: NeonTheme.neonCyan
                                                .withValues(
                                                  alpha: 51,
                                                ), // 0.2 * 255 ≈ 51
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 24),

                      // Bouton pour effacer les données
                      Center(
                        child: NeonButton(
                          text: 'Effacer les données',
                          icon: FontAwesomeIcons.trash,
                          color: Colors.red,
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  backgroundColor: Colors.black,
                                  title: const Text(
                                    'Effacer les données',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  content: const Text(
                                    'Êtes-vous sûr de vouloir effacer toutes les données de performance ?',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text(
                                        'Annuler',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        ref
                                            .read(
                                              aiPerformanceProvider.notifier,
                                            )
                                            .clearPerformance();
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text(
                                        'Effacer',
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return NeonCard(
      color: color,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
