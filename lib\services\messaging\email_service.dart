import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../models/messaging/message.dart';
import '../../models/messaging/channel_config.dart';

class EmailService {
  bool _isInitialized = false;
  late ChannelConfig _config;
  
  Future<void> initialize(ChannelConfig config) async {
    _config = config;
    _isInitialized = true;
    debugPrint('Email service initialized');
  }

  bool get isInitialized => _isInitialized;

  Future<void> sendMessage(Message message) async {
    if (!_isInitialized) {
      throw Exception('Email service not initialized');
    }

    try {
      final recipientEmail = message.contactId;
      final apiKey = _config.apiKey;
      
      // Utilisation de SendGrid comme exemple
      const endpoint = 'https://api.sendgrid.com/v3/mail/send';
      
      Map<String, dynamic> payload = {
        'personalizations': [
          {
            'to': [{'email': recipientEmail}],
            'subject': 'Message de HCP-DESIGN',
          }
        ],
        'from': {'email': _config.additionalConfig?['fromEmail'] ?? '<EMAIL>'},
        'content': [
          {
            'type': 'text/plain',
            'value': message.content,
          }
        ],
      };

      // Si le message contient un média
      if (message.mediaUrl != null) {
        payload['attachments'] = [
          {
            'content': base64Encode(await _downloadFile(message.mediaUrl!)),
            'filename': message.mediaName ?? 'attachment',
            'type': _getContentType(message.mediaType),
            'disposition': 'attachment',
          }
        ];
      }

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode(payload),
      );

      if (response.statusCode != 202) {
        throw Exception('Failed to send email: ${response.body}');
      }

      debugPrint('Email sent successfully');
    } catch (e) {
      debugPrint('Error sending email: $e');
      rethrow;
    }
  }

  Future<List<int>> _downloadFile(String url) async {
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      return response.bodyBytes;
    } else {
      throw Exception('Failed to download file: ${response.statusCode}');
    }
  }

  String _getContentType(MessageType? type) {
    switch (type) {
      case MessageType.image:
        return 'image/jpeg';
      case MessageType.video:
        return 'video/mp4';
      case MessageType.audio:
        return 'audio/mpeg';
      case MessageType.document:
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }

  Future<void> processWebhook(Map<String, dynamic> payload) async {
    if (!_isInitialized) {
      throw Exception('Email service not initialized');
    }

    try {
      // Traiter les événements SendGrid
      final event = payload['event'];
      final email = payload['email'];
      final timestamp = payload['timestamp'];
      final messageId = payload['sg_message_id'];
      
      debugPrint('Received email webhook: Event=$event, Email=$email, Timestamp=$timestamp');
      
      // Traiter les notifications de statut
      if (event != null) {
        MessageStatus messageStatus;
        switch (event) {
          case 'delivered':
            messageStatus = MessageStatus.delivered;
            break;
          case 'open':
            messageStatus = MessageStatus.read;
            break;
          case 'processed':
          case 'sent':
            messageStatus = MessageStatus.sent;
            break;
          default:
            messageStatus = MessageStatus.failed;
        }
        
        // Ici, vous devriez mettre à jour le statut du message dans votre système
        debugPrint('Email $messageId status updated to $messageStatus');
      }
    } catch (e) {
      debugPrint('Error processing email webhook: $e');
      rethrow;
    }
  }
}
