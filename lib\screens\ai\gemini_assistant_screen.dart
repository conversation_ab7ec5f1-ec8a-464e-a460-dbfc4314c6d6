import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../providers/theme_provider.dart';
import '../../services/ai/gemini_service.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class GeminiAssistantScreen extends ConsumerStatefulWidget {
  const GeminiAssistantScreen({super.key});

  @override
  ConsumerState<GeminiAssistantScreen> createState() => _GeminiAssistantScreenState();
}

class _GeminiAssistantScreenState extends ConsumerState<GeminiAssistantScreen> {
  final TextEditingController _promptController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  bool _isLoading = false;
  String _generatedResponse = '';
  final List<Map<String, String>> _chatHistory = [];
  bool _isChatMode = true;

  @override
  void dispose() {
    _promptController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Méthode pour envoyer un message
  Future<void> _sendMessage() async {
    final prompt = _promptController.text.trim();
    if (prompt.isEmpty) return;

    setState(() {
      _isLoading = true;
      if (_isChatMode) {
        _chatHistory.add({
          'role': 'user',
          'content': prompt,
        });
      }
    });

    _scrollToBottom();
    _promptController.clear();

    try {
      final geminiService = ref.read(geminiServiceProvider);
      
      if (!geminiService.isConfigured) {
        setState(() {
          if (_isChatMode) {
            _chatHistory.add({
              'role': 'assistant',
              'content': "Veuillez configurer l'API Gemini dans les paramètres.",
            });
          } else {
            _generatedResponse = "Veuillez configurer l'API Gemini dans les paramètres.";
          }
          _isLoading = false;
        });
        _scrollToBottom();
        return;
      }

      String response;
      if (_isChatMode) {
        response = await geminiService.sendChatMessage(prompt);
      } else {
        response = await geminiService.generateContent(prompt);
      }

      setState(() {
        if (_isChatMode) {
          _chatHistory.add({
            'role': 'assistant',
            'content': response,
          });
        } else {
          _generatedResponse = response;
        }
        _isLoading = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        if (_isChatMode) {
          _chatHistory.add({
            'role': 'assistant',
            'content': "Erreur: $e",
          });
        } else {
          _generatedResponse = "Erreur: $e";
        }
        _isLoading = false;
      });
      _scrollToBottom();
    }
  }

  // Méthode pour faire défiler vers le bas
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Réinitialiser la conversation
  void _resetConversation() {
    final geminiService = ref.read(geminiServiceProvider);
    geminiService.resetChat();
    
    setState(() {
      _chatHistory.clear();
      _generatedResponse = "";
    });
  }

  // Basculer entre le mode chat et le mode génération
  void _toggleMode() {
    setState(() {
      _isChatMode = !_isChatMode;
      _resetConversation();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isChatMode ? 'Assistant IA - Chat' : 'Assistant IA - Génération'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
        actions: [
          // Bouton pour basculer entre les modes
          IconButton(
            icon: Icon(_isChatMode ? Icons.text_fields : Icons.chat),
            tooltip: _isChatMode ? 'Passer en mode génération' : 'Passer en mode chat',
            onPressed: _toggleMode,
          ),
          // Bouton pour réinitialiser la conversation
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Réinitialiser la conversation',
            onPressed: _resetConversation,
          ),
          // Bouton pour accéder aux paramètres
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Paramètres Gemini',
            onPressed: () {
              context.push('/ai/gemini/settings');
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child: Column(
          children: [
            // Zone de contenu (chat ou génération)
            Expanded(
              child: _isChatMode ? _buildChatView(primaryColor, secondaryColor) : _buildGenerationView(),
            ),

            // Zone de saisie
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: NeonTextField(
                      controller: _promptController,
                      labelText: 'Message',
                      hintText: _isChatMode 
                          ? 'Posez une question à l\'assistant...' 
                          : 'Décrivez ce que vous souhaitez générer...',
                      maxLines: 3,
                      minLines: 1,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 16),
                  NeonButton(
                    icon: Icons.send,
                    text: 'Envoyer',
                    color: primaryColor,
                    isLoading: _isLoading,
                    onPressed: () {
                      if (!_isLoading) {
                        _sendMessage();
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Construire la vue de chat
  Widget _buildChatView(Color primaryColor, Color secondaryColor) {
    return _chatHistory.isEmpty && !_isLoading
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  FontAwesomeIcons.robot,
                  size: 64,
                  color: primaryColor.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                ),
                const SizedBox(height: 24),
                Text(
                  'Assistant IA Gemini',
                  style: TextStyle(
                    color: primaryColor,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Posez une question pour commencer la conversation',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                  ),
                ),
              ],
            ),
          )
        : ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: _chatHistory.length + (_isLoading ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == _chatHistory.length) {
                // Indicateur de chargement
                return _buildLoadingIndicator(primaryColor);
              }

              final message = _chatHistory[index];
              final isUser = message['role'] == 'user';

              return _buildChatBubble(
                message: message['content'] ?? '',
                isUser: isUser,
                primaryColor: primaryColor,
                secondaryColor: secondaryColor,
              );
            },
          );
  }

  // Construire la vue de génération
  Widget _buildGenerationView() {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isLoading)
            _buildLoadingIndicator(ref.watch(themeProvider).primaryColor)
          else if (_generatedResponse.isNotEmpty)
            NeonCard(
              padding: const EdgeInsets.all(16),
              child: SelectableText(
                _generatedResponse,
                style: const TextStyle(color: Colors.white),
              ),
            )
          else
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    FontAwesomeIcons.wandMagicSparkles,
                    size: 64,
                    color: ref.watch(themeProvider).primaryColor.withValues(alpha: 128),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Générateur de contenu IA',
                    style: TextStyle(
                      color: ref.watch(themeProvider).primaryColor,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Décrivez ce que vous souhaitez générer',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 179),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Construire une bulle de chat
  Widget _buildChatBubble({
    required String message,
    required bool isUser,
    required Color primaryColor,
    required Color secondaryColor,
  }) {
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isUser
              ? secondaryColor.withValues(alpha: 51) // 0.2 * 255 ≈ 51
              : Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isUser
                ? secondaryColor.withValues(alpha: 77) // 0.3 * 255 ≈ 77
                : primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          ),
        ),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.8,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isUser ? 'Vous' : 'Assistant',
              style: TextStyle(
                color: isUser ? secondaryColor : primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            SelectableText(
              message,
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  // Construire l'indicateur de chargement
  Widget _buildLoadingIndicator(Color primaryColor) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Réflexion en cours...',
              style: TextStyle(color: primaryColor),
            ),
          ],
        ),
      ),
    );
  }
}
