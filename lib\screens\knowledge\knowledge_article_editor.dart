import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:file_picker/file_picker.dart'; // Ajout pour FilePicker
import '../../models/knowledge_article.dart';
import '../../services/knowledge_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_button_type.dart';
import '../../widgets/neon_text_field.dart';

class KnowledgeArticleEditor extends ConsumerStatefulWidget {
  final KnowledgeArticle? article; // Null pour un nouvel article

  const KnowledgeArticleEditor({super.key, this.article});

  @override
  ConsumerState<KnowledgeArticleEditor> createState() =>
      _KnowledgeArticleEditorState();
}

class _KnowledgeArticleEditorState
    extends ConsumerState<KnowledgeArticleEditor> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _titleController;
  late final TextEditingController _contentController;
  late final TextEditingController _tagController;
  late final TextEditingController _imageUrlController; // Ajout pour l'URL de l'image
  late final TextEditingController _videoUrlController; // Ajout pour l'URL de la vidéo
  late String _selectedCategory;
  final List<String> _tags = [];
  bool _isPublished = true;

  @override
  void initState() {
    super.initState();
    // Initialiser les contrôleurs avec les valeurs de l'article si en mode édition
    final article = widget.article;
    _titleController = TextEditingController(text: article?.title ?? '');
    _contentController = TextEditingController(text: article?.content ?? '');
    _tagController = TextEditingController();
    _imageUrlController = TextEditingController(text: article?.imageUrl ?? ''); // Initialisation pour l'image
    _videoUrlController = TextEditingController(text: article?.videoUrl ?? ''); // Initialisation pour la vidéo
    _selectedCategory =
        article?.category ??
        ref.read(knowledgeCategoriesProvider).firstWhere((c) => c != 'Tous');
    if (article != null) {
      _tags.addAll(article.tags);
      _isPublished = article.isPublished;
    }
  }

  Future<void> _pickMedia(FileType fileType) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        // Pour simplifier, nous stockons le chemin local. 
        // Pour une application en production, il faudrait uploader le fichier vers un serveur 
        // et stocker l'URL distante, ou gérer le stockage local de manière plus robuste.
        setState(() {
          if (fileType == FileType.image) {
            _imageUrlController.text = filePath;
          } else if (fileType == FileType.video) {
            _videoUrlController.text = filePath;
          }
        });
      } else {
        // L'utilisateur a annulé la sélection
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Aucun fichier sélectionné')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection du fichier: ${e.toString()}')),
        );
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    _imageUrlController.dispose(); // Dispose pour l'image
    _videoUrlController.dispose(); // Dispose pour la vidéo
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _saveArticle() async {
    if (!_formKey.currentState!.validate()) return;

    final knowledgeService = ref.read(knowledgeServiceProvider);
    final article = KnowledgeArticle(
      id: widget.article?.id ?? const Uuid().v4(),
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      category: _selectedCategory,
      tags: _tags,
      lastUpdated: DateTime.now(),
      isPublished: _isPublished,
      imageUrl: _imageUrlController.text.trim(), // Ajout de l'URL de l'image
      videoUrl: _videoUrlController.text.trim(), // Ajout de l'URL de la vidéo
    );

    try {
      if (widget.article == null) {
        // Nouvel article
        await knowledgeService.addArticle(article);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Article créé avec succès')),
          );
        }
      } else {
        // Mise à jour d'un article existant
        await knowledgeService.updateArticle(article);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Article mis à jour avec succès')),
          );
        }
      }

      // Rafraîchir la liste des articles
      ref.invalidate(knowledgeArticlesProvider);

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur: ${e.toString()}')));
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    final categories =
        ref
            .watch(knowledgeCategoriesProvider)
            .where((c) => c != 'Tous')
            .toList();

    return Scaffold(
      backgroundColor: const Color(0xFF0F0F1E),
      appBar: AppBar(
        title: Text(
          widget.article == null ? 'Nouvel article' : 'Modifier l\'article',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1A1A2E),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          Switch(
            value: _isPublished,
            onChanged: (value) {
              setState(() {
                _isPublished = value;
              });
            },
            activeColor: NeonTheme.neonCyan,
            activeTrackColor: NeonTheme.neonCyan.withAlpha(100),
          ),
          const SizedBox(width: 8),
          Text(
            _isPublished ? 'Publié' : 'Brouillon',
            style: const TextStyle(color: Colors.white),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Titre
            NeonTextField(
              controller: _titleController,
              labelText: 'Titre',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez entrer un titre';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Catégorie
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: InputDecoration(
                labelText: 'Catégorie',
                labelStyle: const TextStyle(color: Colors.white70),
                filled: true,
                fillColor: const Color(0xFF1A1A2E),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.white.withAlpha(30)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: NeonTheme.neonCyan),
                ),
              ),
              dropdownColor: const Color(0xFF1A1A2E),
              style: const TextStyle(color: Colors.white),
              items:
                  categories.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez sélectionner une catégorie';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Contenu
            NeonTextField(
              controller: _contentController,
              labelText: 'Contenu',
              maxLines: 10,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez entrer un contenu';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Tags
            Row(
              children: [
                Expanded(
                  child: NeonTextField(
                    controller: _tagController,
                    labelText: 'Ajouter un tag',
                    onSubmitted: (_) => _addTag(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.add, color: NeonTheme.neonCyan),
                  onPressed: _addTag,
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Liste des tags
            Wrap(
              spacing: 8,
              children:
                  _tags.map((tag) {
                    return Chip(
                      label: Text(
                        tag,
                        style: const TextStyle(color: Colors.white),
                      ),
                      backgroundColor: const Color(0xFF1A1A2E),
                      deleteIcon: const Icon(
                        Icons.close,
                        size: 16,
                        color: Colors.white70,
                      ),
                      onDeleted: () => _removeTag(tag),
                    );
                  }).toList(),
            ),
            const SizedBox(height: 16),

            // Champ Image URL
            NeonTextField(
              controller: _imageUrlController,
              labelText: 'URL de l\'image',
              readOnly: true, // Le champ est en lecture seule, modifié par le FilePicker
              suffixIcon: IconButton(
                icon: const Icon(Icons.attach_file, color: NeonTheme.neonCyan),
                onPressed: () => _pickMedia(FileType.image),
              ),
            ),
            const SizedBox(height: 16),

            // Champ Vidéo URL
            NeonTextField(
              controller: _videoUrlController,
              labelText: 'URL de la vidéo',
              readOnly: true, // Le champ est en lecture seule, modifié par le FilePicker
              suffixIcon: IconButton(
                icon: const Icon(Icons.video_library, color: NeonTheme.neonGreen),
                onPressed: () => _pickMedia(FileType.video),
              ),
            ),
            const SizedBox(height: 24),

            // Bouton de sauvegarde
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                NeonButton(
                  text: 'Annuler',
                  onPressed: () => Navigator.of(context).pop(),
                  type: NeonButtonType.secondary,
                ),
                const SizedBox(width: 16),
                NeonButton(
                  text: widget.article == null ? 'Créer' : 'Mettre à jour',
                  onPressed: _saveArticle,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
