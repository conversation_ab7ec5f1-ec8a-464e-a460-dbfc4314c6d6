// Script pour créer les collections via l'API Admin de PocketBase
// Exécutez ce script dans la console de votre navigateur après vous être connecté à l'interface d'administration

// Fonction pour créer une collection
async function createCollection(collectionData) {
  const response = await fetch('/api/collections', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(collectionData),
  });
  
  if (!response.ok) {
    const error = await response.json();
    console.error(`Erreur lors de la création de la collection ${collectionData.name}:`, error);
    return null;
  }
  
  const result = await response.json();
  console.log(`Collection ${collectionData.name} créée avec succès:`, result);
  return result;
}

// Créer la collection "contacts"
createCollection({
  name: 'contacts',
  schema: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'email',
      type: 'email',
    },
    {
      name: 'phone',
      type: 'text',
    },
    {
      name: 'company',
      type: 'text',
    },
    {
      name: 'notes',
      type: 'text',
    },
    {
      name: 'avatar',
      type: 'file',
      options: {
        maxSelect: 1,
        maxSize: 5242880,
        mimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
        thumbs: ['100x100'],
      },
    },
  ],
  listRule: '@request.auth.id != ""',
  viewRule: '@request.auth.id != ""',
  createRule: '@request.auth.id != ""',
  updateRule: '@request.auth.id != ""',
  deleteRule: '@request.auth.id != ""',
})
.then(() => {
  // Créer la collection "messages"
  return createCollection({
    name: 'messages',
    schema: [
      {
        name: 'contact',
        type: 'relation',
        required: true,
        options: {
          collectionId: 'contacts',
          cascadeDelete: true,
          maxSelect: 1,
        },
      },
      {
        name: 'content',
        type: 'text',
        required: true,
      },
      {
        name: 'is_user',
        type: 'bool',
        required: true,
      },
      {
        name: 'timestamp',
        type: 'date',
        required: true,
      },
      {
        name: 'media_url',
        type: 'text',
      },
      {
        name: 'media_type',
        type: 'select',
        options: {
          values: ['image', 'video', 'audio', 'document'],
        },
      },
      {
        name: 'channel',
        type: 'select',
        options: {
          values: ['app', 'whatsapp', 'facebook', 'sms', 'email'],
        },
      },
    ],
    listRule: '@request.auth.id != ""',
    viewRule: '@request.auth.id != ""',
    createRule: '@request.auth.id != ""',
    updateRule: '@request.auth.id != ""',
    deleteRule: '@request.auth.id != ""',
  });
})
.then(() => {
  // Créer la collection "tasks"
  return createCollection({
    name: 'tasks',
    schema: [
      {
        name: 'title',
        type: 'text',
        required: true,
      },
      {
        name: 'description',
        type: 'text',
      },
      {
        name: 'contact',
        type: 'relation',
        options: {
          collectionId: 'contacts',
          cascadeDelete: false,
          maxSelect: 1,
        },
      },
      {
        name: 'due_date',
        type: 'date',
        required: true,
      },
      {
        name: 'status',
        type: 'select',
        required: true,
        options: {
          values: ['pending', 'in_progress', 'completed'],
        },
      },
      {
        name: 'priority',
        type: 'select',
        required: true,
        options: {
          values: ['low', 'medium', 'high'],
        },
      },
    ],
    listRule: '@request.auth.id != ""',
    viewRule: '@request.auth.id != ""',
    createRule: '@request.auth.id != ""',
    updateRule: '@request.auth.id != ""',
    deleteRule: '@request.auth.id != ""',
  });
})
.then(() => {
  // Créer la collection "knowledge_base"
  return createCollection({
    name: 'knowledge_base',
    schema: [
      {
        name: 'title',
        type: 'text',
        required: true,
      },
      {
        name: 'content',
        type: 'text',
        required: true,
      },
      {
        name: 'category',
        type: 'text',
        required: true,
      },
      {
        name: 'tags',
        type: 'json',
      },
    ],
    listRule: '@request.auth.id != ""',
    viewRule: '@request.auth.id != ""',
    createRule: '@request.auth.id != ""',
    updateRule: '@request.auth.id != ""',
    deleteRule: '@request.auth.id != ""',
  });
})
.then(() => {
  console.log('Toutes les collections ont été créées avec succès!');
})
.catch((error) => {
  console.error('Erreur lors de la création des collections:', error);
});
