import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/theme/theme_export.dart';
import '../../providers/theme_provider.dart';
import '../../services/theme_export_service.dart'
    hide themeExportServiceProvider;
import '../../widgets/neon_button.dart';
import '../../widgets/neon_button_size.dart';
import '../../widgets/neon_card.dart';

// Définir un provider local pour le service d'exportation de thèmes
final _themeExportServiceProvider = Provider<ThemeExportService>((ref) {
  return ThemeExportService();
});

/// Écran de gestion des thèmes
class ThemeManagerScreen extends ConsumerStatefulWidget {
  const ThemeManagerScreen({super.key});

  @override
  ConsumerState<ThemeManagerScreen> createState() => _ThemeManagerScreenState();
}

class _ThemeManagerScreenState extends ConsumerState<ThemeManagerScreen> {
  bool _isLoading = false;
  List<ThemeExport> _customThemes = [];
  String? _statusMessage;
  bool _isError = false;

  @override
  void initState() {
    super.initState();
    _loadCustomThemes();
  }

  /// Charge les thèmes personnalisés
  Future<void> _loadCustomThemes() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Chargement des thèmes personnalisés...';
      _isError = false;
    });

    try {
      final themeService = ref.read(_themeExportServiceProvider);
      final themes = await themeService.getCustomThemes();

      setState(() {
        _customThemes = themes;
        _isLoading = false;
        _statusMessage =
            themes.isEmpty
                ? 'Aucun thème personnalisé trouvé'
                : '${themes.length} thème(s) personnalisé(s) trouvé(s)';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Erreur lors du chargement des thèmes: $e';
        _isError = true;
      });
    }
  }

  /// Exporte un thème prédéfini
  Future<void> _exportPredefinedTheme(String themeName) async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Exportation du thème $themeName...';
      _isError = false;
    });

    try {
      final themeService = ref.read(_themeExportServiceProvider);
      final theme = themeService.exportPredefinedTheme(themeName);
      await themeService.shareTheme(theme);

      setState(() {
        _isLoading = false;
        _statusMessage = 'Thème $themeName exporté avec succès';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Erreur lors de l\'exportation du thème: $e';
        _isError = true;
      });
    }
  }

  /// Importe un thème depuis un fichier
  Future<void> _importThemeFromFile() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Sélection du fichier de thème...';
      _isError = false;
    });

    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Aucun fichier sélectionné';
        });
        return;
      }

      final file = result.files.first;
      final filePath = file.path;

      if (filePath == null) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Chemin de fichier invalide';
          _isError = true;
        });
        return;
      }

      setState(() {
        _statusMessage = 'Importation du thème...';
      });

      final themeService = ref.read(_themeExportServiceProvider);
      final theme = await themeService.importThemeFromFile(filePath);
      await themeService.saveCustomTheme(theme);

      setState(() {
        _isLoading = false;
        _statusMessage = 'Thème ${theme.name} importé avec succès';
      });

      // Recharger les thèmes personnalisés
      await _loadCustomThemes();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Erreur lors de l\'importation du thème: $e';
        _isError = true;
      });
    }
  }

  /// Applique un thème personnalisé
  Future<void> _applyCustomTheme(ThemeExport theme) async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Application du thème ${theme.name}...';
      _isError = false;
    });

    try {
      final themeNotifier = ref.read(themeProvider.notifier);
      await themeNotifier.setCustomTheme(theme);

      setState(() {
        _isLoading = false;
        _statusMessage = 'Thème ${theme.name} appliqué avec succès';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Erreur lors de l\'application du thème: $e';
        _isError = true;
      });
    }
  }

  /// Supprime un thème personnalisé
  Future<void> _deleteCustomTheme(ThemeExport theme) async {
    // Demander confirmation
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Supprimer ${theme.name}?'),
            content: const Text(
              'Cette action est irréversible. Êtes-vous sûr de vouloir supprimer ce thème?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Suppression du thème ${theme.name}...';
      _isError = false;
    });

    try {
      final themeService = ref.read(_themeExportServiceProvider);
      await themeService.deleteCustomTheme(theme.name);

      // Si le thème supprimé est le thème actuel, revenir au thème par défaut
      final currentTheme = ref.read(themeProvider);
      if (currentTheme == AppThemeMode.custom) {
        final themeNotifier = ref.read(themeProvider.notifier);
        final customTheme = themeNotifier.customTheme;
        if (customTheme?.name == theme.name) {
          await themeNotifier.setTheme(AppThemeMode.unique);
        }
      }

      setState(() {
        _isLoading = false;
        _statusMessage = 'Thème ${theme.name} supprimé avec succès';
      });

      // Recharger les thèmes personnalisés
      await _loadCustomThemes();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Erreur lors de la suppression du thème: $e';
        _isError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentTheme = ref.watch(themeProvider);
    final themeNotifier = ref.read(themeProvider.notifier);
    final customTheme = themeNotifier.customTheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Gestionnaire de thèmes')),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(_statusMessage ?? 'Chargement...'),
                  ],
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_statusMessage != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color:
                              _isError
                                  ? Colors.red.withValues(
                                    alpha: 26,
                                  ) // 0.1 * 255 ≈ 26
                                  : Colors.green.withValues(
                                    alpha: 26,
                                  ), // 0.1 * 255 ≈ 26
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _isError ? Colors.red : Colors.green,
                          ),
                        ),
                        child: Text(
                          _statusMessage!,
                          style: TextStyle(
                            color: _isError ? Colors.red : Colors.green,
                          ),
                        ),
                      ),
                    const Text(
                      'Thèmes prédéfinis',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildPredefinedThemesSection(currentTheme),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Thèmes personnalisés',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        NeonButton(
                          text: 'Importer',
                          icon: FontAwesomeIcons.fileImport,
                          onPressed: _importThemeFromFile,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _customThemes.isEmpty
                        ? const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'Aucun thème personnalisé. Importez un thème pour commencer.',
                            textAlign: TextAlign.center,
                          ),
                        )
                        : Column(
                          children:
                              _customThemes
                                  .map(
                                    (theme) => _buildCustomThemeCard(
                                      theme,
                                      isActive:
                                          currentTheme == AppThemeMode.custom &&
                                          customTheme?.name == theme.name,
                                    ),
                                  )
                                  .toList(),
                        ),
                  ],
                ),
              ),
    );
  }

  Widget _buildPredefinedThemesSection(AppThemeMode currentTheme) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildPredefinedThemeCard(
          'Thème Unique',
          'unique',
          isActive: currentTheme == AppThemeMode.unique,
        ),
        _buildPredefinedThemeCard(
          'Thème TZ-1',
          'tz1',
          isActive: currentTheme == AppThemeMode.tz1,
        ),
        _buildPredefinedThemeCard(
          'Thème TZ-2',
          'tz2',
          isActive: currentTheme == AppThemeMode.tz2,
        ),
        _buildPredefinedThemeCard(
          'Creamy Beans',
          'creamyBeans',
          isActive: currentTheme == AppThemeMode.creamyBeans,
        ),
        _buildPredefinedThemeCard(
          'Orix Purple Split',
          'orixPurpleSplit',
          isActive: currentTheme == AppThemeMode.orixPurpleSplit,
        ),
      ],
    );
  }

  Widget _buildPredefinedThemeCard(
    String displayName,
    String themeName, {
    required bool isActive,
  }) {
    return NeonCard(
      width: 180,
      height: 120,
      isActive: isActive,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            displayName,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NeonButton(
                text: 'Appliquer',
                icon: FontAwesomeIcons.check,
                onPressed:
                    isActive
                        ? null
                        : () async {
                          final themeNotifier = ref.read(
                            themeProvider.notifier,
                          );
                          switch (themeName) {
                            case 'unique':
                              await themeNotifier.setTheme(AppThemeMode.unique);
                              break;
                            case 'tz1':
                              await themeNotifier.setTheme(AppThemeMode.tz1);
                              break;
                            case 'tz2':
                              await themeNotifier.setTheme(AppThemeMode.tz2);
                              break;
                            case 'creamyBeans':
                              await themeNotifier.setTheme(
                                AppThemeMode.creamyBeans,
                              );
                              break;
                            case 'orixPurpleSplit':
                              await themeNotifier.setTheme(
                                AppThemeMode.orixPurpleSplit,
                              );
                              break;
                          }
                        },
                size: NeonButtonSize.small,
              ),
              const SizedBox(width: 8),
              NeonButton(
                text: 'Exporter',
                icon: FontAwesomeIcons.fileExport,
                onPressed: () => _exportPredefinedTheme(themeName),
                size: NeonButtonSize.small,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomThemeCard(ThemeExport theme, {required bool isActive}) {
    return NeonCard(
      margin: const EdgeInsets.only(bottom: 8),
      isActive: isActive,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        theme.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      Text(
                        theme.description,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodyMedium?.color
                              ?.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  theme.isDark ? 'Sombre' : 'Clair',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyMedium?.color
                        ?.withValues(alpha: 179), // 0.7 * 255 ≈ 179
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Aperçu des couleurs
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.colors.primary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.colors.secondary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.colors.background,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.colors.surface,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const Spacer(),
                NeonButton(
                  text: isActive ? 'Actif' : 'Appliquer',
                  icon:
                      isActive ? FontAwesomeIcons.check : FontAwesomeIcons.play,
                  onPressed: isActive ? null : () => _applyCustomTheme(theme),
                  size: NeonButtonSize.small,
                ),
                const SizedBox(width: 8),
                NeonButton(
                  text: 'Exporter',
                  icon: FontAwesomeIcons.fileExport,
                  onPressed: () async {
                    final themeService = ref.read(_themeExportServiceProvider);
                    await themeService.shareTheme(theme);
                  },
                  size: NeonButtonSize.small,
                ),
                const SizedBox(width: 8),
                NeonButton(
                  text: 'Supprimer',
                  icon: FontAwesomeIcons.trash,
                  color: Colors.red,
                  onPressed: () => _deleteCustomTheme(theme),
                  size: NeonButtonSize.small,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
