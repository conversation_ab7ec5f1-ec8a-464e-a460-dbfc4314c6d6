Write-Host "Suppression des dossiers mipmap..."
$resPath = "android\app\src\main\res"

# Vérifier si les dossiers existent et les supprimer
$mipmapFolders = @(
    "mipmap-mdpi",
    "mipmap-hdpi",
    "mipmap-xhdpi",
    "mipmap-xxhdpi",
    "mipmap-xxxhdpi"
)

foreach ($folder in $mipmapFolders) {
    $folderPath = Join-Path -Path $resPath -ChildPath $folder
    if (Test-Path $folderPath) {
        Write-Host "Suppression de $folderPath"
        Remove-Item -Path $folderPath -Recurse -Force
    }
}

Write-Host "Dossiers supprimés avec succès."
