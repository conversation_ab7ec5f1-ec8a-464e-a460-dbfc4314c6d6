import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // Non utilisé
// import 'package:intl/intl.dart'; // Non utilisé
import '../../services/auth_service.dart';
import '../../providers/theme_provider.dart';
// import '../../services/invoice_service.dart'; // Service non disponible
import '../../services/customer/customer_service.dart';
import '../../services/task/task_service.dart';
import '../../services/messaging/messaging_service.dart';
// import '../../models/invoice.dart'; // Non utilisé

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);

    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    final cardGradient = appTheme.cardGradient;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec salutation
            Container(
              decoration: BoxDecoration(
                gradient: cardGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
                border: Border.all(
                  color: primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                  width: 1.5,
                ),
              ),
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bonjour, ${user?.name ?? 'Utilisateur'}!',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color:
                          ref.watch(isDarkThemeProvider)
                              ? Colors.white
                              : ref.watch(primaryTextColorProvider),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Département: ${user?.department ?? 'Non spécifié'}',
                    style: TextStyle(
                      fontSize: 16,
                      color:
                          ref.watch(isDarkThemeProvider)
                              ? Colors.white.withValues(alpha: 204)
                              : ref.watch(secondaryTextColorProvider),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(
                            alpha: 77,
                          ), // 0.3 * 255 ≈ 77
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: secondaryColor.withValues(
                              alpha: 128,
                            ), // 0.5 * 255 ≈ 128
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Rôle: ${user?.role ?? "Utilisateur"}',
                          style: TextStyle(
                            color: secondaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (user?.department != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(
                              alpha: 77,
                            ), // 0.3 * 255 ≈ 77
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: primaryColor.withValues(
                                alpha: 128,
                              ), // 0.5 * 255 ≈ 128
                              width: 1,
                            ),
                          ),
                          child: Text(
                            'Département: ${user?.department}',
                            style: TextStyle(
                              color: primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Statistiques
            Text(
              'Statistiques',
              style: TextStyle(
                color: secondaryColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: secondaryColor.withValues(alpha: 128),
                    blurRadius: 5,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, _) {
                final customersAsync = ref.watch(customersProvider);
                final tasksAsync = ref.watch(tasksProvider);
                final messagesAsync = ref.watch(messagesProvider);

                return GridView.count(
                  crossAxisCount:
                      4, // Augmentation du nombre de cartes par ligne
                  crossAxisSpacing: 8, // Réduction de l'espacement horizontal
                  mainAxisSpacing: 8, // Réduction de l'espacement vertical
                  shrinkWrap: true,
                  childAspectRatio:
                      1.0, // Ajustement du ratio pour des cartes plus petites
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildStatCard(
                      context,
                      title: 'Clients',
                      value: customersAsync.when(
                        data: (customers) => customers.length.toString(),
                        loading: () => '...',
                        error: (_, __) => '0',
                      ),
                      icon: Icons.people,
                      color: primaryColor,
                      onTap: () => context.go('/customers'),
                    ),
                    _buildStatCard(
                      context,
                      title: 'Tâches',
                      value: tasksAsync.length.toString(),
                      icon: Icons.check_circle,
                      color: secondaryColor,
                      onTap: () => context.go('/tasks'),
                    ),
                    _buildStatCard(
                      context,
                      title: 'Messages',
                      value: messagesAsync.length.toString(),
                      icon: Icons.message,
                      color: primaryColor,
                      onTap: () => context.go('/messaging'),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 24),

            // Accès Rapides
            Text(
              'Activités récentes',
              style: TextStyle(
                color: secondaryColor,
                fontSize: 20,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: secondaryColor.withValues(alpha: 128),
                    blurRadius: 5,
                    offset: const Offset(0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                gradient: cardGradient,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: secondaryColor.withValues(
                      alpha: 77,
                    ), // 0.3 * 255 ≈ 77
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
                border: Border.all(
                  color: secondaryColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                  width: 1,
                ),
              ),
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 5,
                separatorBuilder:
                    (context, index) => Divider(
                      color: Colors.white.withValues(
                        alpha: 26,
                      ), // 0.1 * 255 ≈ 26
                      height: 1,
                      indent: 70,
                      endIndent: 16,
                    ),
                itemBuilder: (context, index) {
                  final colors = [
                    primaryColor,
                    secondaryColor,
                    Colors.green,
                    Colors.cyan,
                    Colors.pink,
                  ];
                  final icons = [
                    Icons.person_add,
                    Icons.edit_document,
                    Icons.call_made,
                    Icons.email,
                    Icons.task_alt,
                  ];
                  final titles = [
                    'Nouveau client ajouté',
                    'Contrat mis à jour',
                    'Appel effectué',
                    'Email envoyé',
                    'Tâche terminée',
                  ];
                  final descriptions = [
                    'Entreprise ABC a été ajoutée',
                    'Contrat #1234 a été mis à jour',
                    'Appel avec Jean Dupont',
                    'Email envoyé à Marie Martin',
                    'Préparation de la présentation',
                  ];

                  return ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 77,
                        ), // 0.3 * 255 ≈ 77
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: colors[index % colors.length].withValues(
                            alpha: 128,
                          ), // 0.5 * 255 ≈ 128
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: colors[index % colors.length].withValues(
                              alpha: 77,
                            ), // 0.3 * 255 ≈ 77
                            blurRadius: 8,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: Icon(
                        icons[index % icons.length],
                        color: colors[index % colors.length],
                        size: 20,
                      ),
                    ),
                    title: Text(
                      titles[index % titles.length],
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      descriptions[index % descriptions.length],
                      style: TextStyle(
                        color: Colors.white.withValues(
                          alpha: 179,
                        ), // 0.7 * 255 ≈ 179
                      ),
                    ),
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 77,
                        ), // 0.3 * 255 ≈ 77
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Il y a ${index + 1}h',
                        style: TextStyle(
                          color: colors[index % colors.length],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Méthode _buildRecentInvoices supprimée car non utilisée

  // Méthode _buildQuickAccessCard supprimée car non utilisée

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20, // Taille de la valeur augmentée
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12, // Taille du titre réduite
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
