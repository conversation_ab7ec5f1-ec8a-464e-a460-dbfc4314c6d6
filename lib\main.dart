import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:go_router/go_router.dart';
import 'router/app_router.dart';
import 'services/auth_service.dart';
import 'providers/theme_provider.dart';
import 'services/sync_service.dart';
import 'services/supabase_service.dart';
import 'services/data_migration/data_migration_service.dart';

void main() async {
  // Capture les erreurs Flutter non gérées
  FlutterError.onError = (FlutterErrorDetails details) {
    FlutterError.presentError(details);
    debugPrint('FlutterError: ${details.exception}');
    // Vous pourriez ajouter ici un service de rapport d'erreurs comme Sentry ou Firebase Crashlytics
  };

  // Capture les erreurs Dart non gérées
  PlatformDispatcher.instance.onError = (error, stack) {
    debugPrint('Erreur non gérée: $error');
    debugPrint('Stack trace: $stack');
    // Vous pourriez ajouter ici un service de rapport d'erreurs
    return true;
  };

  WidgetsFlutterBinding.ensureInitialized();

  // Chargement des variables d'environnement
  await dotenv.load(fileName: ".env");

  // Initialisation des services
  final container = ProviderContainer();

  try {
    // Initialiser Supabase avec gestion des erreurs
    await container.read(supabaseServiceProvider).initialize();
    debugPrint('Supabase initialisé avec succès');
  } catch (e) {
    debugPrint('Erreur lors de l\'initialisation de Supabase: $e');
    // Continuer l'exécution même en cas d'erreur Supabase
    // L'application fonctionnera en mode hors ligne
  }

  // Initialiser les autres services
  await container.read(authServiceProvider).initialize();
  await container.read(dataMigrationServiceProvider).checkAndMigrate();

  runApp(UncontrolledProviderScope(container: container, child: const MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appTheme = ref.watch(themeProvider);
    final router = ref.watch(routerProvider);
    final syncService = ref.watch(syncServiceProvider);

    // Démarrer la synchronisation en arrière-plan
    syncService.startPeriodicSync();

    // Synchronisation immédiate au démarrage pour récupérer les données des autres appareils
    WidgetsBinding.instance.addPostFrameCallback((_) {
      syncService.syncAll().catchError((error) {
        debugPrint('Erreur de synchronisation au démarrage: $error');
      });
    });

    return MaterialApp.router(
      title: 'CRM Supabase',
      debugShowCheckedModeBanner: false, // Supprime la bannière de débogage
      theme: appTheme.themeData,
      routerConfig: router,
      // Configuration pour la réactivité sur différents appareils
      builder: (context, child) {
        // Obtenir la taille de l'écran
        final mediaQuery = MediaQuery.of(context);
        const textScaler = TextScaler.linear(
          1.0,
        ); // Utiliser TextScaler au lieu de textScaleFactor

        return MediaQuery(
          data: mediaQuery.copyWith(textScaler: textScaler),
          child: child!,
        );
      },
    );
  }
}

// La classe SplashScreen a été déplacée vers lib/screens/splash/splash_screen.dart

class ErrorScreen extends StatelessWidget {
  final String error;

  const ErrorScreen({required this.error, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 100),
            const SizedBox(height: 20),
            Text(
              'Erreur : $error',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }
}
