enum AIProviderType { openai, anthropic, gemini, mistral, groq, deepseek }

extension AIProviderTypeExtension on AIProviderType {
  String get displayName {
    switch (this) {
      case AIProviderType.openai:
        return 'OpenAI';
      case AIProviderType.anthropic:
        return 'Anthropic';
      case AIProviderType.gemini:
        return 'Google Gemini';
      case AIProviderType.mistral:
        return 'Mistral AI';
      case AIProviderType.groq:
        return 'Groq';
      case AIProviderType.deepseek:
        return 'DeepSeek';
    }
  }

  List<String> get availableModels {
    switch (this) {
      case AIProviderType.openai:
        return ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o'];
      case AIProviderType.anthropic:
        return [
          'claude-3-opus-20240229',
          'claude-3-sonnet-20240229',
          'claude-3-haiku-20240307',
          'claude-2.1',
          'claude-2.0',
        ];
      case AIProviderType.gemini:
        return ['gemini-pro', 'gemini-1.5-pro', 'gemini-1.5-flash'];
      case AIProviderType.mistral:
        return [
          'mistral-tiny',
          'mistral-small',
          'mistral-medium',
          'mistral-large-latest',
        ];
      case AIProviderType.groq:
        return ['llama2-70b-4096', 'mixtral-8x7b-32768', 'gemma-7b-it'];
      case AIProviderType.deepseek:
        return ['deepseek-chat', 'deepseek-coder'];
    }
  }

  String get defaultModel {
    switch (this) {
      case AIProviderType.openai:
        return 'gpt-3.5-turbo';
      case AIProviderType.anthropic:
        return 'claude-3-sonnet-20240229';
      case AIProviderType.gemini:
        return 'gemini-pro';
      case AIProviderType.mistral:
        return 'mistral-medium';
      case AIProviderType.groq:
        return 'mixtral-8x7b-32768';
      case AIProviderType.deepseek:
        return 'deepseek-chat';
    }
  }

  bool get requiresOrganizationId {
    return this == AIProviderType.openai;
  }
}
