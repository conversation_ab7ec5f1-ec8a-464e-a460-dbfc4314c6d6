import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/knowledge_article.dart';

// Provider pour le service de base de connaissances
final knowledgeServiceProvider = Provider<KnowledgeService>((ref) {
  return KnowledgeService();
});

// Provider pour la liste des articles
final knowledgeArticlesProvider = FutureProvider<List<KnowledgeArticle>>((
  ref,
) async {
  final service = ref.watch(knowledgeServiceProvider);
  return service.getArticles();
});

// Provider pour les catégories disponibles
final knowledgeCategoriesProvider = Provider<List<String>>((ref) {
  return [
    'Tous',
    'Commandes',
    'Livraison',
    'Produits',
    'Service client',
    'Tarifs',
  ];
});

class KnowledgeService {
  // Liste en mémoire des articles (simulant une base de données)
  final List<KnowledgeArticle> _articles = [
    KnowledgeArticle(
      id: '1',
      title: 'Comment passer une commande',
      content:
          'Pour passer une commande, suivez ces étapes simples:\n\n'
          '1. Sélectionnez les produits que vous souhaitez...\n'
          '2. Ajoutez-les à votre panier\n'
          '3. Procédez au paiement\n\n'
          'Vous recevrez une confirmation par email une fois la commande validée.',
      category: 'Commandes',
      tags: ['commande', 'achat', 'paiement'],
      lastUpdated: DateTime(2023, 10, 15),
    ),
    KnowledgeArticle(
      id: '2',
      title: 'Délais de livraison',
      content:
          'Nos délais de livraison varient selon le type de produit et votre localisation:\n\n'
          '- Produits standard: 3-5 jours ouvrés\n'
          '- Produits personnalisés: 7-10 jours ouvrés\n'
          '- Livraison express: 24-48h (supplément)\n\n'
          'Les délais peuvent être plus longs pour les zones rurales ou insulaires.',
      category: 'Livraison',
      tags: ['livraison', 'délai', 'expédition'],
      lastUpdated: DateTime(2023, 11, 5),
    ),
    KnowledgeArticle(
      id: '3',
      title: 'Options de personnalisation',
      content:
          'Nous proposons plusieurs options de personnalisation pour nos produits:\n\n'
          '- Impression de logo (plusieurs couleurs disponibles)\n'
          '- Choix de matériaux spécifiques\n'
          '- Gravure personnalisée\n'
          '- Emballage cadeau\n\n'
          'Contactez notre service client pour plus d\'informations sur les tarifs.',
      category: 'Produits',
      tags: ['personnalisation', 'sur mesure', 'options'],
      lastUpdated: DateTime(2023, 12, 1),
    ),
    KnowledgeArticle(
      id: '4',
      title: 'Politique de retour',
      content:
          'Notre politique de retour est la suivante:\n\n'
          '- 14 jours pour retourner un produit non utilisé\n'
          '- Remboursement intégral (hors frais de port)\n'
          '- Échange possible\n\n'
          'Les produits personnalisés ne sont pas éligibles aux retours sauf défaut de fabrication.',
      category: 'Service client',
      tags: ['retour', 'remboursement', 'échange'],
      lastUpdated: DateTime(2023, 9, 20),
    ),
    KnowledgeArticle(
      id: '5',
      title: 'Grille tarifaire',
      content:
          'Nos tarifs sont structurés comme suit:\n\n'
          '- Tarif standard: prix catalogue\n'
          '- Tarif revendeur: -15% (minimum 10 pièces)\n'
          '- Tarif grossiste: -25% (minimum 50 pièces)\n\n'
          'Des remises supplémentaires peuvent s\'appliquer pour les commandes régulières.',
      category: 'Tarifs',
      tags: ['prix', 'remise', 'grossiste'],
      lastUpdated: DateTime(2023, 10, 10),
    ),
  ];

  // Méthode pour récupérer tous les articles
  Future<List<KnowledgeArticle>> getArticles() async {
    // Simuler un délai de chargement
    await Future.delayed(const Duration(milliseconds: 800));

    // Retourner une copie de la liste pour éviter les modifications directes
    return [..._articles];
  }

  // Méthode pour récupérer un article par son ID
  Future<KnowledgeArticle?> getArticleById(String id) async {
    try {
      return _articles.firstWhere((article) => article.id == id);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour ajouter un nouvel article
  Future<void> addArticle(KnowledgeArticle article) async {
    // Vérifier si l'ID existe déjà
    if (_articles.any((a) => a.id == article.id)) {
      throw Exception('Un article avec cet ID existe déjà');
    }

    _articles.add(article);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour mettre à jour un article existant
  Future<void> updateArticle(KnowledgeArticle article) async {
    final index = _articles.indexWhere((a) => a.id == article.id);
    if (index == -1) {
      throw Exception('Article non trouvé');
    }

    _articles[index] = article;
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour supprimer un article
  Future<void> deleteArticle(String id) async {
    final index = _articles.indexWhere((a) => a.id == id);
    if (index == -1) {
      throw Exception('Article non trouvé');
    }

    _articles.removeAt(index);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour rechercher des articles
  Future<List<KnowledgeArticle>> searchArticles(String query) async {
    final lowercaseQuery = query.toLowerCase();

    return _articles.where((article) {
      return article.title.toLowerCase().contains(lowercaseQuery) ||
          article.content.toLowerCase().contains(lowercaseQuery) ||
          article.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  // Méthode pour obtenir les articles par catégorie
  Future<List<KnowledgeArticle>> getArticlesByCategory(String category) async {
    if (category == 'Tous') {
      return getArticles();
    }

    return _articles.where((article) => article.category == category).toList();
  }
}
