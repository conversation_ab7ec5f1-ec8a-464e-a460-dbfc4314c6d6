import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/channel_config.dart';
import '../../services/supabase_service.dart';

final whatsAppServiceProvider = Provider<WhatsAppService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return WhatsAppService(supabaseService: supabaseService);
});

class WhatsAppService {
  final SupabaseService _supabaseService;
  bool _isInitialized = false;
  late ChannelConfig _config;
  final String _twilioBaseUrl = 'https://api.twilio.com/2010-04-01';

  WhatsAppService({required SupabaseService supabaseService})
    : _supabaseService = supabaseService;

  Future<void> initialize([ChannelConfig? config]) async {
    if (config != null) {
      _config = config;
      _isInitialized = true;
      return;
    }

    try {
      // Récupérer la configuration depuis Supabase
      final response =
          await _supabaseService.client
              .from('channel_configs')
              .select()
              .eq('channel_type', 'whatsapp')
              .single();

      // La méthode single() lève une exception si aucun résultat n'est trouvé
      _config = ChannelConfig(
        channel: MessageChannel.whatsapp,
        isEnabled: response['enabled'] as bool? ?? false,
        accountId: response['account_id'] as String,
        apiKey: response['api_key'] as String,
        phoneNumber: response['phone_number'] as String,
      );
      _isInitialized = true;
      debugPrint('Configuration WhatsApp chargée depuis Supabase');
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du service WhatsApp: $e');
      rethrow;
    }
  }

  // Méthode pour envoyer un message WhatsApp
  Future<Message> sendMessage({
    required String to,
    required String content,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final accountSid = _config.accountId;
      final authToken = _config.apiKey;
      final fromNumber = _config.phoneNumber;
      final toNumber = to.startsWith('whatsapp:') ? to : 'whatsapp:$to';

      final endpoint = '$_twilioBaseUrl/Accounts/$accountSid/Messages.json';

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Authorization':
              'Basic ${base64Encode(utf8.encode('$accountSid:$authToken'))}',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {'From': fromNumber, 'To': toNumber, 'Body': content},
      );

      if (response.statusCode < 200 || response.statusCode >= 300) {
        throw Exception(
          'Échec de l\'envoi du message WhatsApp: ${response.body}',
        );
      }

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      // Créer un objet Message
      final message = Message(
        id: responseData['sid'] as String,
        contactId: to.replaceAll('whatsapp:', ''),
        content: content,
        isUser: true,
        timestamp: DateTime.now(),
        channel: MessageChannel.whatsapp,
        status: MessageStatus.sending,
      );

      // Enregistrer le message dans Supabase
      await _saveMessageToSupabase(message);

      return message;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message WhatsApp: $e');
      rethrow;
    }
  }

  // Méthode pour envoyer un message basé sur un modèle
  Future<Message> sendTemplateMessage({
    required String to,
    required String templateName,
    required Map<String, String> variables,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final accountSid = _config.accountId;
      final authToken = _config.apiKey;
      final fromNumber = _config.phoneNumber;
      final toNumber = to.startsWith('whatsapp:') ? to : 'whatsapp:$to';

      // Construire le contenu du modèle
      final content = _buildTemplateContent(templateName, variables);

      final endpoint = '$_twilioBaseUrl/Accounts/$accountSid/Messages.json';

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Authorization':
              'Basic ${base64Encode(utf8.encode('$accountSid:$authToken'))}',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {'From': fromNumber, 'To': toNumber, 'Body': content},
      );

      if (response.statusCode < 200 || response.statusCode >= 300) {
        throw Exception(
          'Échec de l\'envoi du message WhatsApp: ${response.body}',
        );
      }

      final responseData = jsonDecode(response.body) as Map<String, dynamic>;

      // Créer un objet Message
      final message = Message(
        id: responseData['sid'] as String,
        contactId: to.replaceAll('whatsapp:', ''),
        content: content,
        isUser: true,
        timestamp: DateTime.now(),
        channel: MessageChannel.whatsapp,
        status: MessageStatus.sending,
        metadata: {
          'template': templateName,
          'variables': jsonEncode(variables),
        },
      );

      // Enregistrer le message dans Supabase
      await _saveMessageToSupabase(message);

      return message;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message WhatsApp avec modèle: $e');
      rethrow;
    }
  }

  // Méthode pour enregistrer un message dans Supabase
  Future<void> _saveMessageToSupabase(Message message) async {
    try {
      await _supabaseService.client.from('messages').insert({
        'id': message.id,
        'contact_id': message.contactId,
        'content': message.content,
        'is_user': message.isUser,
        'timestamp': message.timestamp.toIso8601String(),
        'channel': message.channel.toString().split('.').last,
        'status': message.status.toString().split('.').last,
        'metadata': message.metadata,
      });

      debugPrint('Message enregistré dans Supabase: ${message.id}');
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'enregistrement du message dans Supabase: $e',
      );
    }
  }

  // Méthode pour construire le contenu d'un modèle
  String _buildTemplateContent(
    String templateName,
    Map<String, String> variables,
  ) {
    // Implémentez votre logique de modèle ici
    // Exemple simple:
    switch (templateName) {
      case 'appointment_reminder':
        return 'Votre rendez-vous est prévu le ${variables['date']} à ${variables['time']}.';
      case 'payment_confirmation':
        return 'Nous avons reçu votre paiement de ${variables['amount']} FCFA. Merci!';
      default:
        return 'Message de $templateName';
    }
  }

  // Méthode pour traiter les messages entrants (appelée par votre Edge Function Supabase)
  Future<Message> handleIncomingMessage(Map<String, dynamic> payload) async {
    try {
      // Extraire les informations du message
      final from = payload['From'] as String? ?? '';
      final body = payload['Body'] as String? ?? '';
      final messageSid = payload['MessageSid'] as String? ?? '';

      // Nettoyer le numéro de téléphone
      final contactId = from.replaceAll('whatsapp:', '');

      // Créer un objet Message
      final message = Message(
        id: messageSid,
        contactId: contactId,
        content: body,
        isUser: false,
        timestamp: DateTime.now(),
        channel: MessageChannel.whatsapp,
        status: MessageStatus.delivered,
      );

      // Enregistrer le message dans Supabase
      await _saveMessageToSupabase(message);

      debugPrint('Message WhatsApp entrant traité: $messageSid');
      return message;
    } catch (e) {
      debugPrint('Erreur lors du traitement du message WhatsApp entrant: $e');
      rethrow;
    }
  }
}
