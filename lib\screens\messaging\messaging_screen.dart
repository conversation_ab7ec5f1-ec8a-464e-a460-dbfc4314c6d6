import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:uuid/uuid.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/contact.dart';
import '../../models/customer/customer.dart';
import '../../services/messaging/messaging_service.dart';
import '../../services/customer/customer_service.dart';
import '../../providers/theme_provider.dart';
import 'widgets/contact_list.dart';
import 'widgets/message_list.dart';
import 'widgets/channel_tabs.dart';
import 'widgets/platform_selector.dart';

class MessagingScreen extends ConsumerStatefulWidget {
  const MessagingScreen({super.key});

  @override
  ConsumerState<MessagingScreen> createState() => _MessagingScreenState();
}

class _MessagingScreenState extends ConsumerState<MessagingScreen> {
  final TextEditingController _messageController = TextEditingController();
  bool _isLoading = true;
  bool _isUsingAI = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Charger les contacts
      await ref.read(contactsProvider.notifier).loadContacts();

      // Charger les messages
      await ref.read(messagesProvider.notifier).loadMessages();

      // Charger les configurations des canaux
      await ref.read(channelConfigsProvider.notifier).loadChannelConfigs();

      // Initialiser le service de messagerie
      final configs = ref.read(channelConfigsProvider);
      await ref.read(messagingServiceProvider).initialize(configs);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedContact = ref.watch(selectedContactProvider);
    final selectedChannel = ref.watch(selectedChannelProvider);
    final appTheme = ref.watch(themeProvider);

    return Scaffold(
      floatingActionButton: _buildNewConversationButton(),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Container(
                decoration: BoxDecoration(gradient: appTheme.mainGradient),
                child: Column(
                  children: [
                    // Sélecteur de plateforme en haut de l'écran
                    const PlatformSelector(),

                    // Contenu principal
                    Expanded(
                      child: Row(
                        children: [
                          // Liste des contacts
                          SizedBox(
                            width: 300,
                            child: Column(
                              children: [
                                // En-tête "Contacts"
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF1A1A2E),
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Colors.white.withAlpha(
                                          26,
                                        ), // 0.1 * 255 ≈ 26
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  child: Consumer(
                                    builder: (context, ref, _) {
                                      final appTheme = ref.watch(themeProvider);
                                      final primaryColor =
                                          appTheme.primaryColor;

                                      return Row(
                                        children: [
                                          Text(
                                            'Contacts',
                                            style: TextStyle(
                                              color: primaryColor,
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Icon(
                                            Icons.people,
                                            color: primaryColor,
                                            size: 18,
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                ),

                                // Liste des contacts
                                Expanded(
                                  child: ContactList(
                                    onContactSelected: (contact) {
                                      ref
                                          .read(
                                            selectedContactProvider.notifier,
                                          )
                                          .state = contact;

                                      // Marquer les messages comme lus
                                      if (selectedChannel != null) {
                                        ref
                                            .read(messagesProvider.notifier)
                                            .markAsRead(
                                              contact.id,
                                              selectedChannel,
                                            );
                                      } else {
                                        ref
                                            .read(messagesProvider.notifier)
                                            .markAsRead(contact.id);
                                      }

                                      // Mettre à jour le compteur de messages non lus
                                      ref
                                          .read(contactsProvider.notifier)
                                          .updateUnreadCount(contact.id, 0);
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Séparateur vertical
                          Container(
                            width: 1,
                            color: Colors.white.withAlpha(51), // 0.2 * 255 ≈ 51
                          ),

                          // Zone de conversation
                          Expanded(
                            child:
                                selectedContact == null
                                    ? _buildEmptyConversationState()
                                    : Column(
                                      children: [
                                        // En-tête avec les informations du contact
                                        _buildContactHeader(selectedContact),

                                        // Onglets des canaux
                                        ChannelTabs(
                                          contact: selectedContact,
                                          onChannelSelected: (channel) {
                                            ref
                                                .read(
                                                  selectedChannelProvider
                                                      .notifier,
                                                )
                                                .state = channel;

                                            // Marquer les messages comme lus
                                            ref
                                                .read(messagesProvider.notifier)
                                                .markAsRead(
                                                  selectedContact.id,
                                                  channel,
                                                );
                                          },
                                        ),

                                        // Liste des messages
                                        Expanded(
                                          child: MessageList(
                                            contactId: selectedContact.id,
                                            channel: selectedChannel,
                                          ),
                                        ),

                                        // Zone de saisie du message
                                        _buildMessageInput(
                                          selectedContact,
                                          selectedChannel,
                                        ),
                                      ],
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildEmptyConversationState() {
    return Consumer(
      builder: (context, ref, _) {
        final appTheme = ref.watch(themeProvider);
        final primaryColor = appTheme.primaryColor;

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 80,
                color: primaryColor.withAlpha(150),
              ),
              const SizedBox(height: 20),
              const Text(
                'Sélectionnez une conversation',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'Choisissez un contact pour commencer à discuter',
                style: TextStyle(color: Colors.white70, fontSize: 16),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContactHeader(Contact contact) {
    return Consumer(
      builder: (context, ref, _) {
        final appTheme = ref.watch(themeProvider);
        final primaryColor = appTheme.primaryColor;
        final secondaryColor = appTheme.secondaryColor;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A2E),
            border: Border(
              bottom: BorderSide(color: Color(0xFF2A2A3E), width: 1),
            ),
          ),
          child: Row(
            children: [
              // Avatar du contact
              CircleAvatar(
                radius: 24,
                backgroundColor: _getContactColor(contact),
                backgroundImage:
                    contact.avatarUrl != null
                        ? NetworkImage(contact.avatarUrl!)
                        : null,
                child:
                    contact.avatarUrl == null
                        ? Text(
                          contact.name.isNotEmpty
                              ? contact.name[0].toUpperCase()
                              : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 16),

              // Informations du contact
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contact.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color:
                                contact.isOnline ? Colors.green : Colors.grey,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          contact.isOnline ? 'En ligne' : 'Hors ligne',
                          style: TextStyle(
                            color: Colors.white.withAlpha(
                              179,
                            ), // 0.7 * 255 ≈ 179
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Boutons d'action
              Row(
                children: [
                  IconButton(
                    icon: Icon(Icons.phone_outlined, color: primaryColor),
                    onPressed: () {
                      // Appeler le contact
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.videocam_outlined, color: primaryColor),
                    onPressed: () {
                      // Appel vidéo avec le contact
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.person_add, color: secondaryColor),
                    tooltip: 'Créer un client à partir de cette conversation',
                    onPressed: () {
                      _createCustomerFromConversation(contact);
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.more_vert, color: primaryColor),
                    onPressed: () {
                      // Afficher plus d'options
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getContactColor(Contact contact) {
    // Récupérer le thème actuel
    final appTheme = ref.read(themeProvider);
    final primaryColor = appTheme.primaryColor;

    // Déterminer la couleur en fonction du canal principal du contact
    if (contact.channels[MessageChannel.whatsapp] == true) {
      return Colors.green;
    } else if (contact.channels[MessageChannel.facebook] == true) {
      return Colors.blue;
    } else if (contact.channels[MessageChannel.sms] == true) {
      return Colors.orange;
    } else if (contact.channels[MessageChannel.email] == true) {
      return Colors.red;
    } else {
      return primaryColor;
    }
  }

  Widget _buildMessageInput(Contact contact, MessageChannel? channel) {
    // Si aucun canal n'est sélectionné, utiliser le canal par défaut
    final effectiveChannel = channel ?? MessageChannel.app;

    // Déterminer la couleur en fonction du canal
    Color channelColor = _getChannelColor(effectiveChannel);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A2E),
        border: Border(top: BorderSide(color: Color(0xFF2A2A3E), width: 1)),
      ),
      child: Column(
        children: [
          // Bascule IA/Humain
          Consumer(
            builder: (context, ref, _) {
              final appTheme = ref.watch(themeProvider);
              final primaryColor = appTheme.primaryColor;
              final secondaryColor = appTheme.secondaryColor;

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF0F0F1E),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: const Color(0xFF2A2A3E), width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildModeToggleButton(
                      icon: FontAwesomeIcons.user,
                      label: 'Humain',
                      isSelected: !_isUsingAI,
                      color: primaryColor,
                      onTap: () {
                        setState(() {
                          _isUsingAI = false;
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    _buildModeToggleButton(
                      icon: FontAwesomeIcons.robot,
                      label: 'IA',
                      isSelected: _isUsingAI,
                      color: secondaryColor,
                      onTap: () {
                        setState(() {
                          _isUsingAI = true;
                        });
                      },
                    ),
                  ],
                ),
              );
            },
          ),

          // Zone de saisie du message
          Row(
            children: [
              // Boutons pour les actions
              IconButton(
                icon: Icon(Icons.emoji_emotions_outlined, color: channelColor),
                onPressed: () {
                  // Afficher les emojis
                },
              ),
              IconButton(
                icon: Icon(Icons.attach_file, color: channelColor),
                onPressed: () {
                  // Afficher les options de pièces jointes
                },
              ),

              // Champ de saisie du message
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF0F0F1E),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: const Color(0xFF2A2A3E),
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: _messageController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Votre message...',
                      hintStyle: TextStyle(color: Colors.white.withAlpha(128)),
                      border: InputBorder.none,
                    ),
                    maxLines: 3,
                    minLines: 1,
                  ),
                ),
              ),

              // Bouton d'envoi
              IconButton(
                icon: Icon(Icons.send, color: channelColor),
                onPressed: () async {
                  if (_messageController.text.trim().isEmpty) return;

                  final content = _messageController.text.trim();
                  _messageController.clear();

                  // Envoyer le message
                  final messagingService = ref.read(messagingServiceProvider);
                  final message = await messagingService.sendMessage(
                    contactId: contact.id,
                    content: content,
                    channel: effectiveChannel,
                  );

                  // Ajouter le message à la liste
                  ref.read(messagesProvider.notifier).addMessage(message);

                  // Si le mode IA est activé, générer une réponse
                  if (_isUsingAI) {
                    final aiResponse = await messagingService
                        .generateAIResponse(
                          contactId: contact.id,
                          userMessage: content,
                          channel: effectiveChannel,
                        );

                    // Ajouter la réponse de l'IA à la liste
                    ref.read(messagesProvider.notifier).addMessage(aiResponse);
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModeToggleButton({
    required IconData icon,
    required String label,
    required bool isSelected,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(50) : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.white.withAlpha(150),
              size: 14,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.white.withAlpha(150),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getChannelColor(MessageChannel channel) {
    // Récupérer le thème actuel
    final appTheme = ref.read(themeProvider);
    final primaryColor = appTheme.primaryColor;

    switch (channel) {
      case MessageChannel.whatsapp:
        return Colors.green;
      case MessageChannel.facebook:
        return Colors.blue;
      case MessageChannel.sms:
        return Colors.orange;
      case MessageChannel.email:
        return Colors.red;
      case MessageChannel.app:
        return primaryColor;
      case MessageChannel.instagram:
        return Colors.purple;
      case MessageChannel.internal:
        return Colors.grey;
      case MessageChannel.telegram:
        return Colors.lightBlue;
    }
  }

  Future<void> _createCustomerFromConversation(Contact contact) async {
    if (!mounted) return;

    // Récupérer les messages de la conversation
    final messagesAsync = ref.read(messagesProvider);

    // Filtrer les messages du contact
    final messages =
        messagesAsync
            .where((message) => message.contactId == contact.id)
            .toList();

    // Concaténer les messages de l'utilisateur pour analyse
    final userMessages = messages
        .where((message) => message.isUser)
        .map((message) => message.content)
        .join('\n\n');

    if (userMessages.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Aucun message trouvé pour créer un client'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Afficher un dialogue de chargement
    if (!mounted) return;

    // Récupérer le thème actuel
    final appTheme = ref.read(themeProvider);
    final primaryColor = appTheme.primaryColor;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: Colors.black87,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: primaryColor),
                const SizedBox(height: 16),
                const Text(
                  'Analyse des messages en cours...',
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
    );

    try {
      // Simuler la création d'un client
      // Dans une implémentation réelle, vous utiliseriez un service d'IA pour extraire les informations
      await Future.delayed(const Duration(seconds: 2));

      // Créer un client fictif pour la démonstration
      final customer = Customer(
        id: const Uuid().v4(),
        name: contact.name,
        phone: contact.phoneNumber,
        email: contact.email,
        source: _getChannelName(contact.channels.entries.first.key),
        createdAt: DateTime.now(),
        lastContact: DateTime.now(),
        status: CustomerStatus.lead,
        leadQualification: LeadQualification.warm,
        notes: 'Client créé à partir de la conversation',
        tags: ['chatbot', 'auto-créé'],
      );

      // Ajouter le client à la base de données
      await ref.read(customerServiceProvider).addCustomer(customer);

      // Fermer le dialogue de chargement
      if (mounted) Navigator.of(context).pop();

      // Afficher un message de succès
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Client "${customer.name}" créé avec succès !'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Voir',
              textColor: Colors.white,
              onPressed: () {
                context.go('/customers/${customer.id}');
              },
            ),
          ),
        );
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      if (mounted) Navigator.of(context).pop();

      // Afficher un message d'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _getChannelName(MessageChannel channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return 'WhatsApp';
      case MessageChannel.facebook:
        return 'Facebook';
      case MessageChannel.sms:
        return 'SMS';
      case MessageChannel.email:
        return 'Email';
      case MessageChannel.app:
        return 'Application';
      case MessageChannel.instagram:
        return 'Instagram';
      case MessageChannel.internal:
        return 'Interne';
      case MessageChannel.telegram:
        return 'Telegram';
    }
  }

  Widget _buildNewConversationButton() {
    // Récupérer le thème actuel
    final appTheme = ref.read(themeProvider);
    final primaryColor = appTheme.primaryColor;

    return FloatingActionButton(
      onPressed: () {
        _showNewConversationDialog();
      },
      backgroundColor: primaryColor,
      child: const Icon(Icons.add, color: Colors.black),
    );
  }

  void _showNewConversationDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1A1A2E),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Nouvelle conversation',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white70),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: _buildChannelButton(
                      icon: FontAwesomeIcons.whatsapp,
                      label: 'WhatsApp',
                      color: Colors.green,
                      onTap: () {
                        Navigator.pop(context);
                        _startNewConversation(MessageChannel.whatsapp);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildChannelButton(
                      icon: FontAwesomeIcons.telegram,
                      label: 'Telegram',
                      color: Colors.lightBlue,
                      onTap: () {
                        Navigator.pop(context);
                        _startNewConversation(MessageChannel.telegram);
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: _buildChannelButton(
                      icon: FontAwesomeIcons.commentSms,
                      label: 'SMS',
                      color: Colors.orange,
                      onTap: () {
                        Navigator.pop(context);
                        context.push('/settings/sms/send');
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildChannelButton(
                      icon: FontAwesomeIcons.envelope,
                      label: 'Email',
                      color: Colors.red,
                      onTap: () {
                        Navigator.pop(context);
                        _startNewConversation(MessageChannel.email);
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChannelButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: color.withAlpha(25), // 0.1 * 255 ≈ 25
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withAlpha(77)), // 0.3 * 255 ≈ 77
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _startNewConversation(MessageChannel channel) {
    // Implémenter la logique pour démarrer une nouvelle conversation
    // selon le canal choisi
    switch (channel) {
      case MessageChannel.whatsapp:
        _launchWhatsApp();
        break;
      case MessageChannel.telegram:
        _launchTelegram();
        break;
      case MessageChannel.email:
        _launchEmail();
        break;
      default:
        break;
    }
  }

  void _launchWhatsApp() {
    // Afficher un dialogue pour entrer le numéro de téléphone
    showDialog(
      context: context,
      builder: (context) {
        final TextEditingController phoneController = TextEditingController();
        final TextEditingController messageController = TextEditingController();

        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Démarrer une conversation WhatsApp',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: phoneController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Numéro de téléphone',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.green),
                  ),
                ),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: messageController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Message (optionnel)',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.green),
                  ),
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                final phone = phoneController.text.trim();
                final message = messageController.text.trim();

                if (phone.isNotEmpty) {
                  // Formater le numéro de téléphone (supprimer les espaces et les caractères spéciaux)
                  final formattedPhone = phone.replaceAll(
                    RegExp(r'[^0-9+]'),
                    '',
                  );

                  // Construire l'URL WhatsApp
                  final whatsappUrl = 'https://wa.me/$formattedPhone';

                  // Ajouter le message s'il est présent
                  final url =
                      message.isNotEmpty
                          ? '$whatsappUrl?text=${Uri.encodeComponent(message)}'
                          : whatsappUrl;

                  // Ouvrir WhatsApp
                  launchUrl(
                    Uri.parse(url),
                    mode: LaunchMode.externalApplication,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Démarrer'),
            ),
          ],
        );
      },
    );
  }

  void _launchTelegram() {
    // Afficher un dialogue pour entrer le nom d'utilisateur Telegram
    showDialog(
      context: context,
      builder: (context) {
        final TextEditingController usernameController =
            TextEditingController();

        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Démarrer une conversation Telegram',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: usernameController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Nom d\'utilisateur',
                  labelStyle: TextStyle(color: Colors.white70),
                  hintText: 'Sans le @',
                  hintStyle: TextStyle(color: Colors.white30),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.lightBlue),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                final username = usernameController.text.trim();

                if (username.isNotEmpty) {
                  // Construire l'URL Telegram
                  final telegramUrl = 'https://t.me/$username';

                  // Ouvrir Telegram
                  launchUrl(
                    Uri.parse(telegramUrl),
                    mode: LaunchMode.externalApplication,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.lightBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Démarrer'),
            ),
          ],
        );
      },
    );
  }

  void _launchEmail() {
    // Afficher un dialogue pour entrer l'adresse email
    showDialog(
      context: context,
      builder: (context) {
        final TextEditingController emailController = TextEditingController();
        final TextEditingController subjectController = TextEditingController();

        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Envoyer un email',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: emailController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Adresse email',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: subjectController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Sujet',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                final email = emailController.text.trim();
                final subject = subjectController.text.trim();

                if (email.isNotEmpty) {
                  // Construire l'URL mailto
                  final mailtoUrl = 'mailto:$email';

                  // Ajouter le sujet s'il est présent
                  final url =
                      subject.isNotEmpty
                          ? '$mailtoUrl?subject=${Uri.encodeComponent(subject)}'
                          : mailtoUrl;

                  // Ouvrir le client email
                  launchUrl(Uri.parse(url));
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Envoyer'),
            ),
          ],
        );
      },
    );
  }
}
