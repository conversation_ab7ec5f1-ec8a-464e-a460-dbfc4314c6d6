import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../providers/theme_provider.dart';
import '../../services/inventory/csv_service.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_dropdown.dart';
import '../../widgets/neon_search_bar.dart';

final categoriesProvider = FutureProvider<List<ProductCategory>>((ref) async {
  final inventoryService = ref.read(inventoryServiceProvider);
  return inventoryService.getCategories();
});

// Utiliser le provider de services/inventory_service.dart

final selectedCategoryProvider = StateProvider<ProductCategory?>((ref) => null);
final searchQueryProvider = StateProvider<String>((ref) => '');
final viewModeProvider = StateProvider<bool>(
  (ref) => true,
); // true = grid, false = list

class InventoryScreen extends ConsumerWidget {
  const InventoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(categoriesProvider);
    final products = ref.watch(productsProvider);
    final selectedCategory = ref.watch(selectedCategoryProvider);
    final searchQuery = ref.watch(searchQueryProvider);

    // Filtrer les produits
    List<Product> filteredProducts = products;

    // Filtrer par catégorie si une catégorie est sélectionnée
    if (selectedCategory != null) {
      filteredProducts =
          filteredProducts
              .where((product) => product.categoryId == selectedCategory.id)
              .toList();
    }

    // Filtrer par recherche si une recherche est effectuée
    if (searchQuery.isNotEmpty) {
      filteredProducts =
          filteredProducts
              .where(
                (product) => product.name.toLowerCase().contains(
                  searchQuery.toLowerCase(),
                ),
              )
              .toList();
    }

    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child: Column(
          children: [
            _buildHeader(context, ref),
            Expanded(
              child: categoriesAsync.when(
                data: (categories) {
                  return _buildProductList(
                    context,
                    ref,
                    filteredProducts,
                    categories,
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error:
                    (error, stack) => Center(
                      child: Text(
                        'Erreur: $error',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: primaryColor,
        onPressed: () {
          context.push('/inventory/add');
        },
        child: const Icon(Icons.add, color: Colors.black),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    final categoriesAsync = ref.watch(categoriesProvider);
    final selectedCategory = ref.watch(selectedCategoryProvider);
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: NeonSearchBar(
                  onChanged: (value) {
                    ref.read(searchQueryProvider.notifier).state = value;
                  },
                  hintText: 'Rechercher un produit...',
                ),
              ),
              const SizedBox(width: 16),
              categoriesAsync.when(
                data: (categories) {
                  return NeonDropdown<ProductCategory>(
                    value: selectedCategory,
                    items: [
                      const DropdownMenuItem<ProductCategory>(
                        value: null,
                        child: Text('Toutes les catégories'),
                      ),
                      ...categories.map(
                        (category) => DropdownMenuItem<ProductCategory>(
                          value: category,
                          child: Text(category.name),
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      ref.read(selectedCategoryProvider.notifier).state = value;
                    },
                    width: 200,
                  );
                },
                loading: () => const SizedBox(width: 200),
                error: (_, __) => const SizedBox(width: 200),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                NeonButton(
                  text: 'Alertes',
                  icon: FontAwesomeIcons.triangleExclamation,
                  color: Colors.red,
                  onPressed: () {
                    context.push('/inventory/alerts');
                  },
                ),
                const SizedBox(width: 16),
                Consumer(
                  builder: (context, ref, _) {
                    final appTheme = ref.watch(themeProvider);
                    final secondaryColor = appTheme.secondaryColor;

                    return NeonButton(
                      text: 'Catégories',
                      icon: FontAwesomeIcons.tags,
                      color: secondaryColor,
                      onPressed: () {
                        context.push('/inventory/categories');
                      },
                    );
                  },
                ),
                const SizedBox(width: 16),
                Consumer(
                  builder: (context, ref, _) {
                    final isGridView = ref.watch(viewModeProvider);
                    final appTheme = ref.watch(themeProvider);
                    final primaryColor = appTheme.primaryColor;

                    return NeonButton(
                      text: isGridView ? 'Vue Liste' : 'Vue Grille',
                      icon:
                          isGridView
                              ? FontAwesomeIcons.list
                              : FontAwesomeIcons.tableList,
                      color: primaryColor,
                      onPressed: () {
                        ref.read(viewModeProvider.notifier).state = !isGridView;
                      },
                    );
                  },
                ),
                const SizedBox(width: 16),
                Consumer(
                  builder: (context, ref, _) {
                    final appTheme = ref.watch(themeProvider);
                    final primaryColor = appTheme.primaryColor;

                    return NeonButton(
                      text: 'Import/Export',
                      icon: FontAwesomeIcons.fileExport,
                      color: primaryColor,
                      onPressed: () {
                        _showImportExportDialog(context, ref);
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductList(
    BuildContext context,
    WidgetRef ref,
    List<Product> products,
    List<ProductCategory> categories,
  ) {
    if (products.isEmpty) {
      return const Center(
        child: Text(
          'Aucun produit trouvé',
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      );
    }

    final isGridView = ref.watch(viewModeProvider);

    if (isGridView) {
      // Vue en grille
      return _buildGridView(products);
    } else {
      // Vue en liste
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          final category = categories.firstWhere(
            (c) => c.id == product.categoryId,
            orElse:
                () => ProductCategory(
                  id: '',
                  name: 'Inconnue',
                  price: 0,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
          );

          return _buildProductListItem(context, ref, product, category);
        },
      );
    }
  }

  Widget _buildGridView(List<Product> products) {
    return Consumer(
      builder: (context, ref, _) {
        final appTheme = ref.watch(themeProvider);
        final primaryColor = appTheme.primaryColor;

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6, // Augmentation à 6 cartes par ligne
            childAspectRatio:
                0.65, // Ajustement du ratio pour maintenir une bonne proportion
            crossAxisSpacing: 8, // Réduction de l'espacement horizontal
            mainAxisSpacing: 8, // Réduction de l'espacement vertical
          ),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final product = products[index];
            return GestureDetector(
              onTap: () => context.push('/inventory/product/${product.id}'),
              child: NeonCard(
                color: primaryColor,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Image du produit
                    Expanded(
                      child: ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(8),
                        ),
                        child:
                            product.imageUrl != null &&
                                    product.imageUrl!.isNotEmpty
                                ? product.imageUrl!.startsWith('data:image')
                                    ? Image.memory(
                                      base64Decode(product.imageUrl!.split(',')[1]),
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      errorBuilder:
                                          (
                                            context,
                                            error,
                                            stackTrace,
                                          ) => const Icon(
                                            Icons.image_not_supported,
                                            size:
                                                35, // Réduction de la taille de l'icône
                                          ),
                                    )
                                    : product.imageUrl!.startsWith('http')
                                    ? Image.network(
                                      product.imageUrl!,
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      errorBuilder:
                                          (
                                            context,
                                            error,
                                            stackTrace,
                                          ) => const Icon(
                                            Icons.image_not_supported,
                                            size:
                                                35, // Réduction de la taille de l'icône
                                          ),
                                    )
                                    : kIsWeb
                                    ? Image.network(
                                      product.imageUrl!,
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      errorBuilder:
                                          (
                                            context,
                                            error,
                                            stackTrace,
                                          ) => const Icon(
                                            Icons.image_not_supported,
                                            size:
                                                35, // Réduction de la taille de l'icône
                                          ),
                                    )
                                    : Image.file(
                                      File(product.imageUrl!),
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      errorBuilder:
                                          (
                                            context,
                                            error,
                                            stackTrace,
                                          ) => const Icon(
                                            Icons.image_not_supported,
                                            size:
                                                35, // Réduction de la taille de l'icône
                                          ),
                                    )
                                : Container(
                                  color: Colors.grey[800],
                                  child: const Icon(
                                    Icons.inventory_2,
                                    size: 35,
                                  ), // Réduction de la taille de l'icône
                                ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(
                        4.0,
                      ), // Réduction des marges internes
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 10, // Réduction de la taille du texte
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(
                            height: 1,
                          ), // Réduction de l'espacement
                          Consumer(
                            builder: (context, ref, _) {
                              final appTheme = ref.watch(themeProvider);
                              final secondaryColor = appTheme.secondaryColor;

                              return Text(
                                '${product.price.toStringAsFixed(0)} FCFA',
                                style: TextStyle(
                                  color: secondaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize:
                                      9, // Réduction de la taille du texte
                                ),
                              );
                            },
                          ),
                          const SizedBox(
                            height: 1,
                          ), // Réduction de l'espacement
                          Text(
                            'Stock: ${product.quantity}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 8, // Réduction de la taille du texte
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildProductListItem(
    BuildContext context,
    WidgetRef ref,
    Product product,
    ProductCategory category,
  ) {
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;

    Color statusColor;
    switch (product.stockStatus) {
      case StockStatus.critical:
        statusColor = Colors.red;
        break;
      case StockStatus.low:
        statusColor = Colors.orange;
        break;
      case StockStatus.normal:
        statusColor = Colors.green;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: primaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Row(
          children: [
            Expanded(
              child: Text(
                product.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                product.getStatusLabel(),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Catégorie: ${category.name}',
                    style: TextStyle(
                      color: Colors.white.withValues(
                        alpha: 179,
                      ), // 0.7 * 255 ≈ 179
                    ),
                  ),
                ),
                Text(
                  'Prix: ${product.price.toStringAsFixed(0)} FCFA',
                  style: TextStyle(
                    color: Colors.white.withValues(
                      alpha: 179,
                    ), // 0.7 * 255 ≈ 179
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Quantité: ${product.quantity}',
                  style: TextStyle(
                    color: Colors.white.withValues(
                      alpha: 179,
                    ), // 0.7 * 255 ≈ 179
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(Icons.edit, color: appTheme.secondaryColor, size: 20),
              onPressed: () {
                context.push('/inventory/edit/${product.id}');
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red, size: 20),
              onPressed: () {
                _showDeleteConfirmation(context, ref, product);
              },
            ),
          ],
        ),
        onTap: () {
          context.push('/inventory/product/${product.id}');
        },
      ),
    );
  }

  void _showImportExportDialog(BuildContext context, WidgetRef ref) {
    final csvService = ref.read(csvServiceProvider);
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Import / Export',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Export de produits
              ListTile(
                leading: Icon(Icons.upload_file, color: primaryColor),
                title: const Text(
                  'Exporter les produits',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () async {
                  Navigator.pop(context);
                  try {
                    final filePath = await csvService.exportProducts();
                    if (filePath != null) {
                      await csvService.shareFile(filePath);
                    } else {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Aucun produit à exporter'),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
                    }
                  }
                },
              ),

              // Export de catégories
              ListTile(
                leading: Icon(Icons.upload_file, color: secondaryColor),
                title: const Text(
                  'Exporter les catégories',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () async {
                  Navigator.pop(context);
                  try {
                    final filePath = await csvService.exportCategories();
                    if (filePath != null) {
                      await csvService.shareFile(filePath);
                    } else {
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Aucune catégorie à exporter'),
                          ),
                        );
                      }
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
                    }
                  }
                },
              ),

              // Import de produits
              ListTile(
                leading: Icon(Icons.download, color: primaryColor),
                title: const Text(
                  'Importer des produits',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () async {
                  Navigator.pop(context);
                  try {
                    final count = await csvService.importProducts();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('$count produits importés avec succès'),
                          backgroundColor: Colors.green,
                        ),
                      );
                      // Rafraîchir les données
                      ref.invalidate(productsProvider);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
                    }
                  }
                },
              ),

              // Import de catégories
              ListTile(
                leading: Icon(Icons.download, color: secondaryColor),
                title: const Text(
                  'Importer des catégories',
                  style: TextStyle(color: Colors.white),
                ),
                onTap: () async {
                  Navigator.pop(context);
                  try {
                    final count = await csvService.importCategories();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            '$count catégories importées avec succès',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                      // Rafraîchir les données
                      ref.invalidate(categoriesProvider);
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
                    }
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Fermer',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    Product product,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Confirmer la suppression',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer ${product.name} ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final inventoryService = ref.read(inventoryServiceProvider);
                await inventoryService.deleteProduct(product.id);

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(productsProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
