import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/customer/customer.dart';
import '../../providers/theme_provider.dart';
import '../../services/customer/customer_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/user_avatar.dart';
import '../../utils/vcard_importer.dart';
import '../../utils/csv_importer.dart';
import '../../utils/contacts_importer.dart';

class CustomersScreen extends ConsumerStatefulWidget {
  const CustomersScreen({super.key});

  @override
  ConsumerState<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends ConsumerState<CustomersScreen> {
  final _searchController = TextEditingController();
  bool _isLoading = false;
  CustomerStatus? _statusFilter;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Charger les clients
      await ref.read(customerServiceProvider).getCustomers();

      // Rafraîchir la liste des clients
      final _ = ref.refresh(customersProvider);
    } catch (e) {
      // Gérer l'erreur
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Clients'),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: ref.watch(primaryTextColorProvider),
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Column(
                  children: [
                    _buildHeader(context),
                    Expanded(child: _buildCustomerList(context)),
                  ],
                ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: NeonTheme.neonGreen,
        onPressed: () {
          _showAddCustomerOptions(context);
        },
        child: Icon(Icons.add, color: ref.watch(primaryTextColorProvider)),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.surface.withValues(alpha: 77), // 0.3 * 255 ≈ 77
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: NeonTheme.neonGreen.withValues(alpha: 51), // 0.2 * 255 ≈ 51
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        children: [
          // Barre de recherche
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.surface.withValues(alpha: 102), // 0.4 * 255 ≈ 102
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: ref
                    .watch(primaryTextColorProvider)
                    .withValues(alpha: 51), // 0.2 * 255 ≈ 51
              ),
            ),
            child: TextField(
              controller: _searchController,
              style: TextStyle(color: ref.watch(primaryTextColorProvider)),
              decoration: InputDecoration(
                hintText: 'Rechercher un client...',
                hintStyle: TextStyle(
                  color: ref.watch(secondaryTextColorProvider),
                ),
                icon: Icon(
                  Icons.search,
                  color: ref.watch(secondaryTextColorProvider),
                ),
                border: InputBorder.none,
              ),
              onChanged: (value) {
                // Mettre à jour la recherche
                setState(() {});
              },
            ),
          ),
          const SizedBox(height: 16),
          // Boutons de filtrage
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              NeonButton(
                text: 'Tous',
                icon: FontAwesomeIcons.users,
                color:
                    _statusFilter == null
                        ? NeonTheme.neonCyan
                        : NeonTheme.neonGrey,
                onPressed: () {
                  setState(() {
                    _statusFilter = null;
                  });
                },
              ),
              NeonButton(
                text: 'Clients',
                icon: FontAwesomeIcons.userCheck,
                color:
                    _statusFilter == CustomerStatus.customer
                        ? NeonTheme.neonGreen
                        : NeonTheme.neonGrey,
                onPressed: () {
                  setState(() {
                    _statusFilter = CustomerStatus.customer;
                  });
                },
              ),
              NeonButton(
                text: 'Prospects',
                icon: FontAwesomeIcons.userPlus,
                color:
                    _statusFilter == CustomerStatus.lead
                        ? NeonTheme.neonOrange
                        : NeonTheme.neonGrey,
                onPressed: () {
                  setState(() {
                    _statusFilter = CustomerStatus.lead;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerList(BuildContext context) {
    final customersAsync = ref.watch(customersProvider);

    return customersAsync.when(
      data: (customers) {
        if (customers.isEmpty) {
          return Center(
            child: NeonCard(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      FontAwesomeIcons.userGroup,
                      size: 48,
                      color: NeonTheme.neonCyan,
                    ),
                    const SizedBox(height: 16),
                    Consumer(
                      builder: (context, ref, child) {
                        return Text(
                          'Aucun client trouvé',
                          style: TextStyle(
                            color: ref.watch(primaryTextColorProvider),
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    Consumer(
                      builder: (context, ref, child) {
                        return Text(
                          'Ajoutez votre premier client en cliquant sur le bouton +',
                          style: TextStyle(
                            color: ref.watch(secondaryTextColorProvider),
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // Filtrer les clients selon la recherche et le statut
        var filteredCustomers =
            customers.where((customer) {
              // Filtre de recherche
              final searchMatch =
                  _searchController.text.isEmpty ||
                  customer.name.toLowerCase().contains(
                    _searchController.text.toLowerCase(),
                  ) ||
                  (customer.email?.toLowerCase().contains(
                        _searchController.text.toLowerCase(),
                      ) ??
                      false) ||
                  (customer.phone?.toLowerCase().contains(
                        _searchController.text.toLowerCase(),
                      ) ??
                      false);

              // Filtre de statut
              final statusMatch =
                  _statusFilter == null || customer.status == _statusFilter;

              return searchMatch && statusMatch;
            }).toList();

        return Padding(
          padding: const EdgeInsets.all(16),
          child: ListView.builder(
            itemCount: filteredCustomers.length,
            itemBuilder: (context, index) {
              final customer = filteredCustomers[index];
              return _buildCustomerCard(context, customer);
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Consumer(
              builder: (context, ref, child) {
                return Text(
                  'Erreur: $error',
                  style: TextStyle(color: ref.watch(primaryTextColorProvider)),
                );
              },
            ),
          ),
    );
  }

  Widget _buildCustomerCard(BuildContext context, Customer customer) {
    Color statusColor;
    switch (customer.status) {
      case CustomerStatus.lead:
        statusColor = NeonTheme.neonOrange;
        break;
      case CustomerStatus.customer:
        statusColor = NeonTheme.neonGreen;
        break;
      case CustomerStatus.vip:
        statusColor = NeonTheme.neonPurple;
        break;
      case CustomerStatus.inactive:
        statusColor = NeonTheme.neonGrey;
        break;
      case CustomerStatus.blocked:
        statusColor = NeonTheme.neonRed;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: Theme.of(
        context,
      ).colorScheme.surface.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: statusColor.withValues(alpha: 77),
        ), // 0.3 * 255 ≈ 77
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        leading: UserAvatar(name: customer.name, size: 50),
        title: Consumer(
          builder: (context, ref, child) {
            return Text(
              customer.name,
              style: TextStyle(
                color: ref.watch(primaryTextColorProvider),
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (customer.email != null)
              Consumer(
                builder: (context, ref, child) {
                  return Text(
                    customer.email!,
                    style: TextStyle(
                      color: ref.watch(secondaryTextColorProvider),
                    ),
                  );
                },
              ),
            if (customer.phone != null)
              Consumer(
                builder: (context, ref, child) {
                  return Text(
                    customer.phone!,
                    style: TextStyle(
                      color: ref.watch(secondaryTextColorProvider),
                    ),
                  );
                },
              ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 51), // 0.2 * 255 ≈ 51
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatusLabel(customer.status),
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        onTap: () {
          // Naviguer vers les détails du client
          context.push('/customers/${customer.id}');
        },
      ),
    );
  }

  String _getStatusLabel(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.lead:
        return 'Prospect';
      case CustomerStatus.customer:
        return 'Client';
      case CustomerStatus.vip:
        return 'VIP';
      case CustomerStatus.inactive:
        return 'Inactif';
      case CustomerStatus.blocked:
        return 'Bloqué';
    }
  }

  void _showAddCustomerOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xFF1A1A2E),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Ajouter un client',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              _buildOptionButton(
                context,
                icon: Icons.person_add,
                label: 'Créer un nouveau client',
                color: NeonTheme.neonGreen,
                onTap: () {
                  Navigator.pop(context);
                  context.push('/customers/add');
                },
              ),
              const SizedBox(height: 12),
              _buildOptionButton(
                context,
                icon: Icons.contact_phone,
                label: 'Importer depuis les contacts',
                color: NeonTheme.neonCyan,
                onTap: () {
                  Navigator.pop(context);
                  _importFromContacts(context);
                },
              ),
              const SizedBox(height: 12),
              _buildOptionButton(
                context,
                icon: Icons.contact_page,
                label: 'Importer un fichier vCard',
                color: NeonTheme.neonPink,
                onTap: () {
                  Navigator.pop(context);
                  _importVCard(context);
                },
              ),
              const SizedBox(height: 12),
              _buildOptionButton(
                context,
                icon: Icons.file_upload,
                label: 'Importer un fichier CSV',
                color: Colors.orange,
                onTap: () {
                  Navigator.pop(context);
                  _importCSV(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOptionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: color.withAlpha(25), // 0.1 * 255 ≈ 25
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withAlpha(77)), // 0.3 * 255 ≈ 77
        ),
        child: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                label,
                style: TextStyle(color: color, fontWeight: FontWeight.bold),
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  Future<void> _importFromContacts(BuildContext context) async {
    // Capturer les références nécessaires avant l'opération asynchrone
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              backgroundColor: Color(0xFF1A1A2E),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: NeonTheme.neonCyan),
                  SizedBox(height: 16),
                  Text(
                    'Importation en cours...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
      );

      // Importer les contacts depuis le téléphone
      final customers = await ContactsImporter.importFromContacts();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      if (customers.isEmpty) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Aucun contact importé'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Ajouter les clients importés
      final customerService = ref.read(customerServiceProvider);
      int importedCount = 0;

      for (final customer in customers) {
        try {
          await customerService.addCustomer(customer);
          importedCount++;
        } catch (e) {
          debugPrint('Erreur lors de l\'ajout du client ${customer.name}: $e');
        }
      }

      // Rafraîchir la liste des clients
      await _loadCustomers();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Afficher un message de succès
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$importedCount contacts importés avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      // Afficher un message d'erreur
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'importation: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _importVCard(BuildContext context) async {
    // Capturer les références nécessaires avant l'opération asynchrone
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              backgroundColor: Color(0xFF1A1A2E),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: NeonTheme.neonPink),
                  SizedBox(height: 16),
                  Text(
                    'Importation en cours...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
      );

      // Importer les contacts depuis le fichier vCard
      final customers = await VCardImporter.importFromFile();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      if (customers.isEmpty) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Aucun contact importé'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Ajouter les clients importés
      final customerService = ref.read(customerServiceProvider);
      int importedCount = 0;

      for (final customer in customers) {
        try {
          await customerService.addCustomer(customer);
          importedCount++;
        } catch (e) {
          debugPrint('Erreur lors de l\'ajout du client ${customer.name}: $e');
        }
      }

      // Rafraîchir la liste des clients
      await _loadCustomers();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Afficher un message de succès
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$importedCount contacts importés avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      // Afficher un message d'erreur
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'importation: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _importCSV(BuildContext context) async {
    // Capturer les références nécessaires avant l'opération asynchrone
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              backgroundColor: Color(0xFF1A1A2E),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: Colors.orange),
                  SizedBox(height: 16),
                  Text(
                    'Importation en cours...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
      );

      // Importer les contacts depuis le fichier CSV
      final customers = await CSVImporter.importFromFile();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      if (customers.isEmpty) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Aucun contact importé'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Ajouter les clients importés
      final customerService = ref.read(customerServiceProvider);
      int importedCount = 0;

      for (final customer in customers) {
        try {
          await customerService.addCustomer(customer);
          importedCount++;
        } catch (e) {
          debugPrint('Erreur lors de l\'ajout du client ${customer.name}: $e');
        }
      }

      // Rafraîchir la liste des clients
      await _loadCustomers();

      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Afficher un message de succès
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$importedCount contacts importés avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Vérifier si le widget est toujours monté
      if (!mounted) return;

      // Fermer le dialogue de chargement
      navigator.pop();

      // Afficher un message d'erreur
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'importation: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
