[{"id": "contacts", "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "name": "contacts", "type": "base", "fields": [{"id": "contacts_name", "name": "name", "type": "text", "required": true, "presentable": true, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_email", "name": "email", "type": "email", "required": false, "presentable": false, "options": {"exceptDomains": null, "onlyDomains": null}}, {"id": "contacts_phone", "name": "phone", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_company", "name": "company", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_notes", "name": "notes", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "contacts_avatar", "name": "avatar", "type": "file", "required": false, "presentable": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/gif", "image/webp"], "thumbs": ["100x100"], "protected": false}}, {"id": "contacts_owner", "name": "owner", "type": "relation", "required": false, "presentable": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "indexes": ["CREATE INDEX idx_contacts_name ON contacts (name)"]}, {"id": "messages", "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "name": "messages", "type": "base", "fields": [{"id": "messages_contact", "name": "contact", "type": "relation", "required": true, "presentable": false, "options": {"collectionId": "contacts", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "messages_content", "name": "content", "type": "text", "required": true, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "messages_is_user", "name": "is_user", "type": "bool", "required": true, "presentable": false}, {"id": "messages_timestamp", "name": "timestamp", "type": "date", "required": true, "presentable": false, "options": {"min": "", "max": ""}}, {"id": "messages_media_url", "name": "media_url", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "messages_media_type", "name": "media_type", "type": "select", "required": false, "presentable": false, "options": {"maxSelect": 1, "values": ["image", "video", "audio", "document"]}}, {"id": "messages_channel", "name": "channel", "type": "select", "required": false, "presentable": false, "options": {"maxSelect": 1, "values": ["app", "whatsapp", "facebook", "sms", "email"]}}, {"id": "messages_media_file", "name": "media_file", "type": "file", "required": false, "presentable": false, "options": {"maxSelect": 1, "maxSize": 10485760, "mimeTypes": [], "thumbs": [], "protected": false}}], "indexes": ["CREATE INDEX idx_messages_contact ON messages (contact)", "CREATE INDEX idx_messages_timestamp ON messages (timestamp)"]}, {"id": "tasks", "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "name": "tasks", "type": "base", "fields": [{"id": "tasks_title", "name": "title", "type": "text", "required": true, "presentable": true, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "tasks_description", "name": "description", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "tasks_contact", "name": "contact", "type": "relation", "required": false, "presentable": false, "options": {"collectionId": "contacts", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "tasks_due_date", "name": "due_date", "type": "date", "required": true, "presentable": false, "options": {"min": "", "max": ""}}, {"id": "tasks_status", "name": "status", "type": "select", "required": true, "presentable": false, "options": {"maxSelect": 1, "values": ["pending", "in_progress", "completed"]}}, {"id": "tasks_priority", "name": "priority", "type": "select", "required": true, "presentable": false, "options": {"maxSelect": 1, "values": ["low", "medium", "high"]}}, {"id": "tasks_assigned_to", "name": "assigned_to", "type": "relation", "required": false, "presentable": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "indexes": ["CREATE INDEX idx_tasks_due_date ON tasks (due_date)", "CREATE INDEX idx_tasks_status ON tasks (status)"]}, {"id": "opportunities", "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "name": "opportunities", "type": "base", "fields": [{"id": "opportunities_title", "name": "title", "type": "text", "required": true, "presentable": true, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "opportunities_contact", "name": "contact", "type": "relation", "required": true, "presentable": false, "options": {"collectionId": "contacts", "cascadeDelete": true, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}, {"id": "opportunities_value", "name": "value", "type": "number", "required": true, "presentable": false, "options": {"min": 0, "max": null}}, {"id": "opportunities_status", "name": "status", "type": "select", "required": true, "presentable": false, "options": {"maxSelect": 1, "values": ["lead", "proposal", "negotiation", "won", "lost"]}}, {"id": "opportunities_notes", "name": "notes", "type": "text", "required": false, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "opportunities_expected_close_date", "name": "expected_close_date", "type": "date", "required": false, "presentable": false, "options": {"min": "", "max": ""}}], "indexes": ["CREATE INDEX idx_opportunities_contact ON opportunities (contact)", "CREATE INDEX idx_opportunities_status ON opportunities (status)"]}, {"id": "knowledge_base", "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\"", "name": "knowledge_base", "type": "base", "fields": [{"id": "kb_title", "name": "title", "type": "text", "required": true, "presentable": true, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_content", "name": "content", "type": "text", "required": true, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_category", "name": "category", "type": "text", "required": true, "presentable": false, "options": {"min": null, "max": null, "pattern": ""}}, {"id": "kb_tags", "name": "tags", "type": "json", "required": false, "presentable": false}, {"id": "kb_is_public", "name": "is_public", "type": "bool", "required": false, "presentable": false}, {"id": "kb_author", "name": "author", "type": "relation", "required": false, "presentable": false, "options": {"collectionId": "_pb_users_auth_", "cascadeDelete": false, "minSelect": null, "maxSelect": 1, "displayFields": ["name"]}}], "indexes": ["CREATE INDEX idx_kb_category ON knowledge_base (category)", "CREATE INDEX idx_kb_title ON knowledge_base (title)"]}]