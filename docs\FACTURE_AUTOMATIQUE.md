# Facture Automatique - Documentation

## Vue d'ensemble

La fonctionnalité de **Facture Automatique** permet d'analyser automatiquement un texte de récapitulatif de commande (WhatsApp, SMS, email, etc.) et d'en extraire les informations pertinentes pour générer une facture PDF professionnelle.

## Fonctionnalités

### 🔍 Analyse Intelligente
- **Extraction automatique** des informations client (nom, téléphone)
- **Reconnaissance des articles** commandés avec quantités et prix
- **Détection des informations de paiement** (total, avance, reste à payer)
- **Identification de l'adresse de livraison** et des conditions particulières
- **Validation et formatage** automatique des données

### 🎯 Précision et Fiabilité
- **Indicateur de confiance** pour chaque analyse (0-100%)
- **Gestion robuste des erreurs** avec messages explicites
- **Validation des données** extraites avec corrections automatiques
- **Support de multiples formats** de texte d'entrée

### ⚡ Performance
- **Traitement rapide** (<2 secondes en moyenne)
- **Fonctionnement hors-ligne** possible
- **Optimisation pour mobile** avec interface responsive

## Comment utiliser

### 1. Accès à la fonctionnalité
- Aller dans **Factures** → **Facture Automatique**
- Ou utiliser le bouton flottant dans l'écran des factures

### 2. Saisie du texte
- Coller le texte de commande dans la zone de texte
- Utiliser le bouton **"Voir un exemple"** pour comprendre le format attendu
- Le texte doit contenir au minimum 10 caractères

### 3. Analyse
- Cliquer sur **"Analyser le texte"**
- Attendre l'analyse (indicateur de progression)
- Vérifier le niveau de confiance affiché

### 4. Vérification des résultats
- **Client** : Nom et numéro de téléphone
- **Articles** : Liste des produits avec quantités et prix
- **Paiement** : Montants total, avance et reste à payer
- **Livraison** : Lieu et détails de livraison
- **Notes** : Conditions particulières

### 5. Génération de la facture
- Cliquer sur **"Générer la facture"**
- La facture est automatiquement sauvegardée
- Un PDF peut être généré pour envoi au client

## Formats de texte supportés

### Exemple type
```
Commande pour Jean Dupont
Téléphone: +225 07 09 49 58 48

Articles:
- 2x Coque iPhone 15 Pro VIP-1 (6000 FCFA chacune)
- 1x Coque Samsung Galaxy S24 Premium (8000 FCFA)

Total: 20000 FCFA
Avance payée: 10000 FCFA (Mobile Money)
Reste à payer: 10000 FCFA

Livraison: Domicile à Cocody, Riviera Golf
Délai: 3 jours ouvrables

Conditions: Paiement du solde à la livraison
```

### Variations acceptées
- **Noms** : "pour", "client", "nom", etc.
- **Téléphones** : +225XXXXXXXX, 0XXXXXXXXX, XX XX XX XX XX
- **Quantités** : "2x", "2 fois", "2 pièces", etc.
- **Prix** : "FCFA", "F CFA", "francs", etc.
- **Livraison** : "domicile", "magasin", noms de communes

## Gestion des erreurs

### Erreurs courantes
1. **Texte trop court** : Minimum 10 caractères requis
2. **Aucune information détectée** : Reformuler le texte
3. **Confiance faible** (<60%) : Vérifier les données extraites
4. **Erreur IA** : Problème de connexion ou de service

### Solutions
- **Reformuler le texte** avec plus de détails
- **Utiliser l'exemple** comme modèle
- **Vérifier la connexion** internet
- **Contacter le support** si le problème persiste

## Indicateurs de qualité

### Niveau de confiance
- **🟢 80-100%** : Excellente qualité, données fiables
- **🟡 60-79%** : Bonne qualité, vérification recommandée
- **🔴 <60%** : Qualité faible, révision nécessaire

### Temps de traitement
- **Normal** : 500-2000ms
- **Lent** : 2000-5000ms (vérifier la connexion)
- **Très lent** : >5000ms (problème technique)

## Intégration CRM

### Données automatiquement créées
- **Client** : Ajouté s'il n'existe pas déjà
- **Facture** : Créée avec statut approprié
- **Articles** : Liés aux produits du catalogue
- **Historique** : Enregistré pour suivi

### Synchronisation
- **Supabase** : Synchronisation automatique
- **Hors-ligne** : Données sauvegardées localement
- **Conflits** : Résolution automatique lors de la reconnexion

## Sécurité et confidentialité

### Protection des données
- **Chiffrement** des communications
- **Stockage sécurisé** des informations client
- **Conformité RGPD** pour les données personnelles
- **Audit trail** des modifications

### Bonnes pratiques
- **Vérifier** toujours les données extraites
- **Ne pas partager** les textes de commande
- **Supprimer** les données sensibles après traitement
- **Utiliser** des connexions sécurisées

## Support technique

### Logs et débogage
- Les erreurs sont automatiquement enregistrées
- Temps de traitement affiché pour diagnostic
- Métadonnées disponibles pour analyse

### Contact
- **Email** : <EMAIL>
- **Téléphone** : +225 XX XX XX XX XX
- **Documentation** : Consultez ce guide

---

*Dernière mise à jour : Décembre 2024*
*Version : 2.0*
