import 'dart:io';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../theme/neon_theme.dart';
import '../providers/theme_provider.dart';
import 'theme_toggle_button.dart';

class NeonSidebar extends ConsumerStatefulWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  final String userEmail;
  final String userRole;
  final String? userAvatar;

  const NeonSidebar({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.userEmail,
    required this.userRole,
    this.userAvatar,
  });

  @override
  ConsumerState<NeonSidebar> createState() => _NeonSidebarState();
}

class _NeonSidebarState extends ConsumerState<NeonSidebar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  int _hoveredIndex = -1;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentTheme = Theme.of(context);
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;
    final sidebarGradient = appTheme.sidebarGradient;

    // Déterminer la luminosité du gradient de la sidebar
    bool isSidebarGradientLight =
        sidebarGradient.colors.isNotEmpty &&
        ThemeData.estimateBrightnessForColor(sidebarGradient.colors.first) ==
            Brightness.light;

    // Couleur pour les icônes/texte lorsque la sidebar est claire et le thème général est clair
    Color contrastColorForLightSidebar = currentTheme.colorScheme.onSurface
        .withValues(alpha: 217); // 0.85 * 255 ≈ 217
    // Couleur par défaut pour les icônes/texte (souvent la couleur primaire/secondaire du thème)
    // Color defaultInteractiveColor = primaryColor;

    Color getEffectiveColor(Color themeBasedColor, {bool isText = false}) {
      // Utiliser les couleurs de texte appropriées du thème
      if (isText) {
        // Pour le texte, utiliser les couleurs de texte du thème
        final isDark = ref.watch(isDarkThemeProvider);
        if (isSidebarGradientLight) {
          // Sidebar claire: utiliser du texte sombre
          return isDark ? ref.watch(primaryTextColorProvider) : Colors.black87;
        } else {
          // Sidebar sombre: utiliser du texte clair
          return isDark ? Colors.white : ref.watch(primaryTextColorProvider);
        }
      }

      // Pour les icônes et autres éléments
      if (isSidebarGradientLight) {
        // Sidebar claire: utiliser des couleurs sombres pour le contraste
        if (ThemeData.estimateBrightnessForColor(themeBasedColor) ==
            Brightness.light) {
          return contrastColorForLightSidebar;
        }
        return themeBasedColor;
      } else {
        // Sidebar sombre: utiliser les couleurs du thème ou des couleurs claires
        if (ThemeData.estimateBrightnessForColor(themeBasedColor) ==
            Brightness.dark) {
          return Colors.white.withValues(alpha: 217); // 0.85 * 255
        }
        return themeBasedColor;
      }
    }

    return Container(
      width:
          MediaQuery.of(context).size.width > 600
              ? 80
              : 60, // Réduit la largeur sur les petits écrans
      decoration: BoxDecoration(
        gradient: sidebarGradient,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
        border: Border(
          right: BorderSide(
            color: secondaryColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),
          // Avatar de l'utilisateur
          _buildUserAvatar(),
          const SizedBox(height: 8),
          // Badge de rôle
          _buildRoleBadge(),
          const SizedBox(height: 4),
          // Email de l'utilisateur (abrégé)
          _buildUserEmail(appTheme),
          const SizedBox(height: 32),
          // Boutons de navigation
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildNavItem(
                  0,
                  FontAwesomeIcons.gaugeHigh,
                  'Tableau de bord',
                  getEffectiveColor(primaryColor),
                  getEffectiveColor, // Passer la fonction pour le texte
                ),
                _buildNavItem(
                  1,
                  FontAwesomeIcons.message,
                  'Messages',
                  getEffectiveColor(secondaryColor),
                  getEffectiveColor,
                ),
                _buildNavItem(
                  2,
                  FontAwesomeIcons.userGroup,
                  'Clients',
                  getEffectiveColor(primaryColor),
                  getEffectiveColor,
                ),
                _buildNavItem(
                  3,
                  FontAwesomeIcons.listCheck,
                  'Tâches',
                  getEffectiveColor(secondaryColor),
                  getEffectiveColor,
                ),

                _buildNavItem(
                  4,
                  FontAwesomeIcons.boxOpen,
                  'Inventaire',
                  getEffectiveColor(primaryColor),
                  getEffectiveColor,
                ),
                _buildNavItem(
                  5, // Adjusted index
                  FontAwesomeIcons.book,
                  'Base de connaissance',
                  getEffectiveColor(primaryColor),
                  getEffectiveColor,
                ),
                _buildNavItem(
                  6, // Adjusted index
                  FontAwesomeIcons.gear,
                  'Paramètres',
                  getEffectiveColor(primaryColor),
                  getEffectiveColor,
                ),
              ],
            ),
          ),
          // Bouton de basculement de thème
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Center(child: ThemeToggleButton(showLabel: false, size: 20)),
          ),
          const SizedBox(height: 16),

          // Bouton de déconnexion
          _buildNavItem(
            8, // Adjusted index (was 10, now 6 items + 1 settings + 1 logout)
            FontAwesomeIcons.rightFromBracket,
            'Déconnexion',
            getEffectiveColor(Colors.redAccent),
            getEffectiveColor, // Appliquer la même logique pour le texte du bouton de déconnexion
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildUserAvatar() {
    final currentTheme = Theme.of(context);
    final themeBrightness = currentTheme.brightness;
    final appTheme = ref.watch(themeProvider);
    final sidebarGradient = appTheme.sidebarGradient;
    final isSidebarGradientLight =
        sidebarGradient.colors.isNotEmpty &&
        ThemeData.estimateBrightnessForColor(sidebarGradient.colors.first) ==
            Brightness.light;
    final contrastColorForLightSidebar = currentTheme.colorScheme.onSurface
        .withValues(alpha: 217); // 0.85 * 255 ≈ 217

    Color avatarBorderColor = appTheme.primaryColor;
    Color avatarIconColor = appTheme.primaryColor;
    Color editIconBgColor = appTheme.primaryColor;
    Color editIconColor = Colors.white;

    if (themeBrightness == Brightness.light && isSidebarGradientLight) {
      if (ThemeData.estimateBrightnessForColor(appTheme.primaryColor) ==
          Brightness.light) {
        avatarBorderColor = contrastColorForLightSidebar;
        avatarIconColor = contrastColorForLightSidebar;
        editIconBgColor = contrastColorForLightSidebar;
        // Pour l'icône d'édition sur fond sombre (contrastColorForLightSidebar), le blanc est bien.
      } else {
        // La couleur primaire du thème est déjà foncée, ok pour le contraste
      }
    } else if (themeBrightness == Brightness.dark && isSidebarGradientLight) {
      avatarBorderColor = contrastColorForLightSidebar;
      avatarIconColor = contrastColorForLightSidebar;
      editIconBgColor = contrastColorForLightSidebar;
    }
    // Pour les autres cas, les couleurs par défaut du thème devraient fonctionner.

    return GestureDetector(
      onTap: () {
        // Naviguer vers l'écran de profil
        widget.onItemSelected(-1); // Utiliser un index spécial pour le profil
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: avatarBorderColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: avatarBorderColor.withValues(
                    alpha: 77,
                  ), // 0.3 * 255 ≈ 77
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 24,
              backgroundColor: Colors.black,
              backgroundImage: _getAvatarImage(),
              onBackgroundImageError: (exception, stackTrace) {
                debugPrint(
                  'Erreur de chargement de l\'image de profil: $exception',
                );
              },
              child:
                  widget.userAvatar == null
                      ? Icon(Icons.person, color: avatarIconColor, size: 24)
                      : null,
            ),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: editIconBgColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: editIconBgColor.withValues(
                      alpha: 128,
                    ), // 0.5 * 255 ≈ 128
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Icon(Icons.edit, color: editIconColor, size: 10),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBadge() {
    final currentTheme = Theme.of(context);
    final themeBrightness = currentTheme.brightness;
    final appTheme = ref.watch(themeProvider);
    final sidebarGradient = appTheme.sidebarGradient;
    final isSidebarGradientLight =
        sidebarGradient.colors.isNotEmpty &&
        ThemeData.estimateBrightnessForColor(sidebarGradient.colors.first) ==
            Brightness.light;
    final contrastColorForLightSidebar = currentTheme.colorScheme.onSurface
        .withValues(alpha: 217); // 0.85 * 255 ≈ 217

    Color badgeBgColor = Colors.black.withValues(alpha: 153); // 0.6 * 255 ≈ 153
    Color badgeBorderColor = appTheme.secondaryColor;
    Color badgeTextColor = Colors.white;

    if (themeBrightness == Brightness.light && isSidebarGradientLight) {
      badgeBgColor = contrastColorForLightSidebar.withValues(
        alpha: 26,
      ); // 0.1 * 255 ≈ 26
      if (ThemeData.estimateBrightnessForColor(appTheme.secondaryColor) ==
          Brightness.light) {
        badgeBorderColor = contrastColorForLightSidebar;
        badgeTextColor = contrastColorForLightSidebar;
      } else {
        badgeBorderColor =
            appTheme
                .secondaryColor; // La couleur secondaire est déjà contrastante
        badgeTextColor = appTheme.secondaryColor;
      }
    } else if (themeBrightness == Brightness.dark && isSidebarGradientLight) {
      badgeBgColor = contrastColorForLightSidebar.withValues(
        alpha: 26,
      ); // 0.1 * 255 ≈ 26
      badgeBorderColor = contrastColorForLightSidebar;
      badgeTextColor = contrastColorForLightSidebar;
    }
    // Pour les autres cas, les couleurs par défaut du thème devraient fonctionner.

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeBgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeBorderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: badgeBorderColor.withValues(alpha: 128), // 0.5 * 255 ≈ 128
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Text(
        widget.userRole,
        style: TextStyle(
          color: badgeTextColor,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // Méthode pour obtenir l'image de l'avatar en fonction du type de chemin
  ImageProvider? _getAvatarImage() {
    if (widget.userAvatar == null || widget.userAvatar!.isEmpty) {
      return null;
    }

    // Vérifier si c'est un chemin de fichier local ou une URL
    if (widget.userAvatar!.startsWith('http://') ||
        widget.userAvatar!.startsWith('https://')) {
      // C'est une URL réseau
      debugPrint(
        'NeonSidebar: Utilisation d\'une URL réseau pour l\'avatar: ${widget.userAvatar}',
      );
      return NetworkImage(widget.userAvatar!);
    } else {
      // C'est un chemin de fichier local
      debugPrint(
        'NeonSidebar: Utilisation d\'un fichier local pour l\'avatar: ${widget.userAvatar}',
      );
      final file = File(widget.userAvatar!);

      // Vérifier si le fichier existe
      if (file.existsSync()) {
        return FileImage(file);
      } else {
        debugPrint('NeonSidebar: Le fichier d\'avatar n\'existe pas');
        return null;
      }
    }
  }

  Color getEmailTextColor() {
    final currentTheme = Theme.of(context);
    final themeBrightness = currentTheme.brightness;
    final appTheme = ref.watch(themeProvider);
    final sidebarGradient = appTheme.sidebarGradient;
    final isSidebarGradientLight =
        sidebarGradient.colors.isNotEmpty &&
        ThemeData.estimateBrightnessForColor(sidebarGradient.colors.first) ==
            Brightness.light;
    final contrastColorForLightSidebar = currentTheme.colorScheme.onSurface
        .withValues(alpha: 217); // 0.85 * 255 ≈ 217

    if (themeBrightness == Brightness.light && isSidebarGradientLight) {
      return contrastColorForLightSidebar.withValues(
        alpha: 179,
      ); // 0.7 * 255 ≈ 179
    }
    return Colors.white.withValues(alpha: 179); // 0.7 * 255 ≈ 179
  }

  Widget _buildUserEmail(dynamic appTheme) {
    // Afficher seulement une partie de l'email si trop long
    String displayEmail = widget.userEmail;
    if (displayEmail.length > 12) {
      final parts = displayEmail.split('@');
      if (parts.length == 2) {
        displayEmail =
            '${parts[0].substring(0, 5)}...@${parts[1].substring(0, 3)}...';
      }
    }

    return Text(
      displayEmail,
      style: TextStyle(
        color:
            ref.watch(isDarkThemeProvider)
                ? Colors.white.withValues(alpha: 204)
                : ref.watch(
                  secondaryTextColorProvider,
                ), // Couleur de texte pour l'email
        fontSize: 9,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildNavItem(
    int index,
    IconData icon,
    String label,
    Color iconColor,
    Function(Color, {bool isText}) getTextColorFunction,
  ) {
    final isSelected = widget.selectedIndex == index;
    final isHovered = _hoveredIndex == index;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: MouseRegion(
        onEnter: (_) => setState(() => _hoveredIndex = index),
        onExit: (_) => setState(() => _hoveredIndex = -1),
        child: GestureDetector(
          onTap: () => widget.onItemSelected(index),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 56,
            decoration: BoxDecoration(
              color:
                  isSelected || isHovered
                      ? iconColor.withValues(
                        alpha: 38,
                      ) // 0.15 * 255 ≈ 38 - Fond légèrement coloré avec la couleur de l'icône
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected || isHovered ? iconColor : Colors.transparent,
                width: 1.5,
              ),
              boxShadow:
                  isSelected || isHovered
                      ? NeonTheme.neonShadow(
                        iconColor,
                        intensity: isSelected ? 0.8 : 0.4,
                      )
                      : null,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color:
                      isSelected || isHovered
                          ? iconColor // Couleur de l'icône quand sélectionné/survolé
                          : getTextColorFunction(
                            iconColor,
                            isText: false,
                          ), // Couleur de l'icône au repos
                  size: 20,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color:
                        isSelected || isHovered
                            ? iconColor // Couleur du texte quand sélectionné/survolé
                            : getTextColorFunction(
                              iconColor,
                              isText: true,
                            ), // Couleur du texte au repos
                    fontSize: 10,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
