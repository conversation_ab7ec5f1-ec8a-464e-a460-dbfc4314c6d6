import 'package:flutter/material.dart';

/// Thème unique pour l'application
class NeonTheme {
  // Palette principale de l'écran de messages
  static const Color darkSurface = Color(0xFF2A2D3E);
  static const Color cardColor = Color(0xFF2A2D3E); // Ajout de cardColor
  static const Color darkBackground = Color(0xFF1E1E2D);
  static const borderColor = Color(0xFF2A2A3E); // Bordures et séparateurs

  // Couleurs d'accent
  static const primaryAccent = Color(
    0xFF6A5ACD,
  ); // Violet principal (remplace neonCyan)
  static const secondaryAccent = Color(
    0xFFBF00FF,
  ); // Violet secondaire (remplace neonPurple)
  static const tertiaryAccent = Color(
    0xFFF7A8A0,
  ); // Rose pastel pour les accents

  // Couleurs de base
  static const white = Color(0xFFFFFFFF);
  static const lightGrey = Color(0xFFF2F2F2);
  static const mediumGrey = Color(0xFFDADADA);

  // Couleurs fonctionnelles
  static const successColor = Color(0xFF4CAF50); // Vert pour succès
  static const warningColor = Color(0xFFFFEB3B); // Jaune pour avertissements
  static const errorColor = Color(0xFFFF6F61); // Rouge doux pour erreurs

  // Anciennes couleurs (pour compatibilité - à supprimer progressivement)
  static const neonCyan = primaryAccent;
  static const neonPurple = secondaryAccent;
  static const neonGreen = successColor;
  static const neonPink = tertiaryAccent;
  static const neonBlue = Color(0xFF1E90FF);
  static const neonTurquoise = Color(0xFF40E0D0);
  static const neonOrange = warningColor;
  static const neonRed = errorColor;
  static const neonGrey = mediumGrey;

  // Dégradés principaux
  static const LinearGradient mainGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkBackground, primaryAccent],
  );

  static const LinearGradient sidebarGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [darkSurface, Color(0xFF0F0F1E)],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkSurface, Color(0xFF252542)],
  );

  // Dégradé secondaire pour les écrans d'inventaire
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [darkSurface, Color(0xFF2A2A3E)],
  );

  // Dégradé actif
  static const LinearGradient activeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryAccent, secondaryAccent],
  );

  // Ombres et effets
  static List<BoxShadow> neonShadow(Color color, {double intensity = 1.0}) {
    return [
      BoxShadow(
        color: color.withAlpha((77 * intensity).toInt()), // 0.3 * 255 ≈ 77
        blurRadius: 8.0 * intensity,
        spreadRadius: 2.0 * intensity,
      ),
      BoxShadow(
        color: color.withAlpha((51 * intensity).toInt()), // 0.2 * 255 ≈ 51
        blurRadius: 16.0 * intensity,
        spreadRadius: 4.0 * intensity,
      ),
    ];
  }

  static List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Colors.black.withAlpha(51), // 0.2 * 255 ≈ 51
      blurRadius: 8.0,
      spreadRadius: 1.0,
      offset: const Offset(0, 4),
    ),
  ];

  // Styles de texte
  static const TextStyle headingStyle = TextStyle(
    color: primaryAccent,
    fontSize: 24,
    fontWeight: FontWeight.bold,
    letterSpacing: 1.2,
  );

  static const TextStyle subheadingStyle = TextStyle(
    color: primaryAccent,
    fontSize: 18,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.8,
  );

  static const TextStyle bodyStyle = TextStyle(
    color: white,
    fontSize: 14,
    letterSpacing: 0.5,
  );

  static TextStyle neonTextStyle(Color color) {
    return TextStyle(
      color: color,
      fontSize: 16,
      fontWeight: FontWeight.bold,
      letterSpacing: 1.0,
      shadows: [
        Shadow(
          color: color.withAlpha(204), // 0.8 * 255 ≈ 204
          blurRadius: 8.0,
        ),
        Shadow(
          color: color.withAlpha(128), // 0.5 * 255 ≈ 128
          blurRadius: 16.0,
        ),
      ],
    );
  }

  // Décoration pour les boutons néon
  static BoxDecoration neonButtonDecoration(
    Color color, {
    bool isActive = false,
  }) {
    return BoxDecoration(
      gradient: isActive ? activeGradient : null,
      color:
          isActive ? null : darkSurface.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      borderRadius: BorderRadius.circular(12),
      border: Border.all(color: color, width: 1.5),
      boxShadow: isActive ? neonShadow(color) : null,
    );
  }

  // Décoration pour les cartes
  static BoxDecoration cardDecoration = BoxDecoration(
    color: darkSurface,
    borderRadius: BorderRadius.circular(16),
    boxShadow: cardShadow,
    border: Border.all(color: borderColor, width: 1),
  );

  // Décoration pour les avatars
  static BoxDecoration avatarDecoration(Color borderColor) {
    return BoxDecoration(
      shape: BoxShape.circle,
      border: Border.all(color: borderColor, width: 2),
      boxShadow: neonShadow(borderColor, intensity: 0.7),
    );
  }

  // Thème unique pour l'application
  static ThemeData themeData = ThemeData.dark().copyWith(
    // Couleurs de base
    scaffoldBackgroundColor: darkBackground,
    primaryColor: primaryAccent,
    colorScheme: const ColorScheme.dark(
      primary: primaryAccent,
      secondary: secondaryAccent,
      surface: darkSurface,
      onSurface: white,
      onPrimary: white,
      onSecondary: white,
      error: errorColor,
    ),

    // AppBar
    appBarTheme: const AppBarTheme(
      backgroundColor: darkSurface,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: 0.5,
      ),
      iconTheme: IconThemeData(color: white),
    ),

    // Texte
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        color: white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        letterSpacing: 1.2,
      ),
      headlineMedium: TextStyle(
        color: white,
        fontSize: 18,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.8,
      ),
      bodyLarge: TextStyle(color: white, fontSize: 14, letterSpacing: 0.5),
    ),

    // Icônes
    iconTheme: const IconThemeData(color: primaryAccent, size: 24),

    // Boutons
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryAccent,
        foregroundColor: white,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      ),
    ),

    // Cartes
    cardTheme: CardTheme(
      color: darkSurface,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ),

    // Séparateurs
    dividerTheme: const DividerThemeData(color: borderColor, thickness: 1),
  );
}
