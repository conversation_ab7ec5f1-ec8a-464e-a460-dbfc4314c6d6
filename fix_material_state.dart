import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  // Répertoire racine du projet
  final Directory rootDir = Directory('lib');

  // Parcourir tous les fichiers .dart
  await for (final FileSystemEntity entity in rootDir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      await processFile(entity);
    }
  }

  debugPrint('Terminé !');
}

Future<void> processFile(File file) async {
  try {
    String content = await file.readAsString();

    // Remplacer MaterialStateProperty par WidgetStateProperty
    bool hasChanges = false;

    // Remplacer MaterialStateProperty par WidgetStateProperty
    if (content.contains('WidgetStateProperty')) {
      content = content.replaceAll(
        'WidgetStateProperty',
        'MaterialStateProperty',
      );
      hasChanges = true;
    }
    // Remplacer MaterialState par WidgetState
    if (content.contains('WidgetState.')) {
      content = content.replaceAll('WidgetState.', 'MaterialState.');
      hasChanges = true;
    }

    if (hasChanges) {
      await file.writeAsString(content);
      debugPrint('Fichier mis à jour : ${file.path}');
    }
  } catch (e) {
    debugPrint('Erreur lors du traitement du fichier ${file.path}: $e');
  }
}

