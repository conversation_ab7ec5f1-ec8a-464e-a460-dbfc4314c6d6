// Script pour créer la collection "contacts" via l'API PocketBase
// Exécutez ce script dans la console de votre navigateur après vous être connecté à l'interface d'administration

// Récupérer le token d'authentification
const authToken = localStorage.getItem('pocketbase_auth');

if (!authToken) {
  console.error('Vous devez être connecté à l\'interface d\'administration PocketBase');
  throw new Error('Non authentifié');
}

// Créer la collection "contacts"
fetch('http://127.0.0.1:8090/api/collections', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': authToken
  },
  body: JSON.stringify({
    name: 'contacts',
    type: 'base',
    schema: [
      {
        name: 'name',
        type: 'text',
        required: true,
        options: {
          min: null,
          max: null,
          pattern: ''
        }
      },
      {
        name: 'email',
        type: 'email',
        required: false,
        options: {
          exceptDomains: null,
          onlyDomains: null
        }
      },
      {
        name: 'phone',
        type: 'text',
        required: false,
        options: {
          min: null,
          max: null,
          pattern: ''
        }
      },
      {
        name: 'company',
        type: 'text',
        required: false,
        options: {
          min: null,
          max: null,
          pattern: ''
        }
      },
      {
        name: 'notes',
        type: 'text',
        required: false,
        options: {
          min: null,
          max: null,
          pattern: ''
        }
      }
    ]
  })
})
.then(response => response.json())
.then(data => {
  console.log('Collection créée avec succès:', data);
})
.catch(error => {
  console.error('Erreur lors de la création de la collection:', error);
});
