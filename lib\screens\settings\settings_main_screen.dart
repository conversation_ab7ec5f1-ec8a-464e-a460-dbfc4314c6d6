import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../providers/theme_provider.dart';
import '../../theme/neon_theme.dart';

class SettingsMainScreen extends ConsumerWidget {
  const SettingsMainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres'),
        backgroundColor: currentTheme.primaryColor,
        foregroundColor:
            ref.watch(isDarkThemeProvider) ? Colors.white : Colors.black,
      ),
      body: Container(
        decoration: BoxDecoration(gradient: currentTheme.mainGradient),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Paramètres de l\'application',
                style: TextStyle(
                  color:
                      ref.watch(isDarkThemeProvider)
                          ? Colors.white
                          : Colors.black,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Configurez les différents aspects de l\'application selon vos besoins.',
                style: TextStyle(
                  color:
                      ref.watch(isDarkThemeProvider)
                          ? Colors.white70
                          : Colors.black54,
                ),
              ),
              const SizedBox(height: 24),

              // Section IA
              _buildSectionTitle('Intelligence Artificielle'),
              _buildSettingCard(
                context,
                'Configuration de l\'IA',
                'Configurez les paramètres de l\'IA, les clés API et les modèles',
                FontAwesomeIcons.brain,
                NeonTheme.neonPurple,
                () => context.push('/settings/ai'),
              ),
              _buildSettingCard(
                context,
                'Réponses automatiques',
                'Configurez les réponses automatiques de l\'IA',
                FontAwesomeIcons.reply,
                NeonTheme.neonPurple,
                () => context.push('/settings/auto-response'),
              ),
              _buildSettingCard(
                context,
                'Performances de l\'IA',
                'Consultez les statistiques de performance de l\'IA',
                FontAwesomeIcons.chartLine,
                NeonTheme.neonPurple,
                () => context.push('/settings/ai-performance'),
              ),

              const SizedBox(height: 24),

              // Section Messagerie
              _buildSectionTitle('Messagerie'),
              _buildSettingCard(
                context,
                'Configuration de la messagerie',
                'Configurez les canaux de messagerie (WhatsApp, Email, etc.)',
                FontAwesomeIcons.message,
                NeonTheme.neonBlue,
                () => context.push('/settings/messaging'),
              ),
              _buildSettingCard(
                context,
                'Configuration Telegram',
                'Configurez l\'intégration avec Telegram',
                FontAwesomeIcons.telegram,
                const Color(0xFF0088cc),
                () => context.push('/settings/telegram'),
              ),

              const SizedBox(height: 24),

              // Section Utilisateurs
              _buildSectionTitle('Utilisateurs'),
              _buildSettingCard(
                context,
                'Gestion des utilisateurs',
                'Gérez les utilisateurs et leurs rôles',
                FontAwesomeIcons.users,
                NeonTheme.neonGreen,
                () => context.push('/settings/users'),
              ),

              const SizedBox(height: 24),

              // Section Communication
              _buildSectionTitle('Communication'),
              _buildSettingCard(
                context,
                'SMS',
                'Gérer les SMS et l\'historique des messages',
                FontAwesomeIcons.commentSms,
                Colors.green,
                () => context.push('/settings/sms'),
              ),
              _buildSettingCard(
                context,
                'Synchronisation multi-appareils',
                'Configurez la synchronisation entre vos appareils',
                FontAwesomeIcons.arrowsRotate,
                Colors.orange,
                () => context.push('/settings/sync'),
              ),

              const SizedBox(height: 24),

              // Section Apparence
              _buildSectionTitle('Apparence'),
              _buildSettingCard(
                context,
                'Thème',
                'Personnalisez l\'apparence de l\'application',
                FontAwesomeIcons.palette,
                currentTheme.primaryColor,
                () => context.push('/settings/theme'),
              ),

              const SizedBox(height: 24),

              // Section À propos
              _buildSectionTitle('À propos'),
              _buildSettingCard(
                context,
                'Informations sur l\'application',
                'Version, licences et informations légales',
                FontAwesomeIcons.circleInfo,
                NeonTheme.neonCyan,
                () {
                  showAboutDialog(
                    context: context,
                    applicationName: 'HCP-DESIGN CRM',
                    applicationVersion: '1.0.0',
                    applicationIcon: Image.asset(
                      'assets/images/logo/logo.png',
                      width: 50,
                      height: 50,
                    ),
                    applicationLegalese:
                        '© 2023 HCP-DESIGN. Tous droits réservés.',
                    children: [
                      const SizedBox(height: 16),
                      const Text(
                        'Application de gestion de relation client pour HCP-DESIGN, '
                        'spécialisée dans les coques de téléphone personnalisées.',
                      ),
                    ],
                  );
                },
              ),
              _buildSettingCard(
                context,
                'Débogage Avatar',
                'Diagnostiquer les problèmes de photo de profil',
                FontAwesomeIcons.bug,
                Colors.orange,
                () => context.push('/settings/avatar-debug'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSettingCard(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color.withValues(alpha: 77)), // 0.3 * 255 ≈ 77
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: color,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(FontAwesomeIcons.chevronRight, color: color, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
