import 'package:flutter/foundation.dart';

/// Service d'extraction manuelle des informations de facture
/// Alternative à l'IA utilisant des expressions régulières
class ManualInvoiceParser {
  /// Analyser un texte de commande et extraire les informations
  Map<String, dynamic> parseOrderText(String orderText) {
    try {
      if (orderText.trim().isEmpty) {
        throw Exception('Le texte de commande ne peut pas être vide');
      }

      if (orderText.trim().length < 10) {
        throw Exception(
          'Le texte de commande est trop court pour être analysé',
        );
      }

      final result = {
        'success': true,
        'confidence': 0.0,
        'client': <String, dynamic>{},
        'items': <Map<String, dynamic>>[],
        'delivery': <String, dynamic>{},
        'payment': <String, dynamic>{},
        'notes': '',
        'warnings': '',
        'extracted_keywords': <String>[],
        'processing_time': 0,
      };

      final startTime = DateTime.now();

      // Extraire les informations client
      final clientInfo = _extractClientInfo(orderText);
      result['client'] = clientInfo;

      // Extraire les articles
      final items = _extractItems(orderText);
      result['items'] = items;

      // Extraire les informations de livraison
      final deliveryInfo = _extractDeliveryInfo(orderText);
      result['delivery'] = deliveryInfo;

      // Extraire les informations de paiement
      final paymentInfo = _extractPaymentInfo(orderText, items);
      result['payment'] = paymentInfo;

      // Extraire les notes et avertissements
      result['notes'] = _extractNotes(orderText);
      result['warnings'] = _extractWarnings(orderText);

      // Calculer le niveau de confiance
      result['confidence'] = _calculateConfidence(result);

      // Temps de traitement
      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;
      result['processing_time'] = processingTime;
      result['analysis_timestamp'] = DateTime.now().toIso8601String();

      return result;
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse manuelle: $e');
      return {
        'success': false,
        'error': 'Erreur d\'analyse: ${e.toString()}',
        'confidence': 0.0,
        'processing_time': 0,
        'analysis_timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Extraire les informations client
  Map<String, dynamic> _extractClientInfo(String text) {
    final clientInfo = <String, dynamic>{};

    // Patterns pour le nom
    final namePatterns = [
      RegExp(r'\*Nom\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Nom\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Client\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Mme\s+([A-Za-zÀ-ÖØ-öø-ÿ\s]+)', caseSensitive: false),
      RegExp(r'M\.\s+([A-Za-zÀ-ÖØ-öø-ÿ\s]+)', caseSensitive: false),
    ];

    // Patterns pour le téléphone
    final phonePatterns = [
      RegExp(r'\*Numéro\*\s*:?\s*([+]?[0-9\s-]{8,15})', caseSensitive: false),
      RegExp(
        r'\*Téléphone\*\s*:?\s*([+]?[0-9\s-]{8,15})',
        caseSensitive: false,
      ),
      RegExp(r'Numéro\s*:?\s*([+]?[0-9\s-]{8,15})', caseSensitive: false),
      RegExp(r'Téléphone\s*:?\s*([+]?[0-9\s-]{8,15})', caseSensitive: false),
      RegExp(r'Tel\s*:?\s*([+]?[0-9\s-]{8,15})', caseSensitive: false),
      RegExp(r'([+]?225[0-9\s-]{8,12})', caseSensitive: false),
      RegExp(r'(0[0-9\s-]{8,12})', caseSensitive: false),
    ];

    // Extraire le nom
    for (final pattern in namePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        clientInfo['name'] = match.group(1)!.trim();
        break;
      }
    }

    // Extraire le téléphone
    for (final pattern in phonePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        clientInfo['phone'] = _formatPhoneNumber(match.group(1)!.trim());
        break;
      }
    }

    return clientInfo;
  }

  /// Extraire les articles
  List<Map<String, dynamic>> _extractItems(String text) {
    final items = <Map<String, dynamic>>[];

    // Patterns pour les articles
    final itemPatterns = [
      RegExp(r'\*Article demandé\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'\*Article\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Article\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Produit\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Commande\s*:?\s*([^\n\r*]+)', caseSensitive: false),
    ];

    // Patterns pour les messages de personnalisation
    final messagePatterns = [
      RegExp(r'\*Message\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'\*Message\s+(\d+)\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Message\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Personnalisation\s*:?\s*([^\n\r*]+)', caseSensitive: false),
    ];

    // Extraire l'article principal
    String? productName;
    for (final pattern in itemPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        productName = match.group(1)!.trim();
        break;
      }
    }

    if (productName != null && productName.isNotEmpty) {
      // Extraire les messages de personnalisation
      final customMessages = <String>[];
      for (final pattern in messagePatterns) {
        final matches = pattern.allMatches(text);
        for (final match in matches) {
          if (match.group(1) != null) {
            customMessages.add(match.group(1)!.trim());
          } else if (match.group(2) != null) {
            customMessages.add(match.group(2)!.trim());
          }
        }
      }

      // Déterminer le prix et la catégorie basés sur le nom du produit
      final priceAndCategory = _estimatePriceAndCategory(productName);

      items.add({
        'product_id': '',
        'product_name': productName,
        'category_id': priceAndCategory['category_id'],
        'category_name': priceAndCategory['category_name'],
        'price': priceAndCategory['price'],
        'quantity': _extractQuantity(text, productName),
        'description':
            customMessages.isNotEmpty
                ? 'Messages personnalisés: ${customMessages.join(' | ')}'
                : productName,
        'customization_messages': customMessages,
      });
    }

    return items;
  }

  /// Extraire la quantité
  int _extractQuantity(String text, String productName) {
    // Patterns pour la quantité
    final quantityPatterns = [
      RegExp(
        r'(\d+)\s*x\s*' + RegExp.escape(productName),
        caseSensitive: false,
      ),
      RegExp(r'(\d+)\s*' + RegExp.escape(productName), caseSensitive: false),
      RegExp(r'Quantité\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Qté\s*:?\s*(\d+)', caseSensitive: false),
    ];

    for (final pattern in quantityPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        return int.tryParse(match.group(1)!) ?? 1;
      }
    }

    return 1; // Quantité par défaut
  }

  /// Extraire les informations de livraison
  Map<String, dynamic> _extractDeliveryInfo(String text) {
    final deliveryInfo = <String, dynamic>{};

    // Patterns pour le lieu de livraison
    final locationPatterns = [
      RegExp(
        r'\*Le lieu de livraison\*\s*:?\s*([^\n\r*]+)',
        caseSensitive: false,
      ),
      RegExp(r'\*Livraison\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Lieu de livraison\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Livraison\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Adresse\s*:?\s*([^\n\r*]+)', caseSensitive: false),
    ];

    // Patterns pour les frais de livraison
    final deliveryCostPatterns = [
      RegExp(r'\*Frais de livraison\*\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Frais de livraison\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Livraison\s*:?\s*(\d+)\s*FCFA', caseSensitive: false),
    ];

    // Extraire le lieu
    for (final pattern in locationPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        final location = match.group(1)!.trim();
        deliveryInfo['details'] = location;
        deliveryInfo['location'] = _parseDeliveryLocation(location);
        break;
      }
    }

    // Extraire les frais de livraison
    for (final pattern in deliveryCostPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        deliveryInfo['delivery_cost'] = double.tryParse(match.group(1)!) ?? 0.0;
        break;
      }
    }

    // Frais de livraison par défaut si non spécifié
    if (!deliveryInfo.containsKey('delivery_cost')) {
      deliveryInfo['delivery_cost'] = 0.0;
    }

    return deliveryInfo;
  }

  /// Extraire les informations de paiement
  Map<String, dynamic> _extractPaymentInfo(
    String text,
    List<Map<String, dynamic>> items,
  ) {
    final paymentInfo = <String, dynamic>{};

    // Patterns pour l'avance
    final advancePatterns = [
      RegExp(r'\*Avance\*\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Avance\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Acompte\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Versement\s*:?\s*(\d+)', caseSensitive: false),
    ];

    // Patterns pour le reste à payer
    final remainingPatterns = [
      RegExp(r'\*Reste à payer\*\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Reste à payer\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Solde\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Reste\s*:?\s*(\d+)', caseSensitive: false),
    ];

    // Patterns pour le total
    final totalPatterns = [
      RegExp(r'\*Total\*\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Total\s*:?\s*(\d+)', caseSensitive: false),
      RegExp(r'Montant total\s*:?\s*(\d+)', caseSensitive: false),
    ];

    // Extraire l'avance
    double advance = 0.0;
    for (final pattern in advancePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        advance = double.tryParse(match.group(1)!) ?? 0.0;
        break;
      }
    }

    // Extraire le reste à payer
    double remaining = 0.0;
    for (final pattern in remainingPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        remaining = double.tryParse(match.group(1)!) ?? 0.0;
        break;
      }
    }

    // Extraire le total ou le calculer
    double total = 0.0;
    for (final pattern in totalPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        total = double.tryParse(match.group(1)!) ?? 0.0;
        break;
      }
    }

    // Si le total n'est pas trouvé, le calculer à partir des articles
    if (total == 0.0) {
      for (final item in items) {
        final price = (item['price'] as num?)?.toDouble() ?? 0.0;
        final quantity = (item['quantity'] as num?)?.toInt() ?? 1;
        total += price * quantity;
      }
    }

    // Si le total n'est toujours pas défini, l'estimer à partir de l'avance et du reste
    if (total == 0.0 && (advance > 0 || remaining > 0)) {
      total = advance + remaining;
    }

    paymentInfo['advance'] = advance;
    paymentInfo['remaining'] = remaining;
    paymentInfo['total'] = total;
    paymentInfo['method'] = 'CASH'; // Par défaut
    paymentInfo['delivery_cost'] =
        0.0; // Sera mis à jour par les infos de livraison
    paymentInfo['final_total'] =
        total; // Sera mis à jour avec les frais de livraison

    return paymentInfo;
  }

  /// Extraire les notes
  String _extractNotes(String text) {
    final notePatterns = [
      RegExp(r'\*Note\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'\*Remarque\*\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Note\s*:?\s*([^\n\r*]+)', caseSensitive: false),
      RegExp(r'Remarque\s*:?\s*([^\n\r*]+)', caseSensitive: false),
    ];

    for (final pattern in notePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        return match.group(1)!.trim();
      }
    }

    return '';
  }

  /// Extraire les avertissements
  String _extractWarnings(String text) {
    final warningKeywords = [
      'urgent',
      'attention',
      'important',
      'appeler',
      'vérifier',
      'confirmer',
      'problème',
      'erreur',
      'modification',
    ];

    final warnings = <String>[];
    for (final keyword in warningKeywords) {
      if (text.toLowerCase().contains(keyword)) {
        warnings.add('Contient: $keyword');
      }
    }

    return warnings.join(', ');
  }

  /// Formater le numéro de téléphone
  String _formatPhoneNumber(String phone) {
    // Nettoyer le numéro
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // Formats ivoiriens
    if (cleaned.startsWith('0') && cleaned.length == 10) {
      return '+225${cleaned.substring(1)}';
    } else if (cleaned.startsWith('+225')) {
      return cleaned;
    } else if (cleaned.length == 8) {
      return '+225$cleaned';
    }

    return cleaned;
  }

  /// Estimer le prix et la catégorie d'un produit
  Map<String, dynamic> _estimatePriceAndCategory(String productName) {
    final name = productName.toLowerCase();

    // Logique d'estimation basée sur les mots-clés
    if (name.contains('premium') || name.contains('magsafe')) {
      return {
        'price': 10000.0,
        'category_id': 'premium',
        'category_name': 'Premium',
      };
    } else if (name.contains('vip')) {
      return {'price': 6000.0, 'category_id': 'vip', 'category_name': 'VIP'};
    } else if (name.contains('fullpicture')) {
      return {
        'price': 4500.0,
        'category_id': 'fullpicture',
        'category_name': 'Full Picture',
      };
    } else if (name.contains('coque')) {
      return {
        'price': 5000.0,
        'category_id': 'coque',
        'category_name': 'Coque',
      };
    } else if (name.contains('tasse')) {
      return {
        'price': 3000.0,
        'category_id': 'tasse',
        'category_name': 'Tasse',
      };
    }

    // Prix par défaut
    return {'price': 5000.0, 'category_id': 'autre', 'category_name': 'Autre'};
  }

  /// Parser le lieu de livraison
  String _parseDeliveryLocation(String locationStr) {
    final location = locationStr.toLowerCase();

    if (location.contains('cocody')) {
      return 'COCODY';
    } else if (location.contains('marcory')) {
      return 'MARCORY';
    } else if (location.contains('yopougon')) {
      return 'YOPOUGON';
    } else if (location.contains('bingerville')) {
      return 'BINGERVILLE';
    } else if (location.contains('magasin') || location.contains('boutique')) {
      return 'MAGASIN';
    } else if (location.contains('domicile') || location.contains('maison')) {
      return 'DOMICILE';
    } else if (location.contains('appeler') ||
        location.contains('téléphoner')) {
      return 'APPELER_CLIENT';
    }

    return 'AUTRE';
  }

  /// Calculer le niveau de confiance
  double _calculateConfidence(Map<String, dynamic> data) {
    double confidence = 0.3; // Base pour l'analyse manuelle

    final client = data['client'] as Map<String, dynamic>? ?? {};
    final items = data['items'] as List<dynamic>? ?? [];
    final payment = data['payment'] as Map<String, dynamic>? ?? {};

    // Bonus pour les informations client
    if (client['name'] != null && (client['name'] as String).isNotEmpty) {
      confidence += 0.2;
    }
    if (client['phone'] != null && (client['phone'] as String).isNotEmpty) {
      confidence += 0.2;
    }

    // Bonus pour les articles
    if (items.isNotEmpty) {
      confidence += 0.15;

      for (final item in items) {
        final itemMap = item as Map<String, dynamic>;
        if (itemMap['product_name'] != null &&
            (itemMap['product_name'] as String).isNotEmpty) {
          confidence += 0.05;
        }
      }
    }

    // Bonus pour les informations de paiement
    if (((payment['total'] as num?)?.toDouble() ?? 0) > 0) {
      confidence += 0.1;
    }

    return confidence.clamp(0.0, 1.0);
  }
}
