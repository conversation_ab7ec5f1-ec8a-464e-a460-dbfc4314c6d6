import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ai/ai_message.dart';
import '../../models/ai/ai_provider.dart';
import '../../services/ai/ai_service.dart';
import '../../theme/neon_theme.dart';

// Provider pour les messages du chatbot
final chatbotMessagesProvider =
    StateNotifierProvider<ChatbotMessagesNotifier, List<AIMessage>>((ref) {
      return ChatbotMessagesNotifier();
    });

// Notifier pour gérer les messages du chatbot
class ChatbotMessagesNotifier extends StateNotifier<List<AIMessage>> {
  ChatbotMessagesNotifier()
    : super([
        AIMessage(
          role: AIMessageRole.system,
          content:
              'Je suis l\'assistant virtuel de HCP-DESIGN. Comment puis-je vous aider aujourd\'hui?',
        ),
      ]);

  void addUserMessage(String content) {
    state = [...state, AIMessage(role: AIMessageRole.user, content: content)];
  }

  void addAssistantMessage(String content) {
    state = [
      ...state,
      AIMessage(role: AIMessageRole.assistant, content: content),
    ];
  }

  void clearMessages() {
    state = [
      AIMessage(
        role: AIMessageRole.system,
        content:
            'Je suis l\'assistant virtuel de HCP-DESIGN. Comment puis-je vous aider aujourd\'hui?',
      ),
    ];
  }
}

class KnowledgeChatbotDialog extends ConsumerStatefulWidget {
  const KnowledgeChatbotDialog({super.key});

  @override
  ConsumerState<KnowledgeChatbotDialog> createState() =>
      _KnowledgeChatbotDialogState();
}

class _KnowledgeChatbotDialogState
    extends ConsumerState<KnowledgeChatbotDialog> {
  final TextEditingController _messageController = TextEditingController();
  bool _isLoading = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // Ajouter le message de l'utilisateur
    ref.read(chatbotMessagesProvider.notifier).addUserMessage(message);
    _messageController.clear();
    _scrollToBottom();

    setState(() {
      _isLoading = true;
    });

    try {
      // Générer une réponse avec l'IA
      final aiService = ref.read(aiServiceProvider);
      final messages = ref.read(chatbotMessagesProvider);

      // Construire le prompt avec le contexte de la base de connaissances
      const systemPrompt = '''
      Tu es un assistant virtuel pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

      Utilise la base de connaissances pour répondre aux questions des clients.
      Sois professionnel, concis et utile.
      Si tu ne connais pas la réponse, propose de transférer à un conseiller humain.
      ''';

      // Préparer les messages pour l'IA
      final aiMessages = [
        AIMessage(role: AIMessageRole.system, content: systemPrompt),
        ...messages.where((m) => m.role != AIMessageRole.system),
      ];

      // Vérifier si la configuration de l'IA est valide
      final config = ref.read(aiConfigProvider);
      if (config.apiKey.isEmpty) {
        throw Exception(
          "La clé API n'est pas configurée. Veuillez configurer l'IA dans les paramètres.",
        );
      }

      debugPrint(
        "Envoi de message à l'IA (${config.provider.displayName}, modèle: ${config.model})",
      );
      final response = await aiService.sendMessage(aiMessages);
      debugPrint("Réponse reçue de l'IA");

      // Ajouter la réponse de l'assistant
      ref.read(chatbotMessagesProvider.notifier).addAssistantMessage(response);
      _scrollToBottom();
    } catch (e) {
      // En cas d'erreur, ajouter un message d'erreur avec plus de détails
      debugPrint("Erreur lors de l'envoi du message à l'IA: $e");

      String errorMessage =
          'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.';

      // Ajouter des détails spécifiques selon le type d'erreur
      if (e.toString().contains("API")) {
        errorMessage +=
            "\n\nErreur de configuration API. Veuillez vérifier les paramètres de l'IA.";
      }

      ref
          .read(chatbotMessagesProvider.notifier)
          .addAssistantMessage(errorMessage);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final messages = ref.watch(chatbotMessagesProvider);

    return Dialog(
      backgroundColor: const Color(0xFF1A1A2E),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // En-tête
            Row(
              children: [
                const Icon(Icons.smart_toy, color: NeonTheme.primaryAccent),
                const SizedBox(width: 8),
                const Text(
                  'Assistant IA',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white70),
                  onPressed: () {
                    ref.read(chatbotMessagesProvider.notifier).clearMessages();
                  },
                  tooltip: 'Nouvelle conversation',
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white70),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: 'Fermer',
                ),
              ],
            ),
            const Divider(color: Colors.white24),

            // Messages
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF0F0F1E),
                  borderRadius: BorderRadius.circular(8),
                ),
                child:
                    messages.length <= 1
                        ? const Center(
                          child: Text(
                            'Posez une question à l\'IA...',
                            style: TextStyle(color: Colors.white54),
                          ),
                        )
                        : ListView.builder(
                          controller: _scrollController,
                          itemCount: messages.length,
                          itemBuilder: (context, index) {
                            final message = messages[index];

                            // Ne pas afficher le message système
                            if (message.role == AIMessageRole.system &&
                                index == 0) {
                              return const SizedBox.shrink();
                            }

                            return _buildMessageBubble(message);
                          },
                        ),
              ),
            ),

            // Indicateur de chargement
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          NeonTheme.primaryAccent,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'L\'IA réfléchit...',
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),

            // Champ de saisie
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      decoration: InputDecoration(
                        hintText: 'Tapez votre message...',
                        hintStyle: const TextStyle(color: Colors.white38),
                        filled: true,
                        fillColor: const Color(0xFF0F0F1E),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(color: Colors.white),
                      maxLines: 3,
                      minLines: 1,
                      onSubmitted: (_) => _sendMessage(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton(
                    mini: true,
                    backgroundColor: NeonTheme.primaryAccent,
                    onPressed: _isLoading ? null : _sendMessage,
                    child: const Icon(Icons.send, color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(AIMessage message) {
    final isUser = message.role == AIMessageRole.user;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser)
            Container(
              width: 28,
              height: 28,
              margin: const EdgeInsets.only(right: 8, top: 4),
              decoration: BoxDecoration(
                color: NeonTheme.primaryAccent,
                borderRadius: BorderRadius.circular(14),
              ),
              child: const Icon(Icons.smart_toy, color: Colors.white, size: 16),
            ),

          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color:
                    isUser
                        ? NeonTheme.primaryAccent.withAlpha(50)
                        : const Color(0xFF1A1A2E),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isUser
                          ? NeonTheme.primaryAccent.withAlpha(100)
                          : Colors.transparent,
                  width: 1,
                ),
              ),
              child: Text(
                message.content,
                style: TextStyle(
                  color: isUser ? Colors.white : Colors.white.withAlpha(220),
                ),
              ),
            ),
          ),

          if (isUser)
            Container(
              width: 28,
              height: 28,
              margin: const EdgeInsets.only(left: 8, top: 4),
              decoration: BoxDecoration(
                color: Colors.white24,
                borderRadius: BorderRadius.circular(14),
              ),
              child: const Icon(Icons.person, color: Colors.white, size: 16),
            ),
        ],
      ),
    );
  }
}
