import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/product/product.dart';
import '../../data/product_data.dart';

// Provider pour le service de produits
final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

// Provider pour la liste des produits
final productsProvider = FutureProvider<List<Product>>((ref) async {
  final productService = ref.watch(productServiceProvider);
  return productService.getProducts();
});

// Provider pour les produits en stock bas
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  final productService = ref.watch(productServiceProvider);
  return productService.getLowStockProducts();
});

// Provider pour les produits par catégorie
final productsByCategoryProvider = FutureProvider.family<List<Product>, String>(
  (ref, category) async {
    final productService = ref.watch(productServiceProvider);
    return productService.getProductsByCategory(category);
  },
);

class ProductService {
  // Liste en mémoire des produits (simulant une base de données)
  final List<Product> _products = [];

  ProductService() {
    // Ajouter quelques produits de test
    _addTestProducts();
  }

  // Méthode pour récupérer tous les produits
  Future<List<Product>> getProducts() async {
    // Simuler un délai de chargement
    await Future.delayed(const Duration(milliseconds: 800));

    // Retourner une copie de la liste pour éviter les modifications directes
    return [..._products];
  }

  // Méthode pour récupérer un produit par son ID
  Future<Product?> getProductById(String id) async {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour récupérer les produits par catégorie
  Future<List<Product>> getProductsByCategory(String category) async {
    return _products.where((product) => product.category == category).toList();
  }

  // Méthode pour récupérer les produits en stock bas
  Future<List<Product>> getLowStockProducts() async {
    return _products
        .where((product) => product.stockStatus == StockStatus.lowStock)
        .toList();
  }

  // Méthode pour ajouter un nouveau produit
  Future<void> addProduct(Product product) async {
    // Vérifier si l'ID existe déjà
    if (_products.any((p) => p.id == product.id)) {
      throw Exception('Un produit avec cet ID existe déjà');
    }

    _products.add(product);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour mettre à jour un produit existant
  Future<void> updateProduct(Product product) async {
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index == -1) {
      throw Exception('Produit non trouvé');
    }

    _products[index] = product;
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour supprimer un produit
  Future<void> deleteProduct(String id) async {
    final index = _products.indexWhere((p) => p.id == id);
    if (index == -1) {
      throw Exception('Produit non trouvé');
    }

    _products.removeAt(index);
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode pour rechercher des produits
  Future<List<Product>> searchProducts(String query) async {
    final lowercaseQuery = query.toLowerCase();

    return _products.where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
          product.description.toLowerCase().contains(lowercaseQuery) ||
          product.category.toLowerCase().contains(lowercaseQuery) ||
          (product.sku?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          (product.barcode?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // Méthode pour mettre à jour le stock d'un produit
  Future<void> updateStock(String id, int quantity) async {
    final product = await getProductById(id);
    if (product == null) {
      throw Exception('Produit non trouvé');
    }

    // Déterminer le nouveau statut de stock
    StockStatus newStatus;
    if (quantity <= 0) {
      newStatus = StockStatus.outOfStock;
    } else if (quantity < 5) {
      newStatus = StockStatus.lowStock;
    } else {
      newStatus = StockStatus.inStock;
    }

    final updatedProduct = product.copyWith(
      quantity: quantity,
      stockStatus: newStatus,
      updatedAt: DateTime.now(),
    );

    await updateProduct(updatedProduct);
  }

  // Ajouter des produits de test
  void _addTestProducts() {
    // Utiliser les produits avec les prix corrects
    _products.addAll(ProductData.getProducts());
  }
}
