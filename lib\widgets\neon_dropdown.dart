import 'package:flutter/material.dart';
import '../theme/neon_theme.dart';

class NeonDropdown<T> extends StatelessWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final Function(T?) onChanged;
  final String? hint;
  final Color? color;
  final double? width;

  const NeonDropdown({
    super.key,
    required this.value,
    required this.items,
    required this.onChanged,
    this.hint,
    this.color,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    final neonColor = color ?? NeonTheme.neonCyan;
    
    return Container(
      width: width,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: neonColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1,
        ),
        boxShadow: NeonTheme.neonShadow(neonColor, intensity: 0.3),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<T>(
          value: value,
          hint: hint != null
              ? Text(
                  hint!,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 128), // 0.5 * 255 ≈ 128
                  ),
                )
              : null,
          items: items,
          onChanged: onChanged,
          dropdownColor: Colors.black,
          style: const TextStyle(color: Colors.white),
          icon: Icon(
            Icons.arrow_drop_down,
            color: neonColor,
          ),
          isExpanded: true,
        ),
      ),
    );
  }
}
