import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/invoice.dart';
import '../../models/invoice_item.dart';
import '../../models/product_category.dart';
import '../inventory_service.dart';
import 'ai_service.dart';

// Provider pour le service d'analyse de facture
final invoiceAnalyzerServiceProvider = Provider<InvoiceAnalyzerService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  final inventoryService = ref.watch(inventoryServiceProvider);
  return InvoiceAnalyzerService(
    aiService: aiService,
    inventoryService: inventoryService,
  );
});

class InvoiceAnalyzerService {
  final AIService aiService;
  final InventoryService inventoryService;

  InvoiceAnalyzerService({
    required this.aiService,
    required this.inventoryService,
  });

  // Analyser un texte de commande et extraire les informations pour une facture
  Future<Map<String, dynamic>> analyzeOrderText(String orderText) async {
    try {
      // Validation de l'entrée
      if (orderText.trim().isEmpty) {
        throw Exception('Le texte de commande ne peut pas être vide');
      }

      if (orderText.trim().length < 10) {
        throw Exception(
          'Le texte de commande est trop court pour être analysé',
        );
      }

      // Récupérer tous les produits disponibles pour le contexte
      final products = await inventoryService.getProducts();
      final categories = await inventoryService.getCategories();

      // Construire une liste de produits pour le contexte
      String productsContext = 'Produits disponibles:\n';
      for (final product in products) {
        final categoryName = product.categoryName ?? 'Inconnue';
        productsContext +=
            '- ${product.name} ($categoryName): ${product.price} FCFA\n';
      }

      // Créer le prompt amélioré pour l'extraction d'informations
      final prompt = '''
      Tu es un assistant IA expert en analyse de commandes pour HCP-DESIGN, spécialisé dans les coques de téléphone personnalisées et autres produits personnalisés (tasses, etc.).

      CONTEXTE PRODUITS:
      $productsContext

      INSTRUCTIONS SPÉCIFIQUES:
      1. Analyse minutieusement le texte de commande ci-dessous
      2. Extrait TOUTES les informations pertinentes même si le format est non-standard
      3. Cherche les patterns suivants:
         - *Nom et Numéro* : ou "Nom :" suivi du nom et numéro
         - *Article demandé* : ou variations pour les produits
         - *Message* : ou *Message 1*, *Message 2* etc. pour les personnalisations
         - *Avance* : pour le montant payé
         - *Reste à payer* : pour le solde
         - *Le lieu de livraison* : pour l'adresse
      4. Détecte les numéros de téléphone (formats: +225XXXXXXXX, 0XXXXXXXXX, etc.)
      5. Identifie les quantités (ex: "4 tasses", "2x coques", etc.)
      6. Extrait les messages de personnalisation
      7. Calcule les totaux automatiquement
      8. Gère les frais de livraison séparément

      FORMATS SUPPORTÉS:
      - Textes avec astérisques (*Nom*, *Article*, etc.)
      - Textes avec émojis et caractères spéciaux
      - Messages de personnalisation multiples
      - Frais de livraison séparés
      - Instructions spéciales (appeler client, etc.)

      TEXTE DE COMMANDE:
      "$orderText"

      RÉPONSE REQUISE (JSON strict):
      {
        "success": true,
        "confidence": 0.85,
        "client": {
          "name": "Nom du client extrait",
          "phone": "Numéro de téléphone formaté"
        },
        "items": [
          {
            "product_id": "ID du produit si trouvé",
            "product_name": "Nom du produit détaillé",
            "category_id": "ID de la catégorie si trouvé",
            "category_name": "Nom de la catégorie",
            "price": prix_unitaire_numerique,
            "quantity": quantite_numerique,
            "description": "Description avec messages de personnalisation",
            "customization_messages": ["message1", "message2", "message3"]
          }
        ],
        "delivery": {
          "location": "MAGASIN|DOMICILE|APPELER_CLIENT|AUTRE",
          "details": "Adresse complète ou instructions de livraison",
          "delivery_cost": cout_livraison_numerique,
          "special_instructions": "Instructions spéciales (appeler client, etc.)"
        },
        "payment": {
          "total": montant_total_estime,
          "advance": montant_avance_paye,
          "remaining": montant_restant_sans_livraison,
          "delivery_cost": cout_livraison,
          "final_total": total_avec_livraison,
          "method": "CASH|MOBILE_MONEY|VIREMENT|AUTRE"
        },
        "notes": "Conditions particulières, avertissements, instructions spéciales",
        "warnings": "Avertissements importants extraits du texte",
        "extracted_keywords": ["mot-clé1", "mot-clé2"],
        "processing_time": temps_traitement_ms
      }
      ''';

      final startTime = DateTime.now();

      // Envoyer la requête à l'IA avec gestion d'erreur améliorée
      final response = await aiService.generateText(
        [],
        overrideSystemPrompt: prompt,
      );

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;

      // Extraire et nettoyer le JSON de la réponse
      String jsonStr = _extractJsonFromResponse(response);

      // Décoder le JSON avec validation
      final Map<String, dynamic> extractedData = _validateAndParseJson(jsonStr);

      // Ajouter des métadonnées de traitement
      extractedData['processing_time'] = processingTime;
      extractedData['original_text_length'] = orderText.length;
      extractedData['analysis_timestamp'] = DateTime.now().toIso8601String();

      // Post-traitement et validation des données
      return _postProcessAnalysisData(extractedData, products, categories);
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse du texte de commande: $e');
      return {
        'success': false,
        'error': 'Erreur d\'analyse: ${e.toString()}',
        'confidence': 0.0,
        'processing_time': 0,
        'analysis_timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  // Extraire le JSON de la réponse IA
  String _extractJsonFromResponse(String response) {
    // Chercher le JSON dans la réponse
    final jsonStart = response.indexOf('{');
    final jsonEnd = response.lastIndexOf('}');

    if (jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd) {
      throw Exception('Aucun JSON valide trouvé dans la réponse IA');
    }

    return response.substring(jsonStart, jsonEnd + 1);
  }

  // Valider et parser le JSON
  Map<String, dynamic> _validateAndParseJson(String jsonStr) {
    try {
      final data = jsonDecode(jsonStr) as Map<String, dynamic>;

      // Validation des champs obligatoires
      if (!data.containsKey('client') || !data.containsKey('items')) {
        throw Exception('Champs obligatoires manquants dans la réponse IA');
      }

      return data;
    } catch (e) {
      throw Exception('JSON invalide: ${e.toString()}');
    }
  }

  // Post-traitement des données analysées
  Map<String, dynamic> _postProcessAnalysisData(
    Map<String, dynamic> data,
    List<dynamic> products,
    List<ProductCategory> categories,
  ) {
    // Valider et corriger les informations client
    final client = data['client'] as Map<String, dynamic>? ?? {};
    if (client['phone'] != null) {
      client['phone'] = _formatPhoneNumber(client['phone'] as String);
    }

    // Valider et enrichir les articles
    final items = data['items'] as List<dynamic>? ?? [];
    for (int i = 0; i < items.length; i++) {
      final item = items[i] as Map<String, dynamic>;

      // Valider les prix et quantités
      if (item['price'] == null || item['price'] == 0) {
        item['price'] = _estimatePrice(
          item['product_name'] as String?,
          categories,
        );
      }

      if (item['quantity'] == null || item['quantity'] == 0) {
        item['quantity'] = 1;
      }

      // Assurer que les valeurs numériques sont correctes
      item['price'] = (item['price'] as num).toDouble();
      item['quantity'] = (item['quantity'] as num).toInt();
    }

    // Calculer les totaux si manquants
    final payment = data['payment'] as Map<String, dynamic>? ?? {};
    final delivery = data['delivery'] as Map<String, dynamic>? ?? {};

    if (payment['total'] == null || payment['total'] == 0) {
      double total = 0;
      for (final item in items) {
        final itemMap = item as Map<String, dynamic>;
        total += (itemMap['price'] as double) * (itemMap['quantity'] as int);
      }
      payment['total'] = total;
    }

    // Gérer les frais de livraison
    final deliveryCost =
        (delivery['delivery_cost'] as num?)?.toDouble() ??
        (payment['delivery_cost'] as num?)?.toDouble() ??
        0;
    delivery['delivery_cost'] = deliveryCost;
    payment['delivery_cost'] = deliveryCost;

    // Calculer les montants
    final advance = (payment['advance'] as num?)?.toDouble() ?? 0;
    final total = (payment['total'] as num?)?.toDouble() ?? 0;
    final remaining =
        (payment['remaining'] as num?)?.toDouble() ?? (total - advance);
    final finalTotal = total + deliveryCost;

    payment['remaining'] = remaining;
    payment['final_total'] = finalTotal;

    // Gérer les messages de personnalisation
    for (int i = 0; i < items.length; i++) {
      final item = items[i] as Map<String, dynamic>;
      final customMessages =
          item['customization_messages'] as List<dynamic>? ?? [];

      // Combiner les messages dans la description si elle est vide
      if ((item['description'] as String? ?? '').isEmpty &&
          customMessages.isNotEmpty) {
        item['description'] =
            'Messages personnalisés: ${customMessages.join(' | ')}';
      }
    }

    // Assurer un niveau de confiance minimum
    if (data['confidence'] == null) {
      data['confidence'] = _calculateConfidence(data);
    }

    return data;
  }

  // Formater le numéro de téléphone
  String _formatPhoneNumber(String phone) {
    // Nettoyer le numéro
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');

    // Formats ivoiriens
    if (cleaned.startsWith('0') && cleaned.length == 10) {
      return '+225${cleaned.substring(1)}';
    } else if (cleaned.startsWith('+225')) {
      return cleaned;
    } else if (cleaned.length == 8) {
      return '+225$cleaned';
    }

    return cleaned;
  }

  // Estimer le prix d'un produit
  double _estimatePrice(String? productName, List<ProductCategory> categories) {
    if (productName == null) return 5000; // Prix par défaut

    final name = productName.toLowerCase();

    // Logique d'estimation basée sur les mots-clés
    if (name.contains('premium') || name.contains('magsafe')) {
      return 10000;
    } else if (name.contains('vip')) {
      return 6000;
    } else if (name.contains('fullpicture')) {
      return 4500;
    }

    // Utiliser le prix moyen des catégories
    if (categories.isNotEmpty) {
      final avgPrice =
          categories.map((c) => c.price).reduce((a, b) => a + b) /
          categories.length;
      return avgPrice;
    }

    return 5000; // Prix par défaut
  }

  // Calculer le niveau de confiance
  double _calculateConfidence(Map<String, dynamic> data) {
    double confidence = 0.5; // Base

    final client = data['client'] as Map<String, dynamic>? ?? {};
    final items = data['items'] as List<dynamic>? ?? [];

    // Bonus pour les informations client
    if (client['name'] != null && (client['name'] as String).isNotEmpty) {
      confidence += 0.2;
    }
    if (client['phone'] != null && (client['phone'] as String).isNotEmpty) {
      confidence += 0.2;
    }

    // Bonus pour les articles
    if (items.isNotEmpty) {
      confidence += 0.1;

      for (final item in items) {
        final itemMap = item as Map<String, dynamic>;
        if (itemMap['product_name'] != null &&
            (itemMap['product_name'] as String).isNotEmpty) {
          confidence += 0.05;
        }
      }
    }

    return confidence.clamp(0.0, 1.0);
  }

  // Convertir les données extraites en objet Invoice
  Future<Invoice> createInvoiceFromAnalysis(
    Map<String, dynamic> analysisData,
  ) async {
    // Extraire les informations client
    final clientInfo = analysisData['client'] as Map<String, dynamic>? ?? {};
    final clientName = clientInfo['name'] as String? ?? 'Client inconnu';
    final clientPhone = clientInfo['phone'] as String? ?? '';

    // Extraire les articles
    final List<dynamic> rawItems =
        analysisData['items'] as List<dynamic>? ?? [];
    final List<InvoiceItem> items = [];

    for (final item in rawItems) {
      if (item is Map<String, dynamic>) {
        items.add(
          InvoiceItem(
            id: const Uuid().v4(),
            productId: item['product_id'] as String? ?? '',
            productName: item['product_name'] as String? ?? 'Produit inconnu',
            categoryId: item['category_id'] as String? ?? '',
            categoryName:
                item['category_name'] as String? ?? 'Catégorie inconnue',
            price:
                (item['price'] is num
                    ? (item['price'] as num).toDouble()
                    : 0.0),
            quantity:
                item['quantity'] is num ? (item['quantity'] as num).toInt() : 1,
          ),
        );
      }
    }

    // Extraire les informations de livraison
    final deliveryInfo =
        analysisData['delivery'] as Map<String, dynamic>? ?? {};
    final deliveryLocationStr =
        deliveryInfo['location'] as String? ?? 'MAGASIN';
    final DeliveryLocation deliveryLocation = _parseDeliveryLocation(
      deliveryLocationStr,
    );
    final deliveryDetails = deliveryInfo['details'] as String?;

    // Extraire les informations de paiement
    final paymentInfo = analysisData['payment'] as Map<String, dynamic>? ?? {};
    final advancePayment =
        paymentInfo['advance'] is num
            ? (paymentInfo['advance'] as num).toDouble()
            : 0.0;
    final paymentType = paymentInfo['method'] as String?;

    // Créer l'objet Invoice
    return Invoice(
      id: const Uuid().v4(),
      number: DateTime.now().millisecondsSinceEpoch.toString(),
      clientName: clientName,
      clientPhone: clientPhone,
      items: items,
      deliveryLocation: deliveryLocation,
      deliveryDetails: deliveryDetails,
      advancePayment: advancePayment,
      paymentType: paymentType,
      status: InvoiceStatus.paid,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Convertir une chaîne en DeliveryLocation
  DeliveryLocation _parseDeliveryLocation(String locationStr) {
    switch (locationStr.toUpperCase()) {
      case 'DOMICILE':
        return DeliveryLocation
            .cocody; // Utiliser une valeur existante comme approximation
      case 'AUTRE':
        return DeliveryLocation
            .marcory; // Utiliser une valeur existante comme approximation
      case 'MAGASIN':
      default:
        return DeliveryLocation
            .bingerville; // Utiliser une valeur existante comme approximation
    }
  }
}
