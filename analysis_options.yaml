# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
    prefer_const_constructors: true
    prefer_const_declarations: true
    avoid_empty_else: true
    avoid_relative_lib_imports: true
    avoid_types_as_parameter_names: true
    cancel_subscriptions: true
    close_sinks: true
    control_flow_in_finally: true
    empty_statements: true
    hash_and_equals: true
    iterable_contains_unrelated_type: true
    list_remove_unrelated_type: true
    no_duplicate_case_values: true
    unrelated_type_equality_checks: true
    valid_regexps: true
    use_build_context_synchronously: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_nullable_for_final_variable_declarations: true
    unnecessary_string_escapes: true
    unnecessary_string_interpolations: true
    unnecessary_this: true
    use_full_hex_values_for_flutter_colors: true
    use_key_in_widget_constructors: true
    use_rethrow_when_possible: true

analyzer:
  errors:
    # Treat missing required parameters as a warning (not a hint)
    file_names: ignore
    missing_required_param: warning
    # Treat missing returns as a warning (not a hint)
    missing_return: warning
    # Allow having TODOs in the code
    todo: ignore
    # Add additional error checks
    deprecated_member_use: warning
    invalid_use_of_protected_member: warning
    invalid_use_of_visible_for_testing_member: warning
    always_use_package_imports: warning

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
