Write-Host "Fixing flutter_sms AndroidManifest.xml..."

$manifestFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\src\main\AndroidManifest.xml"

if (-not (Test-Path $manifestFile)) {
    Write-Host "File not found: $manifestFile"
    exit 1
}

Write-Host "Original AndroidManifest.xml content:"
Get-Content $manifestFile

Write-Host ""
Write-Host "Removing package attribute from AndroidManifest.xml..."

$content = Get-Content $manifestFile -Raw
$updatedContent = $content -replace 'package="com.example.flutter_sms"', ''

$updatedContent | Set-Content $manifestFile

Write-Host ""
Write-Host "Modified AndroidManifest.xml content:"
Get-Content $manifestFile

Write-Host ""
Write-Host "Fix completed!"
