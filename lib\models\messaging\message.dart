import 'dart:convert';

// Statut du message
enum MessageStatus {
  sending, // En cours d'envoi
  sent, // Envoy<PERSON>
  delivered, // <PERSON><PERSON>
  read, // <PERSON>
  failed, // Échec
}

// Type de message
enum MessageType {
  text, // Texte
  image, // Image
  audio, // Audio
  video, // Vidéo
  file, // Fichier
  location, // Localisation
  contact, // Contact
  template, // Template (pour WhatsApp)
  system, // Message système
  document, // Document
}

// Canal de messagerie
enum MessageChannel {
  whatsapp, // WhatsApp
  facebook, // Facebook Messenger
  instagram, // Instagram Direct
  email, // Email
  sms, // SMS
  internal, // Messagerie interne
  app, // Application
  telegram, // Telegram
}

class Message {
  final String id;
  final String contactId;
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final String? mediaUrl;
  final MessageType? mediaType;
  final String? mediaName;
  final int? mediaSize;
  final MessageChannel channel;
  final MessageStatus status;
  final bool isAIGenerated;
  final int? replyToId;
  final Map<String, dynamic>? metadata;

  // Propriétés supplémentaires pour compatibilité avec les nouveaux services
  String get conversationId => id.split('_').first;
  bool get isRead => status == MessageStatus.read;
  String get sender => isUser ? 'user' : contactId;
  String get recipient => isUser ? contactId : 'business';

  Message({
    required this.id,
    required this.contactId,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.mediaUrl,
    this.mediaType,
    this.mediaName,
    this.mediaSize,
    required this.channel,
    required this.status,
    this.isAIGenerated = false,
    this.replyToId,
    this.metadata,
  });

  Message copyWith({
    String? id,
    String? contactId,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    String? mediaUrl,
    MessageType? mediaType,
    String? mediaName,
    int? mediaSize,
    MessageChannel? channel,
    MessageStatus? status,
    bool? isAIGenerated,
    int? replyToId,
    Map<String, dynamic>? metadata,
  }) {
    return Message(
      id: id ?? this.id,
      contactId: contactId ?? this.contactId,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaType: mediaType ?? this.mediaType,
      mediaName: mediaName ?? this.mediaName,
      mediaSize: mediaSize ?? this.mediaSize,
      channel: channel ?? this.channel,
      status: status ?? this.status,
      isAIGenerated: isAIGenerated ?? this.isAIGenerated,
      replyToId: replyToId ?? this.replyToId,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'contactId': contactId,
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType?.index,
      'mediaName': mediaName,
      'mediaSize': mediaSize,
      'channel': channel.index,
      'status': status.index,
      'isAIGenerated': isAIGenerated,
      'replyToId': replyToId,
      'metadata': metadata,
    };
  }

  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'] ?? '',
      contactId: map['contactId'] ?? '',
      content: map['content'] ?? '',
      isUser: map['isUser'] ?? false,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      mediaUrl: map['mediaUrl'],
      mediaType:
          map['mediaType'] != null
              ? MessageType.values[map['mediaType']]
              : null,
      mediaName: map['mediaName'],
      mediaSize: map['mediaSize'],
      channel: MessageChannel.values[map['channel']],
      status: MessageStatus.values[map['status']],
      isAIGenerated: map['isAIGenerated'] ?? false,
      replyToId: map['replyToId'],
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  String toJson() => json.encode(toMap());

  factory Message.fromJson(String source) =>
      Message.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Message(id: $id, contactId: $contactId, content: $content, isUser: $isUser, timestamp: $timestamp, mediaUrl: $mediaUrl, mediaType: $mediaType, mediaName: $mediaName, mediaSize: $mediaSize, channel: $channel, status: $status, isAIGenerated: $isAIGenerated, replyToId: $replyToId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Message &&
        other.id == id &&
        other.contactId == contactId &&
        other.content == content &&
        other.isUser == isUser &&
        other.timestamp == timestamp &&
        other.mediaUrl == mediaUrl &&
        other.mediaType == mediaType &&
        other.mediaName == mediaName &&
        other.mediaSize == mediaSize &&
        other.channel == channel &&
        other.status == status &&
        other.isAIGenerated == isAIGenerated &&
        other.replyToId == replyToId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        contactId.hashCode ^
        content.hashCode ^
        isUser.hashCode ^
        timestamp.hashCode ^
        mediaUrl.hashCode ^
        mediaType.hashCode ^
        mediaName.hashCode ^
        mediaSize.hashCode ^
        channel.hashCode ^
        status.hashCode ^
        isAIGenerated.hashCode ^
        replyToId.hashCode;
  }
}
