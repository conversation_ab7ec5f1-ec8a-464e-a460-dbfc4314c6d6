import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/user.dart';
import '../../models/user_role.dart';
import '../../services/user_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';
import '../../widgets/user_avatar.dart';

class UserManagementScreen extends ConsumerStatefulWidget {
  const UserManagementScreen({super.key});

  @override
  ConsumerState<UserManagementScreen> createState() =>
      _UserManagementScreenState();
}

class _UserManagementScreenState extends ConsumerState<UserManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Utilisateurs'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: NeonTheme.neonPurple,
          tabs: const [Tab(text: 'Utilisateurs'), Tab(text: 'Rôles')],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                  controller: _tabController,
                  children: [_buildUsersTab(), _buildRolesTab()],
                ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: NeonTheme.neonPurple,
        onPressed: () {
          if (_tabController.index == 0) {
            _showAddUserDialog(context);
          } else {
            _showAddRoleDialog(context);
          }
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildUsersTab() {
    final usersAsync = ref.watch(usersProvider);

    return usersAsync.when(
      data: (users) {
        if (users.isEmpty) {
          return const Center(
            child: Text(
              'Aucun utilisateur trouvé',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: users.length,
          itemBuilder: (context, index) {
            final user = users[index];
            return _buildUserCard(user);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Text(
              'Erreur: $error',
              style: const TextStyle(color: Colors.white),
            ),
          ),
    );
  }

  Widget _buildUserCard(User user) {
    return NeonCard(
      color: NeonTheme.neonPurple,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            UserAvatar(imageUrl: user.avatar, name: user.name, size: 60),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: const TextStyle(color: Colors.white70),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getRoleColor(
                        user.role,
                      ).withValues(alpha: 51), // 0.2 * 255 ≈ 51
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: _getRoleColor(user.role)),
                    ),
                    child: Text(
                      user.role,
                      style: TextStyle(
                        color: _getRoleColor(user.role),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.white),
              onPressed: () => _showEditUserDialog(context, user),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteUserDialog(context, user),
            ),
          ],
        ),
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.orange;
      case 'employee':
        return Colors.green;
      default:
        return Colors.blue;
    }
  }

  Widget _buildRolesTab() {
    final rolesAsync = ref.watch(userRolesProvider);

    return rolesAsync.when(
      data: (roles) {
        if (roles.isEmpty) {
          return const Center(
            child: Text(
              'Aucun rôle trouvé',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: roles.length,
          itemBuilder: (context, index) {
            final role = roles[index];
            return _buildRoleCard(role);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Text(
              'Erreur: $error',
              style: const TextStyle(color: Colors.white),
            ),
          ),
    );
  }

  Widget _buildRoleCard(UserRole role) {
    return NeonCard(
      color: NeonTheme.neonCyan,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        role.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (role.description != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          role.description!,
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ],
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.white),
                  onPressed: () => _showEditRoleDialog(context, role),
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _showDeleteRoleDialog(context, role),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Permissions:',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  role.permissions.map((permission) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 77,
                        ), // 0.3 * 255 ≈ 77
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: NeonTheme.neonCyan),
                      ),
                      child: Text(
                        permission,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddUserDialog(BuildContext context) {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    String selectedRole = 'user';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Ajouter un Utilisateur',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonTextField(controller: nameController, labelText: 'Nom'),
                const SizedBox(height: 16),
                NeonTextField(
                  controller: emailController,
                  labelText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),
                NeonTextField(
                  controller: passwordController,
                  labelText: 'Mot de passe',
                  obscureText: true,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<String>(
                      value: selectedRole,
                      decoration: InputDecoration(
                        labelText: 'Rôle',
                        labelStyle: const TextStyle(color: Colors.white),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: NeonTheme.neonPurple,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                      ),
                      dropdownColor: Colors.black,
                      style: const TextStyle(color: Colors.white),
                      items:
                          ['admin', 'manager', 'employee', 'user'].map((role) {
                            return DropdownMenuItem<String>(
                              value: role,
                              child: Text(role),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedRole = value;
                          });
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.isEmpty ||
                    emailController.text.isEmpty ||
                    passwordController.text.isEmpty) {
                  return;
                }

                final user = User(
                  id: const Uuid().v4(),
                  name: nameController.text,
                  email: emailController.text,
                  role: selectedRole,
                  password: passwordController.text,
                );

                setState(() {
                  _isLoading = true;
                });

                final userService = ref.read(userServiceProvider);
                final success = await userService.addUser(user);

                setState(() {
                  _isLoading = false;
                });

                if (success) {
                  // Rafraîchir la liste des utilisateurs
                  final _ = ref.refresh(usersProvider);
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }
              },
              child: const Text(
                'Ajouter',
                style: TextStyle(color: NeonTheme.neonPurple),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showEditUserDialog(BuildContext context, User user) {
    final nameController = TextEditingController(text: user.name);
    final emailController = TextEditingController(text: user.email);
    String selectedRole = user.role;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Modifier l\'Utilisateur',
            style: TextStyle(color: Colors.white),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NeonTextField(controller: nameController, labelText: 'Nom'),
                const SizedBox(height: 16),
                NeonTextField(
                  controller: emailController,
                  labelText: 'Email',
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 16),
                StatefulBuilder(
                  builder: (context, setState) {
                    return DropdownButtonFormField<String>(
                      value: selectedRole,
                      decoration: InputDecoration(
                        labelText: 'Rôle',
                        labelStyle: const TextStyle(color: Colors.white),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: NeonTheme.neonPurple,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                      ),
                      dropdownColor: Colors.black,
                      style: const TextStyle(color: Colors.white),
                      items:
                          ['admin', 'manager', 'employee', 'user'].map((role) {
                            return DropdownMenuItem<String>(
                              value: role,
                              child: Text(role),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedRole = value;
                          });
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                if (nameController.text.isEmpty ||
                    emailController.text.isEmpty) {
                  return;
                }

                final updatedUser = user.copyWith(
                  name: nameController.text,
                  email: emailController.text,
                  role: selectedRole,
                );

                setState(() {
                  _isLoading = true;
                });

                final userService = ref.read(userServiceProvider);
                final success = await userService.updateUser(updatedUser);

                setState(() {
                  _isLoading = false;
                });

                if (success) {
                  // Rafraîchir la liste des utilisateurs
                  final _ = ref.refresh(usersProvider);
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }
              },
              child: const Text(
                'Mettre à jour',
                style: TextStyle(color: NeonTheme.neonPurple),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteUserDialog(BuildContext context, User user) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Confirmer la suppression',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer l\'utilisateur ${user.name} ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                setState(() {
                  _isLoading = true;
                });

                final userService = ref.read(userServiceProvider);
                final success = await userService.deleteUser(user.id);

                setState(() {
                  _isLoading = false;
                });

                if (success) {
                  // Rafraîchir la liste des utilisateurs
                  final _ = ref.refresh(usersProvider);
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showAddRoleDialog(BuildContext context) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    UserRoleType selectedType = UserRoleType.user;
    final selectedPermissions = <String>[];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.black,
              title: const Text(
                'Ajouter un Rôle',
                style: TextStyle(color: Colors.white),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NeonTextField(
                      controller: nameController,
                      labelText: 'Nom du rôle',
                    ),
                    const SizedBox(height: 16),
                    NeonTextField(
                      controller: descriptionController,
                      labelText: 'Description',
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<UserRoleType>(
                      value: selectedType,
                      decoration: InputDecoration(
                        labelText: 'Type de rôle',
                        labelStyle: const TextStyle(color: Colors.white),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: NeonTheme.neonCyan,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                      ),
                      dropdownColor: Colors.black,
                      style: const TextStyle(color: Colors.white),
                      items:
                          UserRoleType.values.map((type) {
                            return DropdownMenuItem<UserRoleType>(
                              value: type,
                              child: Text(type.toString().split('.').last),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                            // Mettre à jour les permissions en fonction du type
                            selectedPermissions.clear();
                            selectedPermissions.addAll(
                              AppPermissions.getPermissionsForRole(value),
                            );
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Permissions',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView(
                        children:
                            AppPermissions.getAllPermissions().map((
                              permission,
                            ) {
                              return CheckboxListTile(
                                title: Text(
                                  permission,
                                  style: const TextStyle(color: Colors.white),
                                ),
                                value: selectedPermissions.contains(permission),
                                activeColor: NeonTheme.neonCyan,
                                checkColor: Colors.black,
                                onChanged: (value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedPermissions.add(permission);
                                    } else {
                                      selectedPermissions.remove(permission);
                                    }
                                  });
                                },
                              );
                            }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Annuler',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                TextButton(
                  onPressed: () async {
                    if (nameController.text.isEmpty) {
                      return;
                    }

                    final now = DateTime.now();
                    final role = UserRole(
                      id: const Uuid().v4(),
                      name: nameController.text,
                      type: selectedType,
                      permissions: selectedPermissions,
                      description:
                          descriptionController.text.isNotEmpty
                              ? descriptionController.text
                              : null,
                      createdAt: now,
                      updatedAt: now,
                    );

                    setState(() {
                      _isLoading = true;
                    });

                    final userService = ref.read(userServiceProvider);
                    final success = await userService.addRole(role);

                    setState(() {
                      _isLoading = false;
                    });

                    if (success) {
                      // Rafraîchir la liste des rôles
                      final _ = ref.refresh(userRolesProvider);
                      if (context.mounted) {
                        Navigator.pop(context);
                      }
                    }
                  },
                  child: const Text(
                    'Ajouter',
                    style: TextStyle(color: NeonTheme.neonCyan),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showEditRoleDialog(BuildContext context, UserRole role) {
    final nameController = TextEditingController(text: role.name);
    final descriptionController = TextEditingController(
      text: role.description ?? '',
    );
    var selectedType = role.type;
    final selectedPermissions = [...role.permissions];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.black,
              title: const Text(
                'Modifier le Rôle',
                style: TextStyle(color: Colors.white),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NeonTextField(
                      controller: nameController,
                      labelText: 'Nom du rôle',
                    ),
                    const SizedBox(height: 16),
                    NeonTextField(
                      controller: descriptionController,
                      labelText: 'Description',
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<UserRoleType>(
                      value: selectedType,
                      decoration: InputDecoration(
                        labelText: 'Type de rôle',
                        labelStyle: const TextStyle(color: Colors.white),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Colors.white),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: NeonTheme.neonCyan,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                      ),
                      dropdownColor: Colors.black,
                      style: const TextStyle(color: Colors.white),
                      items:
                          UserRoleType.values.map((type) {
                            return DropdownMenuItem<UserRoleType>(
                              value: type,
                              child: Text(type.toString().split('.').last),
                            );
                          }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedType = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Permissions',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            setState(() {
                              selectedPermissions.clear();
                              selectedPermissions.addAll(
                                AppPermissions.getPermissionsForRole(
                                  selectedType,
                                ),
                              );
                            });
                          },
                          child: const Text(
                            'Réinitialiser',
                            style: TextStyle(color: NeonTheme.neonCyan),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView(
                        children:
                            AppPermissions.getAllPermissions().map((
                              permission,
                            ) {
                              return CheckboxListTile(
                                title: Text(
                                  permission,
                                  style: const TextStyle(color: Colors.white),
                                ),
                                value: selectedPermissions.contains(permission),
                                activeColor: NeonTheme.neonCyan,
                                checkColor: Colors.black,
                                onChanged: (value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedPermissions.add(permission);
                                    } else {
                                      selectedPermissions.remove(permission);
                                    }
                                  });
                                },
                              );
                            }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Annuler',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                TextButton(
                  onPressed: () async {
                    if (nameController.text.isEmpty) {
                      return;
                    }

                    final updatedRole = role.copyWith(
                      name: nameController.text,
                      type: selectedType,
                      permissions: selectedPermissions,
                      description:
                          descriptionController.text.isNotEmpty
                              ? descriptionController.text
                              : null,
                      updatedAt: DateTime.now(),
                    );

                    setState(() {
                      _isLoading = true;
                    });

                    final userService = ref.read(userServiceProvider);
                    final success = await userService.updateRole(updatedRole);

                    setState(() {
                      _isLoading = false;
                    });

                    if (success) {
                      // Rafraîchir la liste des rôles
                      final _ = ref.refresh(userRolesProvider);
                      if (context.mounted) {
                        Navigator.pop(context);
                      }
                    }
                  },
                  child: const Text(
                    'Mettre à jour',
                    style: TextStyle(color: NeonTheme.neonCyan),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteRoleDialog(BuildContext context, UserRole role) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Confirmer la suppression',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer le rôle ${role.name} ?',
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                setState(() {
                  _isLoading = true;
                });

                final userService = ref.read(userServiceProvider);
                final success = await userService.deleteRole(role.id);

                setState(() {
                  _isLoading = false;
                });

                if (success) {
                  // Rafraîchir la liste des rôles
                  final _ = ref.refresh(userRolesProvider);
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }
              },
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}
