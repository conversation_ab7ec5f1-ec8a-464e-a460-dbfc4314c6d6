import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

// Provider pour les produits avec stock critique ou faible
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  final inventoryService = ref.watch(inventoryServiceProvider);
  final products = await inventoryService.getProducts();

  return products
      .where(
        (product) =>
            product.stockStatus == StockStatus.critical ||
            product.stockStatus == StockStatus.low,
      )
      .toList();
});

class StockAlertsScreen extends ConsumerWidget {
  const StockAlertsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lowStockProductsAsync = ref.watch(lowStockProductsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alertes de Stock'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: lowStockProductsAsync.when(
          data: (products) {
            if (products.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      // ignore: deprecated_member_use
                      FontAwesomeIcons.checkCircle,
                      color: Colors.green,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Aucune alerte de stock',
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Tous les produits ont un niveau de stock normal',
                      style: TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                  ],
                ),
              );
            }

            return categoriesAsync.when(
              data: (categories) {
                return _buildAlertsList(context, ref, products, categories);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error:
                  (error, stack) => Center(
                    child: Text(
                      'Erreur: $error',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
        ),
      ),
    );
  }

  Widget _buildAlertsList(
    BuildContext context,
    WidgetRef ref,
    List<Product> products,
    List<ProductCategory> categories,
  ) {
    // Trier les produits par niveau de criticité (critique d'abord, puis faible)
    products.sort((a, b) {
      if (a.stockStatus == StockStatus.critical &&
          b.stockStatus != StockStatus.critical) {
        return -1;
      } else if (a.stockStatus != StockStatus.critical &&
          b.stockStatus == StockStatus.critical) {
        return 1;
      } else {
        return a.quantity.compareTo(b.quantity);
      }
    });

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        final category = categories.firstWhere(
          (c) => c.id == product.categoryId,
          orElse:
              () => ProductCategory(
                id: '',
                name: 'Inconnue',
                price: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
        );

        return _buildAlertCard(context, ref, product, category);
      },
    );
  }

  Widget _buildAlertCard(
    BuildContext context,
    WidgetRef ref,
    Product product,
    ProductCategory category,
  ) {
    final bool isCritical = product.stockStatus == StockStatus.critical;
    final Color statusColor = isCritical ? Colors.red : Colors.orange;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: NeonCard(
        color: statusColor,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: statusColor.withAlpha(77), // 0.3 * 255 ≈ 77
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    isCritical
                        ? FontAwesomeIcons.triangleExclamation
                        : FontAwesomeIcons.exclamation,
                    color: statusColor,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            product.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: statusColor.withAlpha(77), // 0.3 * 255 ≈ 77
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            product.getStatusLabel(),
                            style: TextStyle(
                              color: statusColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Catégorie: ${category.name}',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Quantité actuelle: ${product.quantity}',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        NeonButton(
                          text: 'Voir détails',
                          icon: FontAwesomeIcons.eye,
                          color: Colors.white,
                          onPressed: () {
                            context.push('/inventory/product/${product.id}');
                          },
                        ),
                        const SizedBox(width: 16),
                        NeonButton(
                          text: 'Ajouter au stock',
                          icon: FontAwesomeIcons.plus,
                          color: Colors.green,
                          onPressed: () {
                            _showAddStockDialog(context, ref, product);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddStockDialog(
    BuildContext context,
    WidgetRef ref,
    Product product,
  ) {
    final TextEditingController quantityController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Ajouter au Stock',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Combien d\'unités souhaitez-vous ajouter à ${product.name} ?',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Quantité',
                  labelStyle: TextStyle(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.green),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.green, width: 2),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                final quantity = int.tryParse(quantityController.text) ?? 0;
                if (quantity <= 0) return;

                final inventoryService = ref.read(inventoryServiceProvider);
                final updatedProduct = product.copyWith(
                  quantity: product.quantity + quantity,
                  updatedAt: DateTime.now(),
                );

                await inventoryService.updateProduct(updatedProduct);

                // Rafraîchir les données
                // ignore: unused_result
                ref.refresh(lowStockProductsProvider);
                // ignore: unused_result
                ref.refresh(productsProvider);

                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text(
                'Confirmer',
                style: TextStyle(color: Colors.green),
              ),
            ),
          ],
        );
      },
    );
  }
}
