import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService {
  User? _currentUser;
  String? _token;

  String? get token => _token;
  bool get isLoggedIn => _currentUser != null;
  User? get currentUser => _currentUser;

  // Méthode pour initialiser le service
  Future<void> initialize() async {
    await _loadUserFromPrefs();

    // Vérifier et corriger l'avatar de l'utilisateur si nécessaire
    if (_currentUser != null) {
      await _checkAndFixUserAvatar();
    }
  }

  // Méthode pour vérifier et corriger l'avatar de l'utilisateur
  Future<void> _checkAndFixUserAvatar() async {
    if (_currentUser == null) return;

    final avatar = _currentUser!.avatar;
    debugPrint('Vérification de l\'avatar de l\'utilisateur: $avatar');

    bool needsUpdate = false;
    String? newAvatar;

    // Vérifier si l'avatar est valide
    if (avatar == null || avatar.isEmpty) {
      // Pas d'avatar, générer un avatar par défaut
      needsUpdate = true;
      newAvatar =
          'https://ui-avatars.com/api/?name=${Uri.encodeComponent(_currentUser!.name)}&background=random';
      debugPrint(
        'Pas d\'avatar, génération d\'un avatar par défaut: $newAvatar',
      );
    } else if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
      // C'est une URL réseau, vérifier si c'est une URL d'avatar valide
      if (!avatar.contains('ui-avatars.com') &&
              !avatar.contains('gravatar.com') &&
              avatar.contains('5000') ||
          avatar.contains('product') ||
          avatar.contains('coque')) {
        // C'est probablement une URL de produit, pas un avatar
        needsUpdate = true;
        newAvatar =
            'https://ui-avatars.com/api/?name=${Uri.encodeComponent(_currentUser!.name)}&background=random';
        debugPrint(
          'URL d\'avatar invalide, génération d\'un nouvel avatar: $newAvatar',
        );
      }
    }

    // Mettre à jour l'avatar si nécessaire
    if (needsUpdate && newAvatar != null) {
      final updatedUser = _currentUser!.copyWith(avatar: newAvatar);
      _currentUser = updatedUser;

      // Sauvegarder l'utilisateur mis à jour
      if (_token != null) {
        await _saveUserToPrefs(updatedUser, _token!);
        debugPrint('Avatar de l\'utilisateur mis à jour: $newAvatar');
      }
    }
  }

  // Méthode pour charger l'utilisateur depuis les préférences
  Future<void> _loadUserFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('user');
      final token = prefs.getString('token');

      if (userJson != null && token != null) {
        _currentUser = User.fromJson(userJson);
        _token = token;
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement de l\'utilisateur: $e');
    }
  }

  // Méthode pour sauvegarder l'utilisateur dans les préférences
  Future<void> _saveUserToPrefs(User user, String token) async {
    try {
      debugPrint(
        'Sauvegarde de l\'utilisateur dans les préférences: ${user.id}',
      );
      debugPrint('Avatar à sauvegarder: ${user.avatar}');

      // Vérifier la sérialisation
      final userJson = user.toJson();
      debugPrint('JSON de l\'utilisateur: $userJson');

      // Vérifier la désérialisation
      final deserializedUser = User.fromJson(userJson);
      debugPrint('Avatar après désérialisation: ${deserializedUser.avatar}');

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user', userJson);
      await prefs.setString('token', token);

      // Vérifier que l'utilisateur a bien été sauvegardé
      final savedUserJson = prefs.getString('user');
      if (savedUserJson != null) {
        final savedUser = User.fromJson(savedUserJson);
        debugPrint('Avatar récupéré des préférences: ${savedUser.avatar}');
      } else {
        debugPrint(
          'Aucun utilisateur trouvé dans les préférences après sauvegarde',
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Erreur lors de la sauvegarde de l\'utilisateur: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  // Méthode pour se connecter avec email et mot de passe
  Future<User?> login(String email, String password) async {
    try {
      // Simuler une connexion réussie pour le test
      final user = User(
        id: '1',
        name: 'Utilisateur Test',
        email: email,
        role: 'admin',
        // Utiliser une URL d'avatar fiable pour les tests
        avatar:
            'https://ui-avatars.com/api/?name=Utilisateur+Test&background=random',
        department: 'Marketing',
      );

      _currentUser = user;
      _token = 'test_token_${DateTime.now().millisecondsSinceEpoch}';

      // Sauvegarder l'utilisateur dans les préférences
      await _saveUserToPrefs(user, _token!);

      return user;
    } catch (e) {
      debugPrint('Erreur lors de la connexion: $e');
      return null;
    }
  }

  // Méthode pour se connecter avec un code
  Future<User?> loginWithCode(String code) async {
    try {
      // Vérifier si le code est valide (code d'accès: "03117455")
      if (code != "03117455") {
        return null;
      }

      // Simuler une connexion réussie pour le test
      final user = User(
        id: '2',
        name: 'Agent Commercial',
        email: '<EMAIL>',
        role: 'admin',
        // Utiliser une URL d'avatar fiable pour les tests
        avatar:
            'https://ui-avatars.com/api/?name=Agent+Commercial&background=random',
        department: 'Ventes',
      );

      _currentUser = user;
      _token = 'test_token_code_${DateTime.now().millisecondsSinceEpoch}';

      // Sauvegarder l'utilisateur dans les préférences
      await _saveUserToPrefs(user, _token!);

      return user;
    } catch (e) {
      debugPrint('Erreur lors de la connexion avec code: $e');
      return null;
    }
  }

  // Méthode pour s'inscrire
  Future<bool> register(User user, [String password = '']) async {
    try {
      // Simuler une inscription réussie
      _currentUser = user;
      _token = 'test_token_${DateTime.now().millisecondsSinceEpoch}';

      // Sauvegarder l'utilisateur dans les préférences
      await _saveUserToPrefs(user, _token!);

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'inscription: $e');
      return false;
    }
  }

  // Méthode pour se déconnecter
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user');
      await prefs.remove('token');

      _currentUser = null;
      _token = null;
    } catch (e) {
      debugPrint('Erreur lors de la déconnexion: $e');
    }
  }

  // Méthode pour mettre à jour l'utilisateur actuel
  Future<void> updateCurrentUser(User user) async {
    try {
      debugPrint('Mise à jour de l\'utilisateur actuel: ${user.id}');
      debugPrint('Nouvel avatar: ${user.avatar}');

      // Sauvegarder l'ancien avatar pour comparaison
      final oldAvatar = _currentUser?.avatar;
      debugPrint('Ancien avatar: $oldAvatar');

      _currentUser = user;
      debugPrint('Utilisateur actuel mis à jour en mémoire');

      // Sauvegarder l'utilisateur mis à jour dans les préférences
      if (_token != null) {
        debugPrint('Sauvegarde de l\'utilisateur dans les préférences');
        await _saveUserToPrefs(user, _token!);

        // Vérifier que l'utilisateur a bien été sauvegardé
        final prefs = await SharedPreferences.getInstance();
        final savedUserJson = prefs.getString('user');
        if (savedUserJson != null) {
          final savedUser = User.fromJson(savedUserJson);
          debugPrint(
            'Avatar sauvegardé dans les préférences: ${savedUser.avatar}',
          );
        } else {
          debugPrint(
            'Aucun utilisateur trouvé dans les préférences après sauvegarde',
          );
        }
      } else {
        debugPrint(
          'Pas de token, impossible de sauvegarder l\'utilisateur dans les préférences',
        );
      }
    } catch (e, stackTrace) {
      debugPrint('Erreur lors de la mise à jour de l\'utilisateur: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  // Méthode pour forcer la mise à jour de l'utilisateur actuel
  void forceUpdateCurrentUser(User user) {
    debugPrint('Mise à jour forcée de l\'utilisateur actuel: ${user.id}');
    debugPrint('Nouvel avatar: ${user.avatar}');
    _currentUser = user;
  }
}

// Notifier pour l'utilisateur actuel
class CurrentUserNotifier extends StateNotifier<User?> {
  final AuthService _authService;

  CurrentUserNotifier(this._authService) : super(_authService.currentUser);

  // Méthode pour mettre à jour l'utilisateur actuel
  void updateUser(User user) {
    debugPrint(
      'CurrentUserNotifier: Mise à jour de l\'utilisateur: ${user.id}',
    );
    debugPrint('CurrentUserNotifier: Nouvel avatar: ${user.avatar}');

    // Mettre à jour l'utilisateur dans le service d'authentification
    _authService.forceUpdateCurrentUser(user);

    // Mettre à jour l'état
    state = user;

    debugPrint('CurrentUserNotifier: État mis à jour avec succès');
  }

  // Méthode pour rafraîchir l'utilisateur actuel depuis le service d'authentification
  void refresh() {
    debugPrint('CurrentUserNotifier: Rafraîchissement de l\'utilisateur');
    state = _authService.currentUser;
    debugPrint(
      'CurrentUserNotifier: Utilisateur rafraîchi: ${state?.toString()}',
    );
    debugPrint(
      'CurrentUserNotifier: Avatar après rafraîchissement: ${state?.avatar}',
    );
  }

  // Méthode pour forcer la mise à jour immédiate
  void forceUpdate() {
    debugPrint('CurrentUserNotifier: Mise à jour forcée');
    final currentUser = _authService.currentUser;
    if (currentUser != null) {
      state = User.fromJson(
        currentUser.toJson(),
      ); // Créer une nouvelle instance
      debugPrint('CurrentUserNotifier: Mise à jour forcée terminée');
    }
  }
}

// Provider pour le service d'authentification
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

// Provider pour l'utilisateur actuel
final currentUserProvider = StateNotifierProvider<CurrentUserNotifier, User?>((
  ref,
) {
  final authService = ref.watch(authServiceProvider);
  return CurrentUserNotifier(authService);
});
