import 'package:flutter/foundation.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/customer/customer.dart';

class ContactsImporter {
  /// Vérifie si l'application a la permission d'accéder aux contacts
  static Future<bool> checkPermission() async {
    if (await Permission.contacts.isGranted) {
      return true;
    }

    // Demander la permission
    final status = await Permission.contacts.request();
    return status.isGranted;
  }

  /// Importe les contacts du téléphone
  static Future<List<Customer>> importFromContacts() async {
    try {
      // Vérifier la permission
      if (!await checkPermission()) {
        return [];
      }

      // Récupérer tous les contacts
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      // Convertir les contacts en clients
      return _convertContactsToCustomers(contacts);
    } catch (e) {
      debugPrint('Erreur lors de l\'importation des contacts: $e');
      return [];
    }
  }

  /// Convertit les contacts en clients
  static List<Customer> _convertContactsToCustomers(List<Contact> contacts) {
    final List<Customer> customers = [];

    for (final contact in contacts) {
      // Ignorer les contacts sans nom
      if (contact.displayName.isEmpty) {
        continue;
      }

      // Récupérer le premier numéro de téléphone
      String? phone;
      if (contact.phones.isNotEmpty) {
        phone = contact.phones.first.number;
      }

      // Récupérer le premier email
      String? email;
      if (contact.emails.isNotEmpty) {
        email = contact.emails.first.address;
      }

      // Récupérer la première adresse
      String? address;
      if (contact.addresses.isNotEmpty) {
        final addr = contact.addresses.first;
        address = [
          addr.street,
          addr.city,
          addr.state,
          addr.postalCode,
          addr.country,
        ].where((e) => e.isNotEmpty).join(', ');
      }

      // Récupérer la première organisation
      String? company;
      if (contact.organizations.isNotEmpty) {
        company = contact.organizations.first.company;
      }

      // Créer un client à partir du contact
      customers.add(
        Customer(
          id: DateTime.now().millisecondsSinceEpoch.toString() + contact.id,
          name: contact.displayName,
          phone: phone,
          email: email,
          address: address,
          company: company,
          createdAt: DateTime.now(),
          lastContact: DateTime.now(),
          status: CustomerStatus.lead,
          tags: ['importé', 'contact'],
        ),
      );
    }

    return customers;
  }
}
