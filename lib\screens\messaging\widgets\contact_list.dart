import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../../../models/messaging/contact.dart';
import '../../../models/messaging/message.dart';
import '../../../services/messaging/messaging_service.dart';
import '../../../theme/neon_theme.dart';

class ContactList extends ConsumerStatefulWidget {
  final Function(Contact) onContactSelected;

  const ContactList({super.key, required this.onContactSelected});

  @override
  ConsumerState<ContactList> createState() => _ContactListState();
}

class _ContactListState extends ConsumerState<ContactList> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  MessageChannel? _filterChannel;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final contacts = ref.watch(contactsProvider);
    final selectedContact = ref.watch(selectedContactProvider);
    final selectedPlatform = ref.watch(selectedPlatformProvider);

    // Filtrer les contacts
    final filteredContacts =
        contacts.where((contact) {
          // Filtrer par recherche
          final matchesSearch =
              _searchQuery.isEmpty ||
              contact.name.toLowerCase().contains(_searchQuery) ||
              (contact.phoneNumber?.toLowerCase().contains(_searchQuery) ??
                  false) ||
              (contact.email?.toLowerCase().contains(_searchQuery) ?? false);

          // Filtrer par canal
          final matchesChannel =
              _filterChannel == null ||
              contact.channels[_filterChannel] == true;

          // Filtrer par plateforme
          bool matchesPlatform = true;
          if (selectedPlatform != null) {
            switch (selectedPlatform) {
              case 'WhatsApp':
                matchesPlatform =
                    contact.channels[MessageChannel.whatsapp] == true;
                break;
              case 'Meta':
                matchesPlatform =
                    contact.channels[MessageChannel.facebook] == true ||
                    contact.channels[MessageChannel.instagram] == true;
                break;
              case 'Telegram':
                matchesPlatform =
                    contact.channels[MessageChannel.internal] == true;
                break;
            }
          }

          return matchesSearch && matchesChannel && matchesPlatform;
        }).toList();

    // Trier les contacts: favoris d'abord, puis par nombre de messages non lus, puis par nom
    filteredContacts.sort((a, b) {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;

      if (a.unreadCount > 0 && b.unreadCount == 0) return -1;
      if (a.unreadCount == 0 && b.unreadCount > 0) return 1;
      if (a.unreadCount != b.unreadCount) return b.unreadCount - a.unreadCount;

      return a.name.compareTo(b.name);
    });

    return Container(
      color: const Color(0xFF0F0F1E),
      child: Column(
        children: [
          // En-tête avec recherche
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Barre de recherche
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1A1A2E),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color: const Color(0xFF2A2A3E),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.search, color: Colors.white54, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: _searchController,
                          style: const TextStyle(color: Colors.white),
                          decoration: const InputDecoration(
                            hintText: 'Rechercher',
                            hintStyle: TextStyle(color: Colors.white54),
                            border: InputBorder.none,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Boutons d'action
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.message,
                      label: 'Message direct',
                      color: NeonTheme.neonCyan,
                      onTap: () {
                        // Action pour message direct
                      },
                    ),
                    _buildActionButton(
                      icon: Icons.group,
                      label: 'Groupe',
                      color: NeonTheme.neonPink,
                      onTap: () {
                        // Action pour groupe
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // En-tête "Messages récents"
                const Row(
                  children: [
                    Icon(Icons.history, color: NeonTheme.neonCyan, size: 18),
                    SizedBox(width: 8),
                    Text(
                      'Messages récents',
                      style: TextStyle(
                        color: NeonTheme.neonCyan,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Liste des contacts
          Expanded(
            child:
                filteredContacts.isEmpty
                    ? const Center(
                      child: Text(
                        'Aucun contact trouvé',
                        style: TextStyle(color: Colors.white),
                      ),
                    )
                    : ListView.builder(
                      itemCount: filteredContacts.length,
                      itemBuilder: (context, index) {
                        final contact = filteredContacts[index];
                        final isSelected = selectedContact?.id == contact.id;

                        return _buildContactItem(contact, isSelected);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(
            border: Border.all(color: color, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // Ajout pour éviter le débordement
            children: [
              Icon(icon, color: color, size: 16), // Taille réduite
              const SizedBox(width: 4), // Espace réduit
              Flexible(
                // Ajout pour permettre au texte de s'adapter
                child: Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 12, // Taille de police réduite
                  ),
                  overflow:
                      TextOverflow.ellipsis, // Ajout pour gérer le débordement
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactItem(Contact contact, bool isSelected) {
    final unreadCount = ref.watch(unreadCountByContactProvider(contact.id));

    return InkWell(
      onTap: () {
        widget.onContactSelected(contact);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF1A1A2E) : Colors.transparent,
          border: const Border(
            bottom: BorderSide(color: Color(0xFF2A2A3E), width: 1),
          ),
        ),
        child: Row(
          children: [
            // Avatar du contact avec première lettre
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getContactColor(contact),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  contact.name.isNotEmpty ? contact.name[0].toUpperCase() : '?',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Informations du contact
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          contact.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight:
                                unreadCount > 0 || isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                            fontSize: 16,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        DateFormat('HH:mm').format(contact.lastSeen),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      // Icône du canal principal
                      Icon(
                        _getChannelIcon(_getPrimaryChannel(contact)),
                        color: _getChannelColor(_getPrimaryChannel(contact)),
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      // Dernier message
                      const Expanded(
                        child: Text(
                          'Bonjour, je souhaiterais avoir des informations sur vos services d\'impression.',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // Badge de messages non lus
                      if (unreadCount > 0)
                        Container(
                          margin: const EdgeInsets.only(left: 4),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: NeonTheme.neonPink,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$unreadCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getChannelIcon(MessageChannel? channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return FontAwesomeIcons.whatsapp;
      case MessageChannel.facebook:
        return FontAwesomeIcons.facebookMessenger;
      case MessageChannel.sms:
        return FontAwesomeIcons.commentSms;
      case MessageChannel.email:
        return FontAwesomeIcons.envelope;
      case MessageChannel.app:
        return FontAwesomeIcons.message;
      default:
        return FontAwesomeIcons.comments;
    }
  }

  Color _getChannelColor(MessageChannel channel) {
    switch (channel) {
      case MessageChannel.whatsapp:
        return Colors.green;
      case MessageChannel.facebook:
        return Colors.blue;
      case MessageChannel.sms:
        return Colors.orange;
      case MessageChannel.email:
        return Colors.red;
      case MessageChannel.app:
        return NeonTheme.neonPurple;
      case MessageChannel.instagram:
        return Colors.purple;
      case MessageChannel.internal:
        return Colors.grey;
      case MessageChannel.telegram:
        return Colors.lightBlue;
    }
  }

  MessageChannel _getPrimaryChannel(Contact contact) {
    // Déterminer le canal principal du contact
    if (contact.channels[MessageChannel.whatsapp] == true) {
      return MessageChannel.whatsapp;
    } else if (contact.channels[MessageChannel.facebook] == true) {
      return MessageChannel.facebook;
    } else if (contact.channels[MessageChannel.telegram] == true) {
      return MessageChannel.telegram;
    } else if (contact.channels[MessageChannel.sms] == true) {
      return MessageChannel.sms;
    } else if (contact.channels[MessageChannel.email] == true) {
      return MessageChannel.email;
    } else {
      return MessageChannel.app;
    }
  }

  Color _getContactColor(Contact contact) {
    // Déterminer la couleur en fonction du canal principal du contact
    return _getChannelColor(_getPrimaryChannel(contact));
  }
}
