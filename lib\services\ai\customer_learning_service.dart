import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/ai/ai_message.dart';
import '../../models/messaging/message.dart';
import '../../models/product/product.dart';
import '../ai/ai_service.dart';
import '../customer/customer_service.dart';
import '../messaging/messaging_service.dart';
import '../product/product_service.dart';

// Provider pour le service d'apprentissage client
final customerLearningServiceProvider = Provider<CustomerLearningService>((
  ref,
) {
  final aiService = ref.watch(aiServiceProvider);
  final customerService = ref.watch(customerServiceProvider);
  final messagingService = ref.watch(messagingServiceProvider);
  final productService = ref.watch(productServiceProvider);
  return CustomerLearningService(
    aiService: aiService,
    customerService: customerService,
    messagingService: messagingService,
    productService: productService,
    // invoiceService: invoiceService, // Removed
  );
});

class CustomerLearningService {
  final AIService aiService;
  final CustomerService customerService;
  final MessagingService messagingService;
  final ProductService productService;
  // final InvoiceService invoiceService; // Removed

  CustomerLearningService({
    required this.aiService,
    required this.customerService,
    required this.messagingService,
    required this.productService,
    // required this.invoiceService, // Removed
  });

  // Analyser les messages d'un client pour extraire ses préférences
  Future<Map<String, dynamic>> extractCustomerPreferences(
    String customerId,
  ) async {
    try {
      // Récupérer le client
      final customer = await customerService.getCustomerById(customerId);
      if (customer == null) {
        throw Exception('Client non trouvé');
      }

      // Récupérer les conversations du client
      final conversations = await messagingService
          .getConversationsByParticipantId(customer.contactId ?? '');
      if (conversations.isEmpty) {
        return {};
      }

      // Récupérer tous les messages du client
      List<Message> allMessages = [];
      for (final conversation in conversations) {
        final messages = await messagingService.getMessagesByConversationId(
          conversation.id,
        );
        allMessages.addAll(
          messages.where((m) => m.sender == customer.contactId),
        );
      }

      if (allMessages.isEmpty) {
        return {};
      }

      // Récupérer les commandes du client - Functionality removed due to InvoiceService removal
      // final invoices = await invoiceService.getInvoicesByCustomerId(customerId);

      // Construire le contexte des commandes - Functionality removed due to InvoiceService removal
      String orderContext = '';
      // if (invoices.isNotEmpty) {
      //   orderContext = 'Historique des commandes:\n';
      //   for (final invoice in invoices.take(5)) {
      //     orderContext += '- Commande du ${_formatDate(invoice.date)}:\n';
      //     for (final item in invoice.items) {
      //       orderContext += '  * ${item.quantity}x ${item.productName}\n';
      //     }
      //   }
      // }

      // Construire le prompt pour l'IA
      final prompt = '''
      Tu es un assistant spécialisé dans l'analyse des préférences client.

      Analyse les messages suivants d'un client et extrait ses préférences en termes de:
      - Produits préférés
      - Couleurs préférées
      - Modèles de téléphone
      - Styles de design
      - Méthodes de paiement préférées
      - Autres préférences pertinentes

      $orderContext

      Messages du client:
      ${allMessages.map((m) => '- "${m.content}"').join('\n')}

      Réponds au format JSON comme ceci:
      {
        "preferred_products": ["produit1", "produit2", ...],
        "preferred_colors": ["couleur1", "couleur2", ...],
        "phone_models": ["modèle1", "modèle2", ...],
        "design_styles": ["style1", "style2", ...],
        "payment_methods": ["méthode1", "méthode2", ...],
        "other_preferences": {
          "préférence1": "valeur1",
          "préférence2": "valeur2",
          ...
        }
      }

      Si aucune préférence n'est détectée pour une catégorie, utilise un tableau vide [].
      ''';

      // Générer l'analyse
      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      // Extraire le JSON de la réponse
      String jsonStr = response;
      if (response.contains('{') && response.contains('}')) {
        jsonStr = response.substring(
          response.indexOf('{'),
          response.lastIndexOf('}') + 1,
        );
      }

      // Décoder le JSON
      final Map<String, dynamic> preferences = jsonDecode(jsonStr);

      // Mettre à jour les préférences du client
      final updatedCustomer = customer.copyWith(preferences: preferences);

      await customerService.updateCustomer(updatedCustomer);

      return preferences;
    } catch (e) {
      debugPrint('Erreur lors de l\'extraction des préférences client: $e');
      return {};
    }
  }

  // Générer des suggestions de produits personnalisées
  Future<List<Product>> generatePersonalizedSuggestions(
    String customerId,
  ) async {
    try {
      // Récupérer le client et ses préférences
      final customer = await customerService.getCustomerById(customerId);
      if (customer == null || customer.preferences == null) {
        throw Exception('Client ou préférences non trouvés');
      }

      // Récupérer tous les produits
      final allProducts = await productService.getProducts();

      // Extraire les préférences
      final preferredProducts =
          customer.preferences?['preferred_products'] as List<dynamic>? ?? [];
      final preferredColors =
          customer.preferences?['preferred_colors'] as List<dynamic>? ?? [];
      final phoneModels =
          customer.preferences?['phone_models'] as List<dynamic>? ?? [];

      // Filtrer les produits en fonction des préférences
      List<Product> suggestions = [];

      // 1. Ajouter les produits correspondant exactement aux préférences
      for (final product in allProducts) {
        bool matchesPreference = false;

        // Vérifier si le produit correspond à un produit préféré
        for (final preferredProduct in preferredProducts) {
          if (product.name.toLowerCase().contains(
            preferredProduct.toString().toLowerCase(),
          )) {
            matchesPreference = true;
            break;
          }
        }

        // Vérifier si le produit correspond à une couleur préférée
        if (!matchesPreference) {
          for (final preferredColor in preferredColors) {
            if (product.description.toLowerCase().contains(
              preferredColor.toString().toLowerCase(),
            )) {
              matchesPreference = true;
              break;
            }
          }
        }

        // Vérifier si le produit correspond à un modèle de téléphone
        if (!matchesPreference) {
          for (final phoneModel in phoneModels) {
            if (product.name.toLowerCase().contains(
                  phoneModel.toString().toLowerCase(),
                ) ||
                product.description.toLowerCase().contains(
                  phoneModel.toString().toLowerCase(),
                )) {
              matchesPreference = true;
              break;
            }
          }
        }

        if (matchesPreference && !suggestions.contains(product)) {
          suggestions.add(product);
        }
      }

      // 2. Si pas assez de suggestions, ajouter des produits populaires
      if (suggestions.length < 5) {
        final popularProducts =
            allProducts.where((p) => !suggestions.contains(p)).toList()
              ..sort((a, b) => b.popularity.compareTo(a.popularity));

        suggestions.addAll(popularProducts.take(5 - suggestions.length));
      }

      return suggestions;
    } catch (e) {
      debugPrint(
        'Erreur lors de la génération des suggestions personnalisées: $e',
      );
      return [];
    }
  }

  // Générer un message personnalisé pour le client
  Future<String> generatePersonalizedMessage(
    String customerId,
    String purpose,
  ) async {
    try {
      // Récupérer le client
      final customer = await customerService.getCustomerById(customerId);
      if (customer == null) {
        throw Exception('Client non trouvé');
      }

      // Récupérer les préférences du client
      final preferences = customer.preferences ?? {};

      // Récupérer les commandes du client
      // final invoices = await invoiceService.getInvoicesByCustomerId(customerId); // Service non disponible
      final invoices = <dynamic>[]; // Liste vide temporaire

      // Construire le contexte
      String context = 'Informations sur le client:\n';
      context += '- Nom: ${customer.name}\n';
      context += '- Statut: ${customer.status.name}\n';
      context += '- Dernier contact: ${_formatDate(customer.lastContact)}\n\n';

      if (preferences.isNotEmpty) {
        context += 'Préférences du client:\n';
        preferences.forEach((key, value) {
          if (value is List) {
            if (value.isNotEmpty) {
              context += '- $key: ${value.join(', ')}\n';
            }
          } else if (value is Map) {
            if (value.isNotEmpty) {
              context += '- $key:\n';
              value.forEach((subKey, subValue) {
                context += '  * $subKey: $subValue\n';
              });
            }
          }
        });
        context += '\n';
      }

      if (invoices.isNotEmpty) {
        context += 'Historique des commandes:\n';
        for (final invoice in invoices.take(3)) {
          context +=
              '- Commande du ${_formatDate(invoice.date)}: ${invoice.total} FCFA\n';
        }
        context += '\n';
      }

      // Déterminer le type de message à générer
      String promptType;
      switch (purpose) {
        case 'follow_up':
          promptType =
              'Génère un message de relance personnalisé pour ce client qui n\'a pas répondu depuis plusieurs jours.';
          break;
        case 'new_product':
          promptType =
              'Génère un message pour informer ce client de nouveaux produits qui pourraient l\'intéresser en fonction de ses préférences.';
          break;
        case 'special_offer':
          promptType =
              'Génère un message pour proposer une offre spéciale à ce client en fonction de ses préférences et de son historique d\'achat.';
          break;
        case 'thank_you':
          promptType =
              'Génère un message de remerciement personnalisé suite à un achat récent.';
          break;
        default:
          promptType =
              'Génère un message personnalisé pour ce client en fonction de ses préférences et de son historique.';
          break;
      }

      // Construire le prompt pour l'IA
      final prompt = '''
      Tu es un assistant marketing pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

      $context

      $promptType

      Le message doit être:
      - Personnalisé en fonction des informations du client
      - Professionnel mais chaleureux
      - Concis (maximum 5 lignes)
      - Avec une touche d'humour subtile si approprié

      N'inclus pas de formules d'introduction comme "Cher client" ou de signature, juste le corps du message.
      ''';

      // Générer le message
      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      return response;
    } catch (e) {
      debugPrint('Erreur lors de la génération du message personnalisé: $e');
      return 'Bonjour cher client, nous espérons que vous allez bien. N\'hésitez pas à nous contacter si vous avez besoin de quoi que ce soit.';
    }
  }

  // Analyser le sentiment d'un message
  Future<Map<String, dynamic>> analyzeSentiment(String message) async {
    try {
      final prompt = '''
      Analyse le sentiment du message suivant et détermine:
      1. Le sentiment général (positif, négatif ou neutre)
      2. L'intensité du sentiment (de 1 à 5, où 1 est faible et 5 est fort)
      3. Les émotions détectées (joie, colère, frustration, satisfaction, etc.)
      4. Les sujets mentionnés (produit, service, prix, livraison, etc.)

      Message: "$message"

      Réponds au format JSON comme ceci:
      {
        "sentiment": "positif|négatif|neutre",
        "intensity": 1-5,
        "emotions": ["émotion1", "émotion2", ...],
        "topics": ["sujet1", "sujet2", ...]
      }
      ''';

      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      // Extraire le JSON de la réponse
      String jsonStr = response;
      if (response.contains('{') && response.contains('}')) {
        jsonStr = response.substring(
          response.indexOf('{'),
          response.lastIndexOf('}') + 1,
        );
      }

      // Décoder le JSON
      final Map<String, dynamic> sentiment = json.decode(jsonStr);

      return sentiment;
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse du sentiment: $e');
      return {
        'sentiment': 'neutre',
        'intensity': 3,
        'emotions': [],
        'topics': [],
      };
    }
  }

  // Formater une date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
