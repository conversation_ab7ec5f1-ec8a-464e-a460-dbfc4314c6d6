import 'package:flutter/material.dart';
import '../theme/neon_theme.dart';

class NeonSearchBar extends StatelessWidget {
  final Function(String) onChanged;
  final String hintText;
  final Color? color;
  final TextEditingController? controller;

  const NeonSearchBar({
    super.key,
    required this.onChanged,
    this.hintText = 'Rechercher...',
    this.color,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final neonColor = color ?? NeonTheme.neonCyan;
    
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 153), // 0.6 * 255 ≈ 153
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: neonColor.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1,
        ),
        boxShadow: NeonTheme.neonShadow(neonColor, intensity: 0.3),
      ),
      child: TextF<PERSON>(
        controller: controller,
        onChanged: onChanged,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: Colors.white.withValues(alpha: 128), // 0.5 * 255 ≈ 128
          ),
          prefixIcon: Icon(
            Icons.search,
            color: neonColor,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
        ),
      ),
    );
  }
}
