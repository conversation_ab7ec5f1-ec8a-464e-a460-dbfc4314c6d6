import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/neon_theme.dart';
import 'neon_button_type.dart';
import 'neon_button_size.dart';

class NeonButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color color;
  final IconData? icon;
  final bool small;
  final bool isLoading;
  final NeonButtonType? type;
  final NeonButtonSize? size;

  const NeonButton({
    super.key,
    required this.text,
    this.onPressed,
    this.color = NeonTheme.neonCyan,
    this.icon,
    this.small = false,
    this.isLoading = false,
    this.type,
    this.size,
  });

  @override
  State<NeonButton> createState() => _NeonButtonState();
}

class _NeonButtonState extends State<NeonButton> {
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTapDown: (_) => setState(() => _isPressed = true),
        onTapUp: (_) => setState(() => _isPressed = false),
        onTapCancel: () => setState(() => _isPressed = false),
        onTap: widget.isLoading ? null : widget.onPressed,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(),
            vertical: _getVerticalPadding(),
          ),
          decoration: BoxDecoration(
            color: _getBackgroundColor(),
            borderRadius: BorderRadius.circular(_getBorderRadius()),
            border: Border.all(
              color: _getButtonColor().withValues(alpha: _getBorderOpacity()),
              width: _isPressed ? 2 : 1,
            ),
            boxShadow:
                _isHovered || _isPressed
                    ? NeonTheme.neonShadow(
                      _getButtonColor(),
                      intensity: _isPressed ? 0.8 : 0.5,
                    )
                    : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.isLoading) ...[
                SizedBox(
                  width: _getIconSize(),
                  height: _getIconSize(),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getButtonColor(),
                    ),
                  ),
                ),
                SizedBox(width: widget.icon != null ? 8 : 0),
              ] else if (widget.icon != null) ...[
                FaIcon(
                  widget.icon,
                  color: _getButtonColor(),
                  size: _getIconSize(),
                ),
                const SizedBox(width: 8),
              ],
              Text(
                widget.text,
                style: TextStyle(
                  color: _getButtonColor(),
                  fontSize: _getFontSize(),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    if (_isPressed) {
      return Colors.black.withValues(alpha: 204); // 0.8 * 255 ≈ 204
    } else if (_isHovered) {
      return Colors.black.withValues(alpha: 179); // 0.7 * 255 ≈ 179
    } else {
      return Colors.black.withValues(alpha: 153); // 0.6 * 255 ≈ 153
    }
  }

  // Obtenir la couleur en fonction du type
  Color _getButtonColor() {
    if (widget.type != null) {
      switch (widget.type) {
        case NeonButtonType.primary:
          return NeonTheme.neonCyan;
        case NeonButtonType.secondary:
          return Colors.grey;
        case NeonButtonType.danger:
          return Colors.red;
        case NeonButtonType.success:
          return Colors.green;
        case NeonButtonType.warning:
          return Colors.orange;
        case NeonButtonType.info:
          return Colors.blue;
        default:
          return widget.color;
      }
    }
    return widget.color;
  }

  double _getBorderOpacity() {
    if (_isPressed) {
      return 255.0; // 1.0 * 255 = 255
    } else if (_isHovered) {
      return 204.0; // 0.8 * 255 ≈ 204
    } else {
      return 153.0; // 0.6 * 255 ≈ 153
    }
  }

  double _getHorizontalPadding() {
    if (widget.size != null) {
      switch (widget.size!) {
        case NeonButtonSize.small:
          return 12;
        case NeonButtonSize.medium:
          return 16;
        case NeonButtonSize.large:
          return 24;
      }
    }
    return widget.small ? 12 : 16;
  }

  double _getVerticalPadding() {
    if (widget.size != null) {
      switch (widget.size!) {
        case NeonButtonSize.small:
          return 8;
        case NeonButtonSize.medium:
          return 12;
        case NeonButtonSize.large:
          return 16;
      }
    }
    return widget.small ? 8 : 12;
  }

  double _getBorderRadius() {
    if (widget.size != null) {
      switch (widget.size!) {
        case NeonButtonSize.small:
          return 16;
        case NeonButtonSize.medium:
          return 20;
        case NeonButtonSize.large:
          return 24;
      }
    }
    return widget.small ? 16 : 20;
  }

  double _getIconSize() {
    if (widget.size != null) {
      switch (widget.size!) {
        case NeonButtonSize.small:
          return 14;
        case NeonButtonSize.medium:
          return 18;
        case NeonButtonSize.large:
          return 22;
      }
    }
    return widget.small ? 14 : 18;
  }

  double _getFontSize() {
    if (widget.size != null) {
      switch (widget.size!) {
        case NeonButtonSize.small:
          return 12;
        case NeonButtonSize.medium:
          return 14;
        case NeonButtonSize.large:
          return 16;
      }
    }
    return widget.small ? 12 : 14;
  }
}
