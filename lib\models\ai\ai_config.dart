import 'dart:convert';
import 'ai_provider.dart';

class AIConfig {
  final AIProviderType provider;
  final String apiKey;
  final String? organizationId;
  final String model;
  final double temperature;
  final int maxTokens;
  final String systemPrompt;
  final bool enableAutoResponses;
  final Map<String, dynamic>? additionalConfig;

  AIConfig({
    required this.provider,
    required this.apiKey,
    this.organizationId,
    required this.model,
    required this.temperature,
    required this.maxTokens,
    required this.systemPrompt,
    required this.enableAutoResponses,
    this.additionalConfig,
  });

  factory AIConfig.defaultConfig() {
    return AIConfig(
      provider: AIProviderType.openai,
      apiKey: '',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 1000,
      systemPrompt: '''
Tu es un assistant virtuel pour HCP-DESIGN, une entreprise spécialisée dans l'impression personnalisée et la création graphique.
Ton rôle est d'aider les clients avec leurs questions sur les produits, les commandes et les services.

Sois toujours professionnel, courtois et précis dans tes réponses.
''',
      enableAutoResponses: true,
    );
  }

  // Obtenir le modèle par défaut en fonction du fournisseur
  static String getDefaultModelForProvider(AIProviderType provider) {
    switch (provider) {
      case AIProviderType.openai:
        return 'gpt-3.5-turbo';
      case AIProviderType.anthropic:
        return 'claude-3-haiku-20240307';
      case AIProviderType.gemini:
        return 'gemini-1.5-pro';
      case AIProviderType.mistral:
        return 'mistral-medium';
      case AIProviderType.groq:
        return 'llama3-8b-8192';
      case AIProviderType.deepseek:
        return 'deepseek-chat';
    }
  }

  AIConfig copyWith({
    AIProviderType? provider,
    String? apiKey,
    String? organizationId,
    String? model,
    double? temperature,
    int? maxTokens,
    String? systemPrompt,
    bool? enableAutoResponses,
    Map<String, dynamic>? additionalConfig,
  }) {
    return AIConfig(
      provider: provider ?? this.provider,
      apiKey: apiKey ?? this.apiKey,
      organizationId: organizationId ?? this.organizationId,
      model: model ?? this.model,
      temperature: temperature ?? this.temperature,
      maxTokens: maxTokens ?? this.maxTokens,
      systemPrompt: systemPrompt ?? this.systemPrompt,
      enableAutoResponses: enableAutoResponses ?? this.enableAutoResponses,
      additionalConfig: additionalConfig ?? this.additionalConfig,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'provider': provider.index,
      'apiKey': apiKey,
      'organizationId': organizationId,
      'model': model,
      'temperature': temperature,
      'maxTokens': maxTokens,
      'systemPrompt': systemPrompt,
      'enableAutoResponses': enableAutoResponses,
      'additionalConfig': additionalConfig,
    };
  }

  factory AIConfig.fromMap(Map<String, dynamic> map) {
    return AIConfig(
      provider: AIProviderType.values[map['provider']],
      apiKey: map['apiKey'] ?? '',
      organizationId: map['organizationId'],
      model: map['model'] ?? 'gpt-3.5-turbo',
      temperature: map['temperature']?.toDouble() ?? 0.7,
      maxTokens: map['maxTokens']?.toInt() ?? 1000,
      systemPrompt:
          map['systemPrompt'] ?? AIConfig.defaultConfig().systemPrompt,
      enableAutoResponses: map['enableAutoResponses'] ?? true,
      additionalConfig: map['additionalConfig'],
    );
  }

  String toJson() => json.encode(toMap());

  factory AIConfig.fromJson(String source) =>
      AIConfig.fromMap(json.decode(source));

  @override
  String toString() {
    return 'AIConfig(provider: $provider, apiKey: ${apiKey.substring(0, 3)}***, organizationId: $organizationId, model: $model, temperature: $temperature, maxTokens: $maxTokens, systemPrompt: ${systemPrompt.substring(0, 20)}..., enableAutoResponses: $enableAutoResponses)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AIConfig &&
        other.provider == provider &&
        other.apiKey == apiKey &&
        other.organizationId == organizationId &&
        other.model == model &&
        other.temperature == temperature &&
        other.maxTokens == maxTokens &&
        other.systemPrompt == systemPrompt &&
        other.enableAutoResponses == enableAutoResponses;
  }

  @override
  int get hashCode {
    return provider.hashCode ^
        apiKey.hashCode ^
        organizationId.hashCode ^
        model.hashCode ^
        temperature.hashCode ^
        maxTokens.hashCode ^
        systemPrompt.hashCode ^
        enableAutoResponses.hashCode;
  }
}
