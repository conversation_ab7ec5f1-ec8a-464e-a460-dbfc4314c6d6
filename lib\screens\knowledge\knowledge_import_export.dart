import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import '../../models/knowledge_article.dart';
import '../../services/knowledge_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_button_type.dart';

class KnowledgeImportExport extends ConsumerStatefulWidget {
  const KnowledgeImportExport({super.key});

  @override
  ConsumerState<KnowledgeImportExport> createState() =>
      _KnowledgeImportExportState();
}

class _KnowledgeImportExportState extends ConsumerState<KnowledgeImportExport> {
  bool _isLoading = false;
  String _statusMessage = '';
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F1E),
      appBar: AppBar(
        title: const Text(
          'Importer/Exporter',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1A1A2E),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Gestion de la base de connaissances',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),

            // Exporter
            Card(
              color: const Color(0xFF1A1A2E),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.upload_file, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'Exporter les articles',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Exporter tous les articles de la base de connaissances au format JSON pour les sauvegarder ou les transférer.',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NeonButton(
                          text: 'Exporter',
                          onPressed: _exportArticles,
                          icon: Icons.download,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Importer
            Card(
              color: const Color(0xFF1A1A2E),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.file_download, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'Importer des articles',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Importer des articles depuis un fichier JSON. Les articles existants avec le même ID seront mis à jour.',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NeonButton(
                          text: 'Importer',
                          onPressed: _importArticles,
                          icon: Icons.upload,
                          type: NeonButtonType.secondary,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            if (_isLoading || _statusMessage.isNotEmpty)
              const SizedBox(height: 24),

            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(NeonTheme.neonCyan),
                ),
              ),

            if (_statusMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      _hasError
                          ? Colors.red.withAlpha(50)
                          : Colors.green.withAlpha(50),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _hasError ? Icons.error : Icons.check_circle,
                      color: _hasError ? Colors.red : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _statusMessage,
                        style: TextStyle(
                          color: _hasError ? Colors.red : Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportArticles() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '';
      _hasError = false;
    });

    try {
      final knowledgeService = ref.read(knowledgeServiceProvider);
      final articles = await knowledgeService.getArticles();

      // Convertir les articles en JSON
      final jsonData = jsonEncode(articles.map((a) => a.toJson()).toList());

      // Créer un fichier temporaire
      final directory = await getTemporaryDirectory();
      final file = File('${directory.path}/knowledge_articles.json');
      await file.writeAsString(jsonData);

      // Partager le fichier
      await Share.shareXFiles([
        XFile(file.path),
      ], text: 'Base de connaissances - ${articles.length} articles');

      setState(() {
        _statusMessage = 'Export réussi! ${articles.length} articles exportés.';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur lors de l\'export: ${e.toString()}';
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  Future<void> _importArticles() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '';
      _hasError = false;
    });

    try {
      // Demander la permission de stockage si nécessaire
      if (Platform.isAndroid) {
        final status = await Permission.storage.request();
        if (status.isDenied) {
          throw Exception('Permission de stockage refusée');
        }
      }

      // Sélectionner un fichier
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          _isLoading = false;
          _statusMessage = 'Aucun fichier sélectionné';
          _hasError = true;
        });
        return;
      }

      final file = File(result.files.first.path!);
      final jsonString = await file.readAsString();

      // Décoder le JSON
      final List<dynamic> jsonList = jsonDecode(jsonString);
      final articles =
          jsonList.map((json) => KnowledgeArticle.fromJson(json)).toList();

      // Importer les articles
      final knowledgeService = ref.read(knowledgeServiceProvider);
      int added = 0;
      int updated = 0;

      for (final article in articles) {
        final existing = await knowledgeService.getArticleById(article.id);
        if (existing == null) {
          await knowledgeService.addArticle(article);
          added++;
        } else {
          await knowledgeService.updateArticle(article);
          updated++;
        }
      }

      // Rafraîchir la liste des articles
      ref.invalidate(knowledgeArticlesProvider);

      setState(() {
        _statusMessage =
            'Import réussi! $added articles ajoutés, $updated articles mis à jour.';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur lors de l\'import: ${e.toString()}';
        _hasError = true;
        _isLoading = false;
      });
    }
  }
}
