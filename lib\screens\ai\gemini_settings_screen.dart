import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/theme_provider.dart';
import '../../models/ai/gemini_config.dart';
import '../../models/ai/gemini_model.dart';
import '../../services/ai/gemini_service.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';
import '../../widgets/neon_dropdown.dart';

class GeminiSettingsScreen extends ConsumerStatefulWidget {
  const GeminiSettingsScreen({super.key});

  @override
  ConsumerState<GeminiSettingsScreen> createState() =>
      _GeminiSettingsScreenState();
}

class _GeminiSettingsScreenState extends ConsumerState<GeminiSettingsScreen> {
  final TextEditingController _apiKeyController = TextEditingController();
  bool _isLoading = false;
  bool _showApiKey = false;
  GeminiModel _selectedModel = GeminiModel.geminiPro;
  double _temperature = 0.7;
  int _maxTokens = 2048;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  // Charger les paramètres
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final geminiService = ref.read(geminiServiceProvider);
      final config = geminiService.config;

      if (config != null) {
        _apiKeyController.text = config.apiKey;
        _selectedModel = config.model;
        _temperature = config.temperature;
        _maxTokens = config.maxOutputTokens;
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des paramètres Gemini: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Sauvegarder les paramètres
  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final geminiService = ref.read(geminiServiceProvider);

      final config = GeminiConfig(
        apiKey: _apiKeyController.text.trim(),
        model: _selectedModel,
        temperature: _temperature,
        maxOutputTokens: _maxTokens,
      );

      await geminiService.saveConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Paramètres Gemini sauvegardés avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des paramètres Gemini: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres Gemini'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child:
            _isLoading
                ? Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NeonCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Configuration de l\'API Gemini',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Clé API
                              Row(
                                children: [
                                  Expanded(
                                    child: NeonTextField(
                                      controller: _apiKeyController,
                                      labelText: 'Clé API Gemini',
                                      hintText: 'Entrez votre clé API Gemini',
                                      obscureText: !_showApiKey,
                                    ),
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      _showApiKey
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: primaryColor,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _showApiKey = !_showApiKey;
                                      });
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Obtenez votre clé API sur https://ai.google.dev/',
                                style: TextStyle(
                                  color: Colors.white.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Modèle
                              Text(
                                'Modèle',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              NeonDropdown<GeminiModel>(
                                value: _selectedModel,
                                items:
                                    GeminiModel.values.map((model) {
                                      return DropdownMenuItem<GeminiModel>(
                                        value: model,
                                        child: Text(
                                          _getModelDisplayName(model),
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedModel = value;
                                    });
                                  }
                                },
                                width: double.infinity,
                              ),

                              const SizedBox(height: 24),

                              // Température
                              Text(
                                'Température: ${_temperature.toStringAsFixed(1)}',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Slider(
                                value: _temperature,
                                min: 0.0,
                                max: 1.0,
                                divisions: 10,
                                activeColor: secondaryColor,
                                inactiveColor: secondaryColor.withValues(
                                  alpha: 77,
                                ), // 0.3 * 255 ≈ 77
                                onChanged: (value) {
                                  setState(() {
                                    _temperature = value;
                                  });
                                },
                              ),
                              Text(
                                'Plus la température est basse, plus les réponses sont déterministes. Plus elle est élevée, plus les réponses sont créatives.',
                                style: TextStyle(
                                  color: Colors.white.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Tokens maximum
                              Text(
                                'Tokens maximum: $_maxTokens',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Slider(
                                value: _maxTokens.toDouble(),
                                min: 100,
                                max: 8192,
                                divisions: 81,
                                activeColor: secondaryColor,
                                inactiveColor: secondaryColor.withValues(
                                  alpha: 77,
                                ), // 0.3 * 255 ≈ 77
                                onChanged: (value) {
                                  setState(() {
                                    _maxTokens = value.toInt();
                                  });
                                },
                              ),
                              Text(
                                'Limite la longueur maximale de la réponse générée.',
                                style: TextStyle(
                                  color: Colors.white.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                  fontSize: 12,
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Bouton de sauvegarde
                              Center(
                                child: NeonButton(
                                  text: 'Sauvegarder',
                                  icon: Icons.save,
                                  color: primaryColor,
                                  onPressed: _saveSettings,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Informations sur les modèles
                      NeonCard(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'À propos des modèles Gemini',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildModelInfo(
                                'gemini-pro',
                                'Modèle polyvalent pour les tâches textuelles.',
                                primaryColor,
                              ),
                              _buildModelInfo(
                                'gemini-pro-vision',
                                'Modèle multimodal pour le texte et les images.',
                                primaryColor,
                              ),
                              _buildModelInfo(
                                'gemini-1.5-pro',
                                'Version améliorée avec contexte étendu et meilleures performances.',
                                primaryColor,
                              ),
                              _buildModelInfo(
                                'gemini-1.5-flash',
                                'Version plus rapide et économique de Gemini 1.5.',
                                primaryColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  // Obtenir le nom d'affichage du modèle
  String _getModelDisplayName(GeminiModel model) {
    return model.displayName;
  }

  // Construire les informations sur un modèle
  Widget _buildModelInfo(String modelName, String description, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info_outline, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  modelName,
                  style: TextStyle(color: color, fontWeight: FontWeight.bold),
                ),
                Text(description, style: const TextStyle(color: Colors.white)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
