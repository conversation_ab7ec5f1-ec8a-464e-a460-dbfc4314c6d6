import 'dart:io'; // Ajout pour la classe File
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
// import 'package:youtube_player_flutter/youtube_player_flutter.dart'; // Désactivé temporairement
import '../../models/knowledge_article.dart';
import '../../services/knowledge_service.dart';
import '../../theme/neon_theme.dart';
import 'knowledge_article_editor.dart';

// Provider pour charger un article spécifique par son ID
final knowledgeArticleProvider =
    FutureProvider.family<KnowledgeArticle?, String>((ref, articleId) async {
      return ref.watch(knowledgeServiceProvider).getArticleById(articleId);
    });

class KnowledgeArticleDetail extends ConsumerStatefulWidget {
  final String articleId;

  const KnowledgeArticleDetail({super.key, required this.articleId});

  @override
  KnowledgeArticleDetailState createState() => KnowledgeArticleDetailState();
}

class KnowledgeArticleDetailState
    extends ConsumerState<KnowledgeArticleDetail> {
  // YoutubePlayerController? _youtubeController; // Désactivé temporairement
  VideoPlayerController? _videoPlayerController;
  Future<void>? _initializeVideoPlayerFuture;

  @override
  void initState() {
    super.initState();
    // L'initialisation des contrôleurs se fera dans le build après avoir récupéré l'article
  }

  void _initializeMediaControllers(KnowledgeArticle article) {
    // Dispose des anciens contrôleurs avant d'en créer de nouveaux
    // _youtubeController?.dispose(); // Désactivé temporairement
    _videoPlayerController?.dispose();
    // _youtubeController = null; // Désactivé temporairement
    _videoPlayerController = null;
    _initializeVideoPlayerFuture = null;

    if (article.videoUrl != null && article.videoUrl!.isNotEmpty) {
      // Support uniquement des vidéos locales pour l'instant
      if (Uri.tryParse(article.videoUrl!)?.isAbsolute == false ||
          article.videoUrl!.startsWith('/')) {
        // Chemin local
        final file = File(article.videoUrl!);
        if (file.existsSync()) {
          _videoPlayerController = VideoPlayerController.file(file);
          _initializeVideoPlayerFuture = _videoPlayerController!
              .initialize()
              .then((_) {
                if (mounted) setState(() {});
              })
              .catchError((error) {
                if (mounted) {
                  // print('Erreur initialisation VideoPlayer: $error');
                  setState(() {
                    _videoPlayerController = null;
                  });
                }
              });
        } else {
          // print('Fichier vidéo local non trouvé: ${article.videoUrl}');
        }
      } else if (article.videoUrl!.startsWith('http')) {
        // Pour les URLs HTTP, essayer VideoPlayer.network
        try {
          _videoPlayerController = VideoPlayerController.networkUrl(
            Uri.parse(article.videoUrl!),
          );
          _initializeVideoPlayerFuture = _videoPlayerController!
              .initialize()
              .then((_) {
                if (mounted) setState(() {});
              })
              .catchError((error) {
                if (mounted) {
                  setState(() {
                    _videoPlayerController = null;
                  });
                }
              });
        } catch (e) {
          // URL invalide
        }
      }
    }
    // Déclencher une reconstruction pour afficher les lecteurs vidéo si nécessaire
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    // _youtubeController?.dispose(); // Désactivé temporairement
    _videoPlayerController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F1E),
      appBar: AppBar(
        title: const Text(
          'Détail de l\'article',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1A1A2E),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () async {
              // Utiliser widget.articleId ici
              final article = await ref
                  .read(knowledgeServiceProvider)
                  .getArticleById(widget.articleId);
              if (article != null && context.mounted) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => KnowledgeArticleEditor(article: article),
                  ),
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () {
              _showDeleteConfirmation(context, ref, widget.articleId);
            },
          ),
        ],
      ),
      body: ref
          .watch(knowledgeArticleProvider(widget.articleId))
          .when(
            loading:
                () => const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      NeonTheme.neonCyan,
                    ),
                  ),
                ),
            error:
                (error, stackTrace) => Center(
                  child: Text(
                    'Erreur: $error',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
            data: (article) {
              if (article == null) {
                return const Center(
                  child: Text(
                    'Article non trouvé',
                    style: TextStyle(color: Colors.white),
                  ),
                );
              }

              // Initialiser les contrôleurs si l'article a changé ou pour la première fois
              // On vérifie si l'URL a changé pour éviter des réinitialisations inutiles
              bool shouldInitialize = false;
              if (article.videoUrl != null && article.videoUrl!.isNotEmpty) {
                if (_videoPlayerController == null) {
                  shouldInitialize = true;
                } else if (_videoPlayerController != null &&
                    _videoPlayerController!.dataSource != article.videoUrl!) {
                  shouldInitialize = true;
                }
              } else {
                // Si videoUrl est null ou vide, s'assurer que les contrôleurs sont null
                if (_videoPlayerController != null) {
                  shouldInitialize = true; // pour les disposer
                }
              }

              if (shouldInitialize) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    _initializeMediaControllers(article);
                  }
                });
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // En-tête avec titre et catégorie
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            article.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Chip(
                          label: Text(
                            article.category,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          backgroundColor: NeonTheme.neonCyan.withAlpha(50),
                          padding: EdgeInsets.zero,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // Statut de publication
                    Row(
                      children: [
                        Icon(
                          article.isPublished
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color:
                              article.isPublished
                                  ? Colors.green
                                  : Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          article.isPublished ? 'Publié' : 'Brouillon',
                          style: TextStyle(
                            color:
                                article.isPublished
                                    ? Colors.green
                                    : Colors.orange,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Date de mise à jour
                    Text(
                      'Dernière mise à jour: ${_formatDate(article.lastUpdated)}',
                      style: TextStyle(
                        color: Colors.white.withAlpha(130),
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Affichage de l'image
                    if (article.imageUrl != null &&
                        article.imageUrl!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Center(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8.0),
                            child:
                                article.imageUrl!.startsWith('http')
                                    ? Image.network(
                                      article.imageUrl!,
                                      fit: BoxFit.cover,
                                      loadingBuilder: (
                                        BuildContext context,
                                        Widget child,
                                        ImageChunkEvent? loadingProgress,
                                      ) {
                                        if (loadingProgress == null) {
                                          return child;
                                        }
                                        return Center(
                                          child: CircularProgressIndicator(
                                            value:
                                                loadingProgress
                                                            .expectedTotalBytes !=
                                                        null
                                                    ? loadingProgress
                                                            .cumulativeBytesLoaded /
                                                        loadingProgress
                                                            .expectedTotalBytes!
                                                    : null,
                                            valueColor:
                                                const AlwaysStoppedAnimation<
                                                  Color
                                                >(NeonTheme.neonCyan),
                                          ),
                                        );
                                      },
                                      errorBuilder: (
                                        BuildContext context,
                                        Object error,
                                        StackTrace? stackTrace,
                                      ) {
                                        return const Center(
                                          child: Icon(
                                            Icons.broken_image,
                                            color: Colors.red,
                                            size: 50,
                                          ),
                                        );
                                      },
                                    )
                                    : kIsWeb
                                    ? Image.network(
                                      article.imageUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        BuildContext context,
                                        Object error,
                                        StackTrace? stackTrace,
                                      ) {
                                        return const Center(
                                          child: Icon(
                                            Icons.broken_image,
                                            color: Colors.red,
                                            size: 50,
                                          ),
                                        );
                                      },
                                    )
                                    : Image.file(
                                      File(article.imageUrl!),
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        BuildContext context,
                                        Object error,
                                        StackTrace? stackTrace,
                                      ) {
                                        return const Center(
                                          child: Icon(
                                            Icons.broken_image,
                                            color: Colors.red,
                                            size: 50,
                                          ),
                                        );
                                      },
                                    ),
                          ),
                        ),
                      ),

                    // Affichage de la vidéo
                    // Section YouTube Player désactivée temporairement
                    if (_videoPlayerController != null &&
                        _videoPlayerController!.value.isInitialized)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Column(
                          children: [
                            AspectRatio(
                              aspectRatio:
                                  _videoPlayerController!.value.aspectRatio,
                              child: VideoPlayer(_videoPlayerController!),
                            ),
                            VideoProgressIndicator(
                              _videoPlayerController!,
                              allowScrubbing: true,
                            ),
                            IconButton(
                              icon: Icon(
                                _videoPlayerController!.value.isPlaying
                                    ? Icons.pause
                                    : Icons.play_arrow,
                                color: Colors.white,
                              ),
                              onPressed: () {
                                setState(() {
                                  _videoPlayerController!.value.isPlaying
                                      ? _videoPlayerController!.pause()
                                      : _videoPlayerController!.play();
                                });
                              },
                            ),
                          ],
                        ),
                      )
                    else if (_videoPlayerController != null &&
                        _initializeVideoPlayerFuture != null &&
                        !_videoPlayerController!.value.isInitialized)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: FutureBuilder(
                          future: _initializeVideoPlayerFuture,
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              if (snapshot.hasError ||
                                  !_videoPlayerController!
                                      .value
                                      .isInitialized) {
                                return const Center(
                                  child: Text(
                                    'Erreur chargement vidéo locale.',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                );
                              }
                              // Le setState dans _initializeMediaControllers devrait reconstruire avec le lecteur initialisé
                              return const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    NeonTheme.neonCyan,
                                  ),
                                ),
                              );
                            }
                            return const Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  NeonTheme.neonCyan,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    else if (article.videoUrl != null &&
                        article.videoUrl!.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Center(
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: NeonTheme.darkSurface.withValues(
                                alpha: 128,
                              ), // 0.5 * 255 ≈ 128
                              borderRadius: BorderRadius.circular(8.0),
                              border: Border.all(color: NeonTheme.borderColor),
                            ),
                            child: Column(
                              children: [
                                const Icon(
                                  Icons.videocam_off,
                                  color: Colors.orange,
                                  size: 50,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Vidéo non supportée ou URL invalide: ${article.videoUrl}',
                                  style: const TextStyle(color: Colors.white),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Contenu principal
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1A1A2E),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        article.content,
                        style: TextStyle(
                          color: Colors.white.withAlpha(220),
                          fontSize: 16,
                          height: 1.5,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Tags
                    const Text(
                      'Tags:',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          article.tags.map((tag) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1A1A2E),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                tag,
                                style: TextStyle(
                                  color: Colors.white.withAlpha(180),
                                  fontSize: 12,
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    String articleId,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A2E),
            title: const Text(
              'Supprimer l\'article',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
              'Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.',
              style: TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                child: const Text(
                  'Annuler',
                  style: TextStyle(color: Colors.white70),
                ),
                onPressed: () => Navigator.of(context).pop(),
              ),
              TextButton(
                child: const Text(
                  'Supprimer',
                  style: TextStyle(color: Colors.red),
                ),
                onPressed: () async {
                  try {
                    await ref
                        .read(knowledgeServiceProvider)
                        .deleteArticle(
                          articleId,
                        ); // articleId est maintenant un paramètre
                    ref.invalidate(
                      knowledgeArticlesProvider,
                    ); // Pour rafraîchir la liste des articles
                    ref.invalidate(
                      knowledgeArticleProvider(articleId),
                    ); // Pour rafraîchir ce provider aussi

                    if (context.mounted) {
                      Navigator.of(
                        context,
                      ).pop(); // Fermer la boîte de dialogue
                      Navigator.of(
                        context,
                      ).pop(); // Retourner à la liste des articles

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Article supprimé avec succès'),
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      Navigator.of(
                        context,
                      ).pop(); // Fermer la boîte de dialogue
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Erreur: ${e.toString()}')),
                      );
                    }
                  }
                },
              ),
            ],
          ),
    );
  }
}
