Write-Host "Fixing flutter_local_notifications FlutterLocalNotificationsPlugin.java..."

$javaFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_local_notifications-16.3.3\android\src\main\java\com\dexterous\flutterlocalnotifications\FlutterLocalNotificationsPlugin.java"

if (-not (Test-Path $javaFile)) {
    Write-Host "File not found: $javaFile"
    exit 1
}

Write-Host "Fixing ambiguous reference to bigLargeIcon..."

$content = Get-Content $javaFile -Raw
$updatedContent = $content -replace "bigPictureStyle.bigLargeIcon\(null\);", "bigPictureStyle.bigLargeIcon((Bitmap) null);"

$updatedContent | Set-Content $javaFile

Write-Host ""
Write-Host "Fix completed!"
