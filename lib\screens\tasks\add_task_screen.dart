import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import '../../models/task/task.dart';
import '../../services/task/task_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class AddTaskScreen extends ConsumerStatefulWidget {
  final String? parentTaskId;
  final String? taskId; // ID de la tâche à éditer

  const AddTaskScreen({super.key, this.parentTaskId, this.taskId});

  @override
  ConsumerState<AddTaskScreen> createState() => _AddTaskScreenState();
}

class _AddTaskScreenState extends ConsumerState<AddTaskScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();

  DateTime _dueDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _dueTime = TimeOfDay.now();
  TaskPriority _priority = TaskPriority.medium;
  String? _assignedToId;
  bool _hasReminder = false;
  DateTime? _reminderTime;

  bool _isLoading = false;
  bool _isEditMode = false;
  Task? _taskToEdit;

  @override
  void initState() {
    super.initState();
    _loadTaskData();
  }

  Future<void> _loadTaskData() async {
    if (widget.taskId != null) {
      // Mode édition
      setState(() {
        _isEditMode = true;
      });

      // Récupérer la tâche à éditer
      final tasks = ref.read(tasksProvider);
      try {
        _taskToEdit = tasks.firstWhere((task) => task.id == widget.taskId);

        // Remplir les champs avec les données de la tâche
        _titleController.text = _taskToEdit!.title;
        _descriptionController.text = _taskToEdit!.description;
        _dueDate = _taskToEdit!.dueDate;
        _dueTime = TimeOfDay(hour: _dueDate.hour, minute: _dueDate.minute);
        _priority = _taskToEdit!.priority;
        _assignedToId = _taskToEdit!.assignedToId;
        _hasReminder = _taskToEdit!.hasReminder;
        _reminderTime = _taskToEdit!.reminderTime;

        if (_taskToEdit!.tags.isNotEmpty) {
          _tagsController.text = _taskToEdit!.tags.join(', ');
        }
      } catch (e) {
        debugPrint('Erreur lors de la récupération de la tâche: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tâche non trouvée'),
              backgroundColor: Colors.red,
            ),
          );
          Future.delayed(const Duration(seconds: 1)).then((_) {
            if (mounted) {
              context.pop();
            }
          });
        }
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final teamMembers = ref.watch(teamMembersProvider);
    final parentTask =
        widget.parentTaskId != null
            ? ref
                .watch(tasksProvider)
                .firstWhere(
                  (task) => task.id == widget.parentTaskId,
                  orElse: () => Task.empty(),
                )
            : null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditMode
              ? 'Modifier la tâche'
              : parentTask != null
              ? 'Ajouter une sous-tâche'
              : 'Créer une tâche',
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (parentTask != null && parentTask.id.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(77), // 0.3 * 255 ≈ 77
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: NeonTheme.neonPurple),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Tâche parente:',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          parentTask.title,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),

                // Titre
                NeonTextField(
                  controller: _titleController,
                  labelText: 'Titre',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un titre';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                NeonTextField(
                  controller: _descriptionController,
                  labelText: 'Description',
                  maxLines: 5,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer une description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Date d'échéance
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDueDate(context),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Date d\'échéance',
                            labelStyle: const TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: NeonTheme.neonPurple,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.black.withAlpha(
                              153,
                            ), // 0.6 * 255 ≈ 153
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                DateFormat('dd/MM/yyyy').format(_dueDate),
                                style: const TextStyle(color: Colors.white),
                              ),
                              const Icon(
                                Icons.calendar_today,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDueTime(context),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Heure',
                            labelStyle: const TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: NeonTheme.neonPurple,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.black.withAlpha(
                              153,
                            ), // 0.6 * 255 ≈ 153
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _dueTime.format(context),
                                style: const TextStyle(color: Colors.white),
                              ),
                              const Icon(
                                Icons.access_time,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Priorité
                const Text(
                  'Priorité',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildPriorityOption(
                      'Basse',
                      TaskPriority.low,
                      Colors.green,
                    ),
                    const SizedBox(width: 16),
                    _buildPriorityOption(
                      'Moyenne',
                      TaskPriority.medium,
                      Colors.orange,
                    ),
                    const SizedBox(width: 16),
                    _buildPriorityOption(
                      'Haute',
                      TaskPriority.high,
                      Colors.red,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Assigné à
                const Text(
                  'Assigné à',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String?>(
                  value: _assignedToId,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: NeonTheme.neonPurple,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.black.withAlpha(153), // 0.6 * 255 ≈ 153
                  ),
                  dropdownColor: Colors.black,
                  style: const TextStyle(color: Colors.white),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('Non assigné'),
                    ),
                    ...teamMembers.map((member) {
                      return DropdownMenuItem<String?>(
                        value: member.id,
                        child: Text(member.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _assignedToId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Tags
                NeonTextField(
                  controller: _tagsController,
                  labelText: 'Tags (séparés par des virgules)',
                  hintText: 'Ex: urgent, client, design',
                ),
                const SizedBox(height: 16),

                // Rappel
                SwitchListTile(
                  title: const Text(
                    'Définir un rappel',
                    style: TextStyle(color: Colors.white),
                  ),
                  value: _hasReminder,
                  onChanged: (value) {
                    setState(() {
                      _hasReminder = value;
                      if (value) {
                        _reminderTime = _dueDate.subtract(
                          const Duration(hours: 24),
                        );
                      } else {
                        _reminderTime = null;
                      }
                    });
                  },
                  activeColor: NeonTheme.neonPurple,
                  activeTrackColor: Colors.white.withAlpha(
                    128,
                  ), // 0.5 * 255 ≈ 128
                ),
                if (_hasReminder)
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 8),
                    child: Row(
                      children: [
                        const Text(
                          'Rappel:',
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(width: 8),
                        DropdownButton<int>(
                          value:
                              _reminderTime != null
                                  ? _dueDate.difference(_reminderTime!).inHours
                                  : 24,
                          dropdownColor: Colors.black,
                          style: const TextStyle(color: Colors.white),
                          items: const [
                            DropdownMenuItem(
                              value: 1,
                              child: Text('1 heure avant'),
                            ),
                            DropdownMenuItem(
                              value: 2,
                              child: Text('2 heures avant'),
                            ),
                            DropdownMenuItem(
                              value: 4,
                              child: Text('4 heures avant'),
                            ),
                            DropdownMenuItem(
                              value: 24,
                              child: Text('1 jour avant'),
                            ),
                            DropdownMenuItem(
                              value: 48,
                              child: Text('2 jours avant'),
                            ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _reminderTime = _dueDate.subtract(
                                  Duration(hours: value),
                                );
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 24),

                // Bouton de soumission
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NeonButton(
                        text: 'Annuler',
                        icon: FontAwesomeIcons.xmark,
                        color: Colors.grey,
                        onPressed: () {
                          context.pop();
                        },
                      ),
                      const SizedBox(width: 16),
                      NeonButton(
                        text: _isEditMode ? 'Mettre à jour' : 'Enregistrer',
                        icon: FontAwesomeIcons.floppyDisk,
                        color: NeonTheme.neonGreen,
                        isLoading: _isLoading,
                        onPressed: _submitForm,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityOption(
    String label,
    TaskPriority priority,
    Color color,
  ) {
    final isSelected = _priority == priority;
    return InkWell(
      onTap: () {
        setState(() {
          _priority = priority;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? color.withAlpha(77)
                  : Colors.transparent, // 0.3 * 255 ≈ 77
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.white,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              priority == TaskPriority.high
                  ? Icons.arrow_upward
                  : priority == TaskPriority.medium
                  ? Icons.remove
                  : Icons.arrow_downward,
              color: isSelected ? color : Colors.white,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: NeonTheme.neonPurple,
              onPrimary: Colors.white,
              surface: Colors.black,
              onSurface: Colors.white,
            ),
            dialogTheme: const DialogTheme(backgroundColor: Colors.black),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          _dueTime.hour,
          _dueTime.minute,
        );
      });
    }
  }

  Future<void> _selectDueTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _dueTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: NeonTheme.neonPurple,
              onPrimary: Colors.white,
              surface: Colors.black,
              onSurface: Colors.white,
            ),
            dialogTheme: const DialogTheme(backgroundColor: Colors.black),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _dueTime) {
      setState(() {
        _dueTime = picked;
        _dueDate = DateTime(
          _dueDate.year,
          _dueDate.month,
          _dueDate.day,
          _dueTime.hour,
          _dueTime.minute,
        );
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint(
        _isEditMode
            ? 'Mise à jour de la tâche...'
            : 'Création d\'une nouvelle tâche...',
      );

      // Préparer les tags
      final tags =
          _tagsController.text.isEmpty
              ? <String>[]
              : _tagsController.text
                  .split(',')
                  .map((tag) => tag.trim())
                  .toList();

      debugPrint('Tags: $tags');

      // Obtenir le nom de l'assigné si nécessaire
      String? assignedToName;
      if (_assignedToId != null) {
        try {
          assignedToName =
              ref
                  .read(teamMembersProvider)
                  .firstWhere((member) => member.id == _assignedToId)
                  .name;
          debugPrint('Tâche assignée à: $assignedToName (ID: $_assignedToId)');
        } catch (e) {
          debugPrint('Erreur lors de la récupération du nom de l\'assigné: $e');
        }
      }

      if (_isEditMode && _taskToEdit != null) {
        // Mettre à jour la tâche existante
        final updatedTask = _taskToEdit!.copyWith(
          title: _titleController.text,
          description: _descriptionController.text,
          priority: _priority,
          dueDate: _dueDate,
          updatedAt: DateTime.now(),
          assignedToId: _assignedToId,
          assignedToName: assignedToName,
          reminderTime: _hasReminder ? _reminderTime : null,
          tags: tags,
          hasReminder: _hasReminder,
        );

        debugPrint(
          'Tâche mise à jour: ${updatedTask.title} (ID: ${updatedTask.id})',
        );
        debugPrint('Date d\'échéance: ${updatedTask.dueDate}');
        debugPrint('Priorité: ${updatedTask.priority}');

        // Mettre à jour la tâche
        ref.read(tasksProvider.notifier).updateTask(updatedTask);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tâche mise à jour avec succès'),
              backgroundColor: Colors.green,
            ),
          );

          // Attendre un peu pour que l'utilisateur voie le message
          Future.delayed(const Duration(seconds: 1)).then((_) {
            if (mounted) {
              context.pop();
            }
          });
        }
      } else {
        // Créer une nouvelle tâche
        final task = Task(
          id: const Uuid().v4(),
          title: _titleController.text,
          description: _descriptionController.text,
          status: TaskStatus.todo,
          priority: _priority,
          dueDate: _dueDate,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          assignedToId: _assignedToId,
          assignedToName: assignedToName,
          parentTaskId: widget.parentTaskId,
          reminderTime: _hasReminder ? _reminderTime : null,
          tags: tags,
          hasReminder: _hasReminder,
          isRecurring: false,
        );

        debugPrint('Tâche créée: ${task.title} (ID: ${task.id})');
        debugPrint('Date d\'échéance: ${task.dueDate}');
        debugPrint('Priorité: ${task.priority}');

        // Vérifier l'état actuel des tâches
        final currentTasks = ref.read(tasksProvider);
        debugPrint('Nombre de tâches avant ajout: ${currentTasks.length}');

        // Ajouter la tâche
        ref.read(tasksProvider.notifier).addTask(task);

        // Vérifier que la tâche a bien été ajoutée
        final updatedTasks = ref.read(tasksProvider);
        debugPrint('Nombre de tâches après ajout: ${updatedTasks.length}');

        // Vérifier si la tâche est dans la liste
        final taskAdded = updatedTasks.any((t) => t.id == task.id);
        debugPrint('La tâche a-t-elle été ajoutée? $taskAdded');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tâche créée avec succès'),
              backgroundColor: Colors.green,
            ),
          );

          // Attendre un peu pour que l'utilisateur voie le message
          Future.delayed(const Duration(seconds: 1)).then((_) {
            if (mounted) {
              context.pop();
            }
          });
        }
      }
    } catch (e) {
      debugPrint(
        'Erreur lors de la ${_isEditMode ? 'mise à jour' : 'création'} de la tâche: $e',
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
