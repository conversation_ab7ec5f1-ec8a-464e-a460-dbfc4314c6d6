import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:csv/csv.dart';
import '../models/customer/customer.dart';

class CSVImporter {
  /// Importe des contacts à partir d'un fichier CSV
  static Future<List<Customer>> importFromFile() async {
    try {
      // Sélectionner un fichier CSV
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        return [];
      }

      // Lire le contenu du fichier
      final file = result.files.first;
      String content;
      
      if (file.bytes != null) {
        // Web ou fichier en mémoire
        content = String.fromCharCodes(file.bytes!);
      } else if (file.path != null) {
        // Fichier sur le disque
        content = await File(file.path!).readAsString();
      } else {
        return [];
      }

      // Parser le contenu CSV
      return _parseCSVContent(content);
    } catch (e) {
      debugPrint('Erreur lors de l\'importation du fichier CSV: $e');
      return [];
    }
  }

  /// Parse le contenu d'un fichier CSV
  static List<Customer> _parseCSVContent(String content) {
    final List<Customer> customers = [];
    
    try {
      // Convertir le contenu CSV en liste de listes
      final List<List<dynamic>> rowsAsListOfValues = const CsvToListConverter().convert(content);
      
      // Vérifier si le fichier contient des données
      if (rowsAsListOfValues.isEmpty) {
        return [];
      }
      
      // Récupérer les en-têtes
      final headers = rowsAsListOfValues[0].map((e) => e.toString().toLowerCase()).toList();
      
      // Indices des colonnes
      int nameIndex = headers.indexOf('nom');
      if (nameIndex == -1) nameIndex = headers.indexOf('name');
      
      int emailIndex = headers.indexOf('email');
      if (emailIndex == -1) emailIndex = headers.indexOf('e-mail');
      
      int phoneIndex = headers.indexOf('téléphone');
      if (phoneIndex == -1) phoneIndex = headers.indexOf('telephone');
      if (phoneIndex == -1) phoneIndex = headers.indexOf('phone');
      
      int addressIndex = headers.indexOf('adresse');
      if (addressIndex == -1) addressIndex = headers.indexOf('address');
      
      int companyIndex = headers.indexOf('société');
      if (companyIndex == -1) companyIndex = headers.indexOf('societe');
      if (companyIndex == -1) companyIndex = headers.indexOf('entreprise');
      if (companyIndex == -1) companyIndex = headers.indexOf('company');
      
      // Parcourir les lignes (en ignorant l'en-tête)
      for (int i = 1; i < rowsAsListOfValues.length; i++) {
        final row = rowsAsListOfValues[i];
        
        // Vérifier si la ligne contient suffisamment de colonnes
        if (row.length <= nameIndex || nameIndex == -1) {
          continue;
        }
        
        // Récupérer les valeurs
        final name = row[nameIndex].toString();
        final email = emailIndex != -1 && row.length > emailIndex ? row[emailIndex].toString() : '';
        final phone = phoneIndex != -1 && row.length > phoneIndex ? row[phoneIndex].toString() : '';
        final address = addressIndex != -1 && row.length > addressIndex ? row[addressIndex].toString() : '';
        final company = companyIndex != -1 && row.length > companyIndex ? row[companyIndex].toString() : '';
        
        // Créer un client à partir des informations extraites
        if (name.isNotEmpty) {
          customers.add(Customer(
            id: DateTime.now().millisecondsSinceEpoch.toString() + i.toString(),
            name: name,
            phone: phone.isNotEmpty ? phone : null,
            email: email.isNotEmpty ? email : null,
            address: address.isNotEmpty ? address : null,
            company: company.isNotEmpty ? company : null,
            createdAt: DateTime.now(),
            lastContact: DateTime.now(),
            status: CustomerStatus.lead,
            tags: ['importé', 'csv'],
          ));
        }
      }
    } catch (e) {
      debugPrint('Erreur lors du parsing du fichier CSV: $e');
    }
    
    return customers;
  }
}
