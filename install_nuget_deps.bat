@echo off
echo Installing NuGet dependencies for flutter_inappwebview_windows...

REM Create packages directory if it doesn't exist
mkdir -p build\windows\x64\packages

REM Install required packages
%USERPROFILE%\bin\nuget.exe install Microsoft.Windows.ImplementationLibrary -Version 1.0.231216.1 -ExcludeVersion -OutputDirectory build\windows\x64\packages
%USERPROFILE%\bin\nuget.exe install Microsoft.Web.WebView2 -Version 1.0.2792.45 -ExcludeVersion -OutputDirectory build\windows\x64\packages
%USERPROFILE%\bin\nuget.exe install nlohmann.json -Version 3.11.2 -ExcludeVersion -OutputDirectory build\windows\x64\packages

echo NuGet dependencies installed successfully!
