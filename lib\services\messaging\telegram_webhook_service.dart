import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shelf/shelf.dart' as shelf;
import 'package:shelf/shelf_io.dart' as shelf_io;
import '../../models/messaging/message.dart';
import 'telegram_service.dart';

// Provider pour le service de webhook Telegram
final telegramWebhookServiceProvider = Provider<TelegramWebhookService>((ref) {
  final telegramService = ref.watch(telegramServiceProvider);
  return TelegramWebhookService(telegramService);
});

class TelegramWebhookService {
  final TelegramService _telegramService;
  HttpServer? _server;
  bool _isRunning = false;
  final List<Function(Message)> _messageHandlers = [];

  TelegramWebhookService(this._telegramService);

  // Vérifier si le serveur est en cours d'exécution
  bool get isRunning => _isRunning;

  // Démarrer le serveur webhook
  Future<bool> startWebhookServer({
    required String host,
    required int port,
    required String path,
    required String webhookUrl,
  }) async {
    if (_isRunning) {
      debugPrint('Le serveur webhook Telegram est déjà en cours d\'exécution');
      return true;
    }

    try {
      // Configurer le webhook Telegram
      final webhookSet = await _telegramService.setWebhook('$webhookUrl$path');
      if (!webhookSet) {
        debugPrint('Échec de la configuration du webhook Telegram');
        return false;
      }

      // Créer le gestionnaire de requêtes
      final handler = const shelf.Pipeline()
          .addMiddleware(shelf.logRequests())
          .addHandler(_handleRequest);

      // Démarrer le serveur
      _server = await shelf_io.serve(handler, host, port);
      _isRunning = true;

      debugPrint('Serveur webhook Telegram démarré sur $host:$port$path');
      return true;
    } catch (e) {
      debugPrint('Erreur lors du démarrage du serveur webhook Telegram: $e');
      return false;
    }
  }

  // Arrêter le serveur webhook
  Future<bool> stopWebhookServer() async {
    if (!_isRunning || _server == null) {
      debugPrint(
        'Le serveur webhook Telegram n\'est pas en cours d\'exécution',
      );
      return true;
    }

    try {
      // Supprimer le webhook Telegram
      await _telegramService.deleteWebhook();

      // Arrêter le serveur
      await _server!.close(force: true);
      _server = null;
      _isRunning = false;

      debugPrint('Serveur webhook Telegram arrêté');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt du serveur webhook Telegram: $e');
      return false;
    }
  }

  // Ajouter un gestionnaire de messages
  void addMessageHandler(Function(Message) handler) {
    _messageHandlers.add(handler);
  }

  // Supprimer un gestionnaire de messages
  void removeMessageHandler(Function(Message) handler) {
    _messageHandlers.remove(handler);
  }

  // Gérer les requêtes entrantes
  Future<shelf.Response> _handleRequest(shelf.Request request) async {
    try {
      if (request.method != 'POST') {
        return shelf.Response(405, body: 'Method Not Allowed');
      }

      final body = await request.readAsString();
      final update = jsonDecode(body);

      // Traiter la mise à jour
      if (update['message'] != null) {
        final message = _parseMessage(update['message']);
        if (message != null) {
          // Notifier tous les gestionnaires de messages
          for (final handler in _messageHandlers) {
            handler(message);
          }
        }
      }

      return shelf.Response.ok('OK');
    } catch (e) {
      debugPrint(
        'Erreur lors du traitement de la requête webhook Telegram: $e',
      );
      return shelf.Response.internalServerError(body: 'Internal Server Error');
    }
  }

  // Convertir un message Telegram en Message
  Message? _parseMessage(Map<String, dynamic> telegramMessage) {
    try {
      final messageId = telegramMessage['message_id'].toString();
      final chatId = telegramMessage['chat']['id'].toString();
      final fromUser = telegramMessage['from'] != null;
      final text = telegramMessage['text'] ?? '';
      final timestamp = telegramMessage['date'] as int;
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

      return Message(
        id: messageId,
        contactId: chatId,
        content: text,
        isUser:
            !fromUser, // Inverse car isUser signifie "envoyé par l'utilisateur de l'app"
        timestamp: date,
        channel: MessageChannel.telegram,
        status: MessageStatus.delivered,
      );
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse du message Telegram: $e');
      return null;
    }
  }
}
