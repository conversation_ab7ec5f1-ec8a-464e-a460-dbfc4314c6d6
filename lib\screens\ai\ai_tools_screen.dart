import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../providers/theme_provider.dart';
import '../../services/ai/gemini_service.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class AIToolsScreen extends ConsumerStatefulWidget {
  const AIToolsScreen({super.key});

  @override
  ConsumerState<AIToolsScreen> createState() => _AIToolsScreenState();
}

class _AIToolsScreenState extends ConsumerState<AIToolsScreen> {
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _contextController = TextEditingController();
  
  String _generatedContent = '';
  bool _isLoading = false;
  String _selectedTool = 'email';
  String _selectedTone = 'professionnel';

  @override
  void dispose() {
    _inputController.dispose();
    _contextController.dispose();
    super.dispose();
  }

  // Générer du contenu avec l'outil sélectionné
  Future<void> _generateContent() async {
    if (_inputController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez entrer du texte')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _generatedContent = '';
    });

    try {
      final geminiService = ref.read(geminiServiceProvider);
      
      if (!geminiService.isConfigured) {
        setState(() {
          _generatedContent = "Veuillez configurer l'API Gemini dans les paramètres.";
          _isLoading = false;
        });
        return;
      }

      String result;
      
      switch (_selectedTool) {
        case 'email':
          result = await geminiService.generateEmailResponse(
            clientName: _inputController.text,
            subject: 'Suivi de notre conversation',
            context: _contextController.text.isEmpty ? null : _contextController.text,
            tone: _selectedTone,
          );
          break;
        
        case 'sentiment':
          result = await geminiService.analyzeSentiment(_inputController.text);
          break;
        
        case 'summary':
          result = await _generateSummary(_inputController.text);
          break;
        
        case 'suggestions':
          result = await _generateSuggestions();
          break;
        
        default:
          result = "Outil non reconnu";
      }

      setState(() {
        _generatedContent = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _generatedContent = "Erreur: $e";
        _isLoading = false;
      });
    }
  }

  // Générer un résumé
  Future<String> _generateSummary(String text) async {
    final geminiService = ref.read(geminiServiceProvider);
    
    final prompt = '''
    Résume le texte suivant en conservant les points clés et les informations importantes.
    Fais un résumé concis et clair.
    
    Texte à résumer:
    "$text"
    ''';
    
    return geminiService.generateContent(prompt);
  }

  // Générer des suggestions
  Future<String> _generateSuggestions() async {
    final geminiService = ref.read(geminiServiceProvider);
    
    final clientName = _inputController.text;
    final context = _contextController.text;
    
    final prompt = '''
    En tant qu'assistant commercial, génère des suggestions personnalisées pour le client suivant:
    
    Nom du client: $clientName
    Contexte: $context
    
    Fournis 3 suggestions de produits ou services que ce client pourrait apprécier, avec une brève explication pour chaque suggestion.
    
    Format de la réponse:
    1. [Suggestion 1] - [Explication]
    2. [Suggestion 2] - [Explication]
    3. [Suggestion 3] - [Explication]
    ''';
    
    return geminiService.generateContent(prompt);
  }

  @override
  Widget build(BuildContext context) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);
    final primaryColor = appTheme.primaryColor;
    final secondaryColor = appTheme.secondaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Outils IA pour CRM'),
        backgroundColor: Colors.black,
        foregroundColor: primaryColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.chat),
            tooltip: 'Assistant IA',
            onPressed: () {
              context.push('/ai/gemini');
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Paramètres IA',
            onPressed: () {
              context.push('/ai/gemini/settings');
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(gradient: appTheme.mainGradient),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sélection de l'outil
              NeonCard(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sélectionnez un outil IA',
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Options d'outils
                      Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: [
                          _buildToolOption(
                            'email',
                            'Rédaction d\'email',
                            FontAwesomeIcons.envelope,
                            primaryColor,
                          ),
                          _buildToolOption(
                            'sentiment',
                            'Analyse de sentiment',
                            FontAwesomeIcons.faceLaughBeam,
                            primaryColor,
                          ),
                          _buildToolOption(
                            'summary',
                            'Résumé de texte',
                            FontAwesomeIcons.fileLines,
                            primaryColor,
                          ),
                          _buildToolOption(
                            'suggestions',
                            'Suggestions client',
                            FontAwesomeIcons.lightbulb,
                            primaryColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Options supplémentaires pour l'outil sélectionné
              if (_selectedTool == 'email')
                NeonCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Ton de l\'email',
                          style: TextStyle(
                            color: primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          children: [
                            _buildToneChip('professionnel', 'Professionnel', primaryColor),
                            _buildToneChip('amical', 'Amical', primaryColor),
                            _buildToneChip('formel', 'Formel', primaryColor),
                            _buildToneChip('persuasif', 'Persuasif', primaryColor),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              
              const SizedBox(height: 16),
              
              // Champ de saisie principal
              NeonTextField(
                controller: _inputController,
                labelText: _getInputLabel(),
                hintText: _getInputHint(),
                maxLines: 5,
              ),
              
              const SizedBox(height: 16),
              
              // Champ de contexte (pour certains outils)
              if (_selectedTool == 'email' || _selectedTool == 'suggestions')
                NeonTextField(
                  controller: _contextController,
                  labelText: 'Contexte (optionnel)',
                  hintText: _selectedTool == 'email'
                      ? 'Ex: Client intéressé par nos services de consultation'
                      : 'Ex: A acheté des produits X et Y, intéressé par la technologie',
                  maxLines: 3,
                ),
              
              const SizedBox(height: 16),
              
              // Bouton de génération
              Center(
                child: NeonButton(
                  text: 'Générer avec IA',
                  icon: FontAwesomeIcons.wandMagicSparkles,
                  color: secondaryColor,
                  isLoading: _isLoading,
                  onPressed: _generateContent,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Résultat généré
              if (_generatedContent.isNotEmpty || _isLoading)
                NeonCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Résultat',
                              style: TextStyle(
                                color: primaryColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.copy, color: Colors.white),
                              tooltip: 'Copier le résultat',
                              onPressed: _generatedContent.isEmpty || _isLoading
                                  ? null
                                  : () {
                                      // Copier le texte dans le presse-papier
                                    },
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _isLoading
                            ? Center(
                                child: Column(
                                  children: [
                                    CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Génération en cours...',
                                      style: TextStyle(color: primaryColor),
                                    ),
                                  ],
                                ),
                              )
                            : SelectableText(
                                _generatedContent,
                                style: const TextStyle(color: Colors.white),
                              ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Construire une option d'outil
  Widget _buildToolOption(String value, String label, IconData icon, Color color) {
    final isSelected = _selectedTool == value;
    
    return InkWell(
      onTap: () {
        setState(() {
          _selectedTool = value;
          _generatedContent = '';
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? color.withValues(alpha: 51) // 0.2 * 255 ≈ 51
              : Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? color
                : Colors.white.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.white,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Construire une puce de ton
  Widget _buildToneChip(String value, String label, Color color) {
    final isSelected = _selectedTone == value;
    
    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      selectedColor: color.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      backgroundColor: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
      labelStyle: TextStyle(
        color: isSelected ? color : Colors.white,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedTone = value;
          });
        }
      },
    );
  }

  // Obtenir le libellé du champ de saisie en fonction de l'outil sélectionné
  String _getInputLabel() {
    switch (_selectedTool) {
      case 'email':
        return 'Nom du client';
      case 'sentiment':
        return 'Texte à analyser';
      case 'summary':
        return 'Texte à résumer';
      case 'suggestions':
        return 'Nom du client';
      default:
        return 'Texte';
    }
  }

  // Obtenir l'indice du champ de saisie en fonction de l'outil sélectionné
  String _getInputHint() {
    switch (_selectedTool) {
      case 'email':
        return 'Ex: Jean Dupont';
      case 'sentiment':
        return 'Ex: Je suis très satisfait de votre service, merci beaucoup !';
      case 'summary':
        return 'Collez ici le texte que vous souhaitez résumer...';
      case 'suggestions':
        return 'Ex: Marie Martin';
      default:
        return '';
    }
  }
}
