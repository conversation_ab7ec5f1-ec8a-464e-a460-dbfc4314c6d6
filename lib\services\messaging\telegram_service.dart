import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/telegram_config.dart';
import '../../constants/telegram_constants.dart';

// Provider pour la configuration Telegram
final telegramConfigProvider =
    StateNotifierProvider<TelegramConfigNotifier, TelegramConfig>((ref) {
      return TelegramConfigNotifier();
    });

// Provider pour le service Telegram
final telegramServiceProvider = Provider<TelegramService>((ref) {
  final config = ref.watch(telegramConfigProvider);
  return TelegramService(config);
});

// Provider pour vérifier si Telegram est configuré
final isTelegramConfiguredProvider = Provider<bool>((ref) {
  final config = ref.watch(telegramConfigProvider);
  return config.apiToken.isNotEmpty;
});

class TelegramConfigNotifier extends StateNotifier<TelegramConfig> {
  TelegramConfigNotifier() : super(TelegramConfig.empty()) {
    _loadConfig();
  }

  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString('telegram_config');

      if (configJson != null) {
        state = TelegramConfig.fromJson(configJson);
      } else {
        // Utiliser le token par défaut si aucun n'est configuré
        state = TelegramConfig(
          apiToken: defaultTelegramToken,
          lastUpdateId: 0,
          enabled: true,
        );
        // Sauvegarder la configuration par défaut
        await updateConfig(state);
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement de la configuration Telegram: $e');
    }
  }

  Future<void> updateConfig(TelegramConfig config) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('telegram_config', config.toJson());
      state = config;
    } catch (e) {
      debugPrint(
        'Erreur lors de la mise à jour de la configuration Telegram: $e',
      );
    }
  }
}

class TelegramService {
  final TelegramConfig _config;
  final String _baseUrl = 'https://api.telegram.org/bot';

  TelegramService(this._config);

  // Vérifier si le service est configuré
  bool get isConfigured => _config.apiToken.isNotEmpty;

  // Obtenir l'URL de base pour les requêtes API
  String get _apiUrl => '$_baseUrl${_config.apiToken}';

  // Obtenir les mises à jour (nouveaux messages)
  Future<List<Message>> getUpdates() async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final url = Uri.parse(
        '$_apiUrl/getUpdates?offset=${_config.lastUpdateId + 1}',
      );
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['ok'] == true && data['result'] != null) {
          final updates = data['result'] as List;

          if (updates.isEmpty) {
            return [];
          }

          // Mettre à jour l'ID de la dernière mise à jour
          final lastUpdateId = updates.last['update_id'] as int;

          // Mettre à jour la configuration avec le dernier ID
          _config.copyWith(lastUpdateId: lastUpdateId);

          // Convertir les mises à jour en messages
          final messages = <Message>[];

          for (final update in updates) {
            if (update['message'] != null) {
              final message = _parseMessage(update['message']);
              if (message != null) {
                messages.add(message);
              }
            }
          }

          return messages;
        }
      }

      return [];
    } catch (e) {
      debugPrint(
        'Erreur lors de la récupération des mises à jour Telegram: $e',
      );
      return [];
    }
  }

  // Envoyer un message
  Future<bool> sendMessage(String chatId, String text) async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final url = Uri.parse('$_apiUrl/sendMessage');
      final response = await http.post(
        url,
        body: {'chat_id': chatId, 'text': text, 'parse_mode': 'HTML'},
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message Telegram: $e');
      return false;
    }
  }

  // Envoyer un message à partir d'un objet Message
  Future<bool> sendMessageObject(Message message) async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      if (message.mediaUrl != null && message.mediaUrl!.isNotEmpty) {
        if (message.mediaType == MessageType.image) {
          return sendPhoto(
            message.contactId,
            message.mediaUrl!,
            caption: message.content.isNotEmpty ? message.content : null,
          );
        } else if (message.mediaType == MessageType.video ||
            message.mediaType == MessageType.document ||
            message.mediaType == MessageType.file) {
          // Telegram utilise sendDocument pour les vidéos et autres fichiers
          return sendDocument(
            message.contactId,
            message.mediaUrl!,
            caption: message.content.isNotEmpty ? message.content : null,
          );
        } else if (message.mediaType == MessageType.audio) {
          //  TODO: Implémenter sendAudio si nécessaire, pour l'instant, on envoie comme document
          return sendDocument(
            message.contactId,
            message.mediaUrl!,
            caption: message.content.isNotEmpty ? message.content : null,
          );
        }
      }
      // Si pas de média ou type non géré, envoyer comme texte simple
      return sendMessage(message.contactId, message.content);
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du message Telegram (objet): $e');
      return false;
    }
  }

  // Envoyer une image
  Future<bool> sendPhoto(
    String chatId,
    String photoUrl, {
    String? caption,
  }) async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final url = Uri.parse('$_apiUrl/sendPhoto');
      final body = {'chat_id': chatId, 'photo': photoUrl};

      if (caption != null) {
        body['caption'] = caption;
      }

      final response = await http.post(url, body: body);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi de la photo Telegram: $e');
      return false;
    }
  }

  // Envoyer un fichier
  Future<bool> sendDocument(
    String chatId,
    String documentUrl, {
    String? caption,
  }) async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final url = Uri.parse('$_apiUrl/sendDocument');
      final body = {'chat_id': chatId, 'document': documentUrl};

      if (caption != null) {
        body['caption'] = caption;
      }

      final response = await http.post(url, body: body);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi du document Telegram: $e');
      return false;
    }
  }

  // Obtenir les informations sur le bot
  Future<Map<String, dynamic>?> getMe() async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final url = Uri.parse('$_apiUrl/getMe');
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['ok'] == true && data['result'] != null) {
          return data['result'] as Map<String, dynamic>;
        }
      }

      return null;
    } catch (e) {
      debugPrint(
        'Erreur lors de la récupération des informations du bot Telegram: $e',
      );
      return null;
    }
  }

  // Configurer un webhook pour recevoir les mises à jour
  Future<bool> setWebhook(String url) async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final apiUrl = Uri.parse('$_apiUrl/setWebhook');
      final response = await http.post(
        apiUrl,
        body: {
          'url': url,
          'allowed_updates': jsonEncode(['message', 'callback_query']),
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['ok'] == true) {
          debugPrint('Webhook Telegram configuré avec succès: $url');
          return true;
        }
      }

      debugPrint(
        'Échec de la configuration du webhook Telegram: ${response.body}',
      );
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la configuration du webhook Telegram: $e');
      return false;
    }
  }

  // Supprimer le webhook
  Future<bool> deleteWebhook() async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final apiUrl = Uri.parse('$_apiUrl/deleteWebhook');
      final response = await http.get(apiUrl);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['ok'] == true) {
          debugPrint('Webhook Telegram supprimé avec succès');
          return true;
        }
      }

      debugPrint(
        'Échec de la suppression du webhook Telegram: ${response.body}',
      );
      return false;
    } catch (e) {
      debugPrint('Erreur lors de la suppression du webhook Telegram: $e');
      return false;
    }
  }

  // Obtenir les informations sur le webhook actuel
  Future<Map<String, dynamic>?> getWebhookInfo() async {
    if (!isConfigured) {
      throw Exception('Telegram n\'est pas configuré');
    }

    try {
      final apiUrl = Uri.parse('$_apiUrl/getWebhookInfo');
      final response = await http.get(apiUrl);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['ok'] == true && data['result'] != null) {
          return data['result'] as Map<String, dynamic>;
        }
      }

      return null;
    } catch (e) {
      debugPrint(
        'Erreur lors de la récupération des informations du webhook Telegram: $e',
      );
      return null;
    }
  }

  // Convertir un message Telegram en Message
  Message? _parseMessage(Map<String, dynamic> telegramMessage) {
    try {
      final messageId = telegramMessage['message_id'].toString();
      final chatId = telegramMessage['chat']['id'].toString();
      final fromUser = telegramMessage['from'] != null;
      final text = telegramMessage['text'] ?? '';
      final timestamp = telegramMessage['date'] as int;
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

      return Message(
        id: messageId,
        contactId: chatId,
        content: text,
        isUser:
            !fromUser, // Inverse car isUser signifie "envoyé par l'utilisateur de l'app"
        timestamp: date,
        channel: MessageChannel.telegram, // Utiliser telegram pour Telegram
        status: MessageStatus.delivered,
      );
    } catch (e) {
      debugPrint('Erreur lors de l\'analyse du message Telegram: $e');
      return null;
    }
  }
}
