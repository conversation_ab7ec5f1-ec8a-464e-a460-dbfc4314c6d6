import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/auth_service.dart';
import '../../theme/neon_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/user.dart';

final registerLoadingProvider = StateProvider<bool>((ref) => false);
final registerErrorProvider = StateProvider<String?>((ref) => null);

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final String _selectedRole = AppConstants.userRoleAgent;
  final _departmentController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      ref.read(registerLoadingProvider.notifier).state = true;
      ref.read(registerErrorProvider.notifier).state = null;

      // Créer un objet User
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(), // ID temporaire
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        role: _selectedRole,
        department:
            _departmentController.text.isEmpty
                ? null
                : _departmentController.text.trim(),
        avatar: null,
        phone: null,
        bio: null,
      );

      final bool success = await ref
          .read(authServiceProvider)
          .register(user, _passwordController.text);

      ref.read(registerLoadingProvider.notifier).state = false;

      if (!mounted) return;

      if (success) {
        context.go('/dashboard');
      } else {
        ref.read(registerErrorProvider.notifier).state =
            'Erreur lors de l\'inscription. Cet email est peut-être déjà utilisé.';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(registerLoadingProvider);
    final error = ref.watch(registerErrorProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.mainGradient),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo et titre
                Icon(
                  Icons.business_center,
                  size: 60,
                  color: NeonTheme.neonCyan,
                  shadows: [
                    Shadow(
                      color: NeonTheme.neonCyan.withValues(
                        alpha: 204,
                      ), // 0.8 * 255 ≈ 204
                      blurRadius: 12.0,
                    ),
                    Shadow(
                      color: NeonTheme.neonCyan.withValues(
                        alpha: 128,
                      ), // 0.5 * 255 ≈ 128
                      blurRadius: 24.0,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Créer un compte',
                  textAlign: TextAlign.center,
                  style: NeonTheme.neonTextStyle(
                    NeonTheme.neonCyan,
                  ).copyWith(fontSize: 24),
                ),
                const SizedBox(height: 32),

                // Formulaire d'inscription
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Afficher l'erreur s'il y en a une
                      if (error != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade900.withValues(
                              alpha: 51,
                            ), // 0.2 * 255 ≈ 51
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade800),
                          ),
                          child: Text(
                            error,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Champ nom
                      _buildTextField(
                        controller: _nameController,
                        label: 'Nom complet',
                        hint: 'Entrez votre nom complet',
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer votre nom';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Champ email
                      _buildTextField(
                        controller: _emailController,
                        label: 'Email',
                        hint: 'Entrez votre email',
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer votre email';
                          }
                          if (!RegExp(
                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                          ).hasMatch(value)) {
                            return 'Veuillez entrer un email valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Champ mot de passe
                      _buildTextField(
                        controller: _passwordController,
                        label: 'Mot de passe',
                        hint: 'Entrez votre mot de passe',
                        icon: Icons.lock,
                        obscureText: _obscurePassword,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscurePassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: NeonTheme.neonPurple,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscurePassword = !_obscurePassword;
                            });
                          },
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez entrer votre mot de passe';
                          }
                          if (value.length < 6) {
                            return 'Le mot de passe doit contenir au moins 6 caractères';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Champ confirmation mot de passe
                      _buildTextField(
                        controller: _confirmPasswordController,
                        label: 'Confirmer le mot de passe',
                        hint: 'Confirmez votre mot de passe',
                        icon: Icons.lock_outline,
                        obscureText: _obscureConfirmPassword,
                        suffixIcon: IconButton(
                          icon: Icon(
                            _obscureConfirmPassword
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: NeonTheme.neonPurple,
                          ),
                          onPressed: () {
                            setState(() {
                              _obscureConfirmPassword =
                                  !_obscureConfirmPassword;
                            });
                          },
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Veuillez confirmer votre mot de passe';
                          }
                          if (value != _passwordController.text) {
                            return 'Les mots de passe ne correspondent pas';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Bouton d'inscription
                      _buildNeonButton(
                        text: 'S\'inscrire',
                        isLoading: isLoading,
                        onPressed: _register,
                      ),
                      const SizedBox(height: 16),

                      // Lien vers la connexion
                      TextButton(
                        onPressed: () {
                          context.go('/login');
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: NeonTheme.neonCyan,
                        ),
                        child: const Text('Déjà un compte ? Connectez-vous'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 51), // 0.2 * 255 ≈ 51
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: NeonTheme.neonPurple.withValues(alpha: 77), // 0.3 * 255 ≈ 77
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: Icon(icon, color: NeonTheme.neonCyan),
          suffixIcon: suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          labelStyle: TextStyle(
            color: Colors.white.withValues(alpha: 179),
          ), // 0.7 * 255 ≈ 179
          hintStyle: TextStyle(
            color: Colors.white.withValues(alpha: 77),
          ), // 0.3 * 255 ≈ 77
        ),
        style: const TextStyle(color: Colors.white),
        keyboardType: keyboardType,
        obscureText: obscureText,
        validator: validator,
      ),
    );
  }

  Widget _buildNeonButton({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: NeonTheme.activeGradient,
        borderRadius: BorderRadius.circular(12),
        boxShadow: NeonTheme.neonShadow(NeonTheme.neonCyan),
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child:
            isLoading
                ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
                : Text(
                  text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
      ),
    );
  }
}
