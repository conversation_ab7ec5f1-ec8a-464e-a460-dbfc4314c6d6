import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../models/customer/customer.dart';
import '../../services/customer/customer_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/user_avatar.dart';

// Provider pour un client spécifique
final customerProvider = FutureProvider.family<Customer?, String>((
  ref,
  id,
) async {
  final customerService = ref.watch(customerServiceProvider);
  return customerService.getCustomerById(id);
});

class CustomerDetailScreen extends ConsumerWidget {
  final String customerId;

  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerAsync = ref.watch(customerProvider(customerId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du Client'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // Navigation vers l'écran d'édition (à implémenter)
              // context.push('/customers/edit/$customerId');
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: customerAsync.when(
          data: (customer) {
            if (customer == null) {
              return const Center(
                child: Text(
                  'Client non trouvé',
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
              );
            }
            return _buildCustomerDetail(context, ref, customer);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
        ),
      ),
    );
  }

  Widget _buildCustomerDetail(
    BuildContext context,
    WidgetRef ref,
    Customer customer,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCustomerHeader(customer),
          const SizedBox(height: 24),
          _buildContactInfo(customer),
          const SizedBox(height: 24),
          _buildCustomerNotes(customer),
          const SizedBox(height: 24),
          _buildActionButtons(context, ref, customer),
        ],
      ),
    );
  }

  Widget _buildCustomerHeader(Customer customer) {
    final statusColor = _getStatusColor(customer.status);

    return NeonCard(
      color: NeonTheme.neonCyan,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            UserAvatar(name: customer.name, size: 80),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(
                        alpha: 51,
                      ), // 0.2 * 255 ≈ 51
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: statusColor, width: 1),
                    ),
                    child: Text(
                      _getStatusLabel(customer.status),
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Client depuis: ${_formatDate(customer.createdAt)}',
                    style: TextStyle(
                      color: Colors.white.withValues(
                        alpha: 179,
                      ), // 0.7 * 255 ≈ 179
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(Customer customer) {
    return NeonCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations de contact',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (customer.email != null)
              _buildInfoRow(Icons.email, 'Email', customer.email!),
            if (customer.phone != null)
              _buildInfoRow(Icons.phone, 'Téléphone', customer.phone!),
            if (customer.source != null)
              _buildInfoRow(Icons.source, 'Source', customer.source!),
            if (customer.tags.isNotEmpty) _buildTagsRow(customer.tags),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerNotes(Customer customer) {
    return NeonCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notes',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              customer.notes ?? 'Aucune note',
              style: const TextStyle(color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    WidgetRef ref,
    Customer customer,
  ) {
    return Column(
      children: [
        // Boutons de contact
        Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withAlpha(26),
            ), // 0.1 * 255 ≈ 26
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Contacter le client',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  if (customer.phone != null)
                    _buildContactButton(
                      icon: Icons.phone,
                      label: 'Appeler',
                      color: Colors.green,
                      onTap: () => _makePhoneCall(customer.phone!),
                    ),
                  if (customer.phone != null)
                    _buildContactButton(
                      icon: FontAwesomeIcons.commentSms,
                      label: 'SMS',
                      color: Colors.orange,
                      onTap: () {
                        final params = {
                          'customerId': customer.id,
                          'customerName': customer.name,
                          'phoneNumber': customer.phone,
                        };
                        context.push('/settings/sms/send', extra: params);
                      },
                    ),
                  if (customer.phone != null)
                    _buildContactButton(
                      icon: FontAwesomeIcons.whatsapp,
                      label: 'WhatsApp',
                      color: Colors.green.shade700,
                      onTap: () => _openWhatsApp(customer.phone!),
                    ),
                  if (customer.email != null)
                    _buildContactButton(
                      icon: Icons.email,
                      label: 'Email',
                      color: Colors.red,
                      onTap: () => _sendEmail(customer.email!),
                    ),
                  _buildContactButton(
                    icon: FontAwesomeIcons.telegram,
                    label: 'Telegram',
                    color: Colors.lightBlue,
                    onTap: () => _showTelegramDialog(context, customer),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Boutons d'action
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            NeonButton(
              text: 'Envoyer un SMS',
              icon: FontAwesomeIcons.commentSms,
              color: Colors.green,
              onPressed: () {
                // Navigation vers l'écran d'envoi de SMS avec les informations du client
                final params = {
                  'customerId': customer.id,
                  'customerName': customer.name,
                  'phoneNumber': customer.phone,
                };
                context.push('/settings/sms/send', extra: params);
              },
            ),
            const SizedBox(width: 16),
            NeonButton(
              text: 'Historique SMS',
              icon: FontAwesomeIcons.clockRotateLeft,
              color: Colors.green,
              onPressed: () {
                // Navigation vers l'historique des SMS pour ce client
                context.push('/settings/sms/history/${customer.id}');
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            NeonButton(
              text: 'Envoyer un message',
              icon: FontAwesomeIcons.message,
              color: NeonTheme.neonCyan,
              onPressed: () {
                // Navigation vers la messagerie
                context.push('/messaging');
              },
            ),
            const SizedBox(width: 16),
            NeonButton(
              text: 'Créer une tâche',
              icon: FontAwesomeIcons.listCheck,
              color: NeonTheme.neonGreen,
              onPressed: () {
                // Navigation vers la création de tâche
                context.push('/tasks/add');
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: NeonTheme.neonCyan, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white.withValues(
                      alpha: 179,
                    ), // 0.7 * 255 ≈ 179
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsRow(List<String> tags) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.tag, color: NeonTheme.neonCyan, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tags',
                  style: TextStyle(
                    color: Colors.white.withValues(
                      alpha: 179,
                    ), // 0.7 * 255 ≈ 179
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: tags.map((tag) => _buildTag(tag)).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: NeonTheme.neonPurple.withValues(alpha: 51), // 0.2 * 255 ≈ 51
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: NeonTheme.neonPurple, width: 1),
      ),
      child: Text(
        tag,
        style: const TextStyle(color: NeonTheme.neonPurple, fontSize: 12),
      ),
    );
  }

  Color _getStatusColor(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.lead:
        return Colors.orange;
      case CustomerStatus.customer:
        return Colors.green;
      case CustomerStatus.vip:
        return NeonTheme.neonPurple;
      case CustomerStatus.inactive:
        return Colors.grey;
      case CustomerStatus.blocked:
        return Colors.red;
    }
  }

  String _getStatusLabel(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.lead:
        return 'Prospect';
      case CustomerStatus.customer:
        return 'Client';
      case CustomerStatus.vip:
        return 'VIP';
      case CustomerStatus.inactive:
        return 'Inactif';
      case CustomerStatus.blocked:
        return 'Bloqué';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withAlpha(25), // 0.1 * 255 ≈ 25
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withAlpha(77)), // 0.3 * 255 ≈ 77
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      throw 'Impossible d\'appeler $phoneNumber';
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Contact depuis l\'application CRM',
    );
    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      throw 'Impossible d\'envoyer un email à $email';
    }
  }

  Future<void> _openWhatsApp(String phoneNumber) async {
    // Formater le numéro de téléphone (supprimer les espaces et les caractères spéciaux)
    final formattedPhone = phoneNumber.replaceAll(RegExp(r'[^0-9+]'), '');

    // Construire l'URL WhatsApp
    final whatsappUrl = 'https://wa.me/$formattedPhone';

    // Ouvrir WhatsApp
    final Uri uri = Uri.parse(whatsappUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      throw 'Impossible d\'ouvrir WhatsApp pour $phoneNumber';
    }
  }

  void _showTelegramDialog(BuildContext context, Customer customer) {
    showDialog(
      context: context,
      builder: (context) {
        final TextEditingController usernameController =
            TextEditingController();

        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A2E),
          title: const Text(
            'Contacter via Telegram',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: usernameController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  labelText: 'Nom d\'utilisateur Telegram',
                  labelStyle: TextStyle(color: Colors.white70),
                  hintText: 'Sans le @',
                  hintStyle: TextStyle(color: Colors.white30),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.white30),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.lightBlue),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                final username = usernameController.text.trim();

                if (username.isNotEmpty) {
                  // Construire l'URL Telegram
                  final telegramUrl = 'https://t.me/$username';

                  // Ouvrir Telegram
                  final Uri uri = Uri.parse(telegramUrl);
                  if (await canLaunchUrl(uri)) {
                    await launchUrl(uri, mode: LaunchMode.externalApplication);
                  } else {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Impossible d\'ouvrir Telegram'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.lightBlue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Contacter'),
            ),
          ],
        );
      },
    );
  }
}
