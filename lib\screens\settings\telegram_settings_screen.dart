import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/messaging/telegram_config.dart';
import '../../services/messaging/telegram_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_text_field.dart';

class TelegramSettingsScreen extends ConsumerStatefulWidget {
  const TelegramSettingsScreen({super.key});

  @override
  ConsumerState<TelegramSettingsScreen> createState() =>
      _TelegramSettingsScreenState();
}

class _TelegramSettingsScreenState
    extends ConsumerState<TelegramSettingsScreen> {
  final _apiTokenController = TextEditingController();
  final _webhookUrlController = TextEditingController();
  bool _isEnabled = false;
  bool _isLoading = false;
  bool _isTestingConnection = false;
  String? _botInfo;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  @override
  void dispose() {
    _apiTokenController.dispose();
    _webhookUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadConfig() async {
    final config = ref.read(telegramConfigProvider);

    setState(() {
      _apiTokenController.text = config.apiToken;
      _webhookUrlController.text = config.webhookUrl ?? '';
      _isEnabled = config.enabled;
    });
  }

  Future<void> _saveConfig() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final currentConfig = ref.read(telegramConfigProvider);
      final newConfig = currentConfig.copyWith(
        apiToken: _apiTokenController.text,
        webhookUrl:
            _webhookUrlController.text.isNotEmpty
                ? _webhookUrlController.text
                : null,
        enabled: _isEnabled,
      );

      await ref.read(telegramConfigProvider.notifier).updateConfig(newConfig);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration Telegram enregistrée'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur lors de l\'enregistrement: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testConnection() async {
    if (_apiTokenController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Veuillez entrer un token API';
      });
      return;
    }

    setState(() {
      _isTestingConnection = true;
      _botInfo = null;
      _errorMessage = null;
    });

    try {
      // Créer une configuration temporaire pour le test
      final tempConfig = TelegramConfig(
        apiToken: _apiTokenController.text,
        lastUpdateId: 0,
        enabled: true,
        webhookUrl:
            _webhookUrlController.text.isNotEmpty
                ? _webhookUrlController.text
                : null,
      );

      // Créer un service temporaire avec cette configuration
      final telegramService = TelegramService(tempConfig);

      // Tester la connexion en récupérant les informations du bot
      final botInfo = await telegramService.getMe();

      if (botInfo != null) {
        setState(() {
          _botInfo =
              'Bot connecté: ${botInfo['first_name']} (@${botInfo['username']})';
        });
      } else {
        setState(() {
          _errorMessage = 'Impossible de se connecter au bot';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur de connexion: $e';
      });
    } finally {
      setState(() {
        _isTestingConnection = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration Telegram'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      NeonCard(
                        color: NeonTheme.neonBlue,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Configuration du Bot Telegram',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              NeonTextField(
                                controller: _apiTokenController,
                                labelText: 'Token API du Bot',
                                hintText:
                                    'Exemple: 1234567890:ABCDefGhIJKlmnOPQRstUVwxYZ',
                                obscureText: true,
                              ),
                              const SizedBox(height: 16),
                              NeonTextField(
                                controller: _webhookUrlController,
                                labelText: 'URL Webhook (optionnel)',
                                hintText: 'https://votre-serveur.com/webhook',
                              ),
                              const SizedBox(height: 16),
                              SwitchListTile(
                                title: const Text(
                                  'Activer Telegram',
                                  style: TextStyle(color: Colors.white),
                                ),
                                value: _isEnabled,
                                activeColor: NeonTheme.neonBlue,
                                onChanged: (value) {
                                  setState(() {
                                    _isEnabled = value;
                                  });
                                },
                              ),
                              const SizedBox(height: 16),
                              Center(
                                child: NeonButton(
                                  text: 'Configurer le Webhook',
                                  icon: FontAwesomeIcons.link,
                                  color: const Color(0xFF0088cc),
                                  onPressed: () {
                                    context.push('/settings/telegram/webhook');
                                  },
                                ),
                              ),
                              if (_errorMessage != null) ...[
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withValues(
                                      alpha: 51,
                                    ), // 0.2 * 255 ≈ 51
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.red),
                                  ),
                                  child: Text(
                                    _errorMessage!,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ],
                              if (_botInfo != null) ...[
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withValues(
                                      alpha: 51,
                                    ), // 0.2 * 255 ≈ 51
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.green),
                                  ),
                                  child: Text(
                                    _botInfo!,
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ],
                              const SizedBox(height: 24),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  NeonButton(
                                    text: 'Tester la connexion',
                                    icon: FontAwesomeIcons.vial,
                                    color: NeonTheme.neonPurple,
                                    isLoading: _isTestingConnection,
                                    onPressed: _testConnection,
                                  ),
                                  NeonButton(
                                    text: 'Enregistrer',
                                    icon: FontAwesomeIcons.floppyDisk,
                                    color: NeonTheme.neonGreen,
                                    onPressed: _saveConfig,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      const NeonCard(
                        color: NeonTheme.neonCyan,
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Comment configurer un bot Telegram',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 16),
                              Text(
                                '1. Ouvrez Telegram et recherchez @BotFather',
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(height: 8),
                              Text(
                                '2. Envoyez la commande /newbot',
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(height: 8),
                              Text(
                                '3. Suivez les instructions pour créer votre bot',
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(height: 8),
                              Text(
                                '4. Copiez le token API fourni par BotFather',
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(height: 8),
                              Text(
                                '5. Collez le token dans le champ ci-dessus',
                                style: TextStyle(color: Colors.white),
                              ),
                              SizedBox(height: 8),
                              Text(
                                '6. Cliquez sur "Enregistrer" pour activer l\'intégration',
                                style: TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }
}
