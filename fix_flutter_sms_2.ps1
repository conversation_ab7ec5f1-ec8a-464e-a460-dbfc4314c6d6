Write-Host "Fixing flutter_sms build.gradle file..."

$gradleFile = "$env:LOCALAPPDATA\Pub\Cache\hosted\pub.dev\flutter_sms-2.3.3\android\build.gradle"

if (-not (Test-Path $gradleFile)) {
    Write-Host "File not found: $gradleFile"
    exit 1
}

Write-Host "Original build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Adding namespace to build.gradle..."

$content = Get-Content $gradleFile -Raw
$androidBlockStart = $content.IndexOf("android {")
$androidBlockContent = $content.Substring($androidBlockStart)
$newAndroidBlock = "android {`n    namespace `"com.babariviere.sms`"`n" + $androidBlockContent.Substring("android {".Length)
$newContent = $content.Substring(0, $androidBlockStart) + $newAndroidBlock

$newContent | Set-Content $gradleFile

Write-Host ""
Write-Host "Modified build.gradle content:"
Get-Content $gradleFile

Write-Host ""
Write-Host "Fix completed!"
