class KnowledgeArticle {
  final String id;
  final String title;
  final String content;
  final String category;
  final List<String> tags;
  final DateTime lastUpdated;
  final bool isPublished;
  final String? imageUrl;
  final String? videoUrl;

  KnowledgeArticle({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.tags,
    required this.lastUpdated,
    this.isPublished = true,
    this.imageUrl,
    this.videoUrl,
  });

  factory KnowledgeArticle.fromJson(Map<String, dynamic> json) {
    return KnowledgeArticle(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      category: json['category'] as String,
      tags: List<String>.from(json['tags'] as List),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      isPublished: json['is_published'] as bool? ?? true,
      imageUrl: json['image_url'] as String?,
      videoUrl: json['video_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'tags': tags,
      'last_updated': lastUpdated.toIso8601String(),
      'is_published': isPublished,
      'image_url': imageUrl,
      'video_url': videoUrl,
    };
  }
}
