import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/theme_provider.dart';
import '../theme/neon_theme.dart';

/// Bouton pour basculer entre les modes clair et sombre
class ThemeToggleButton extends ConsumerWidget {
  final bool showLabel;
  final double size;

  const ThemeToggleButton({
    super.key,
    this.showLabel = false,
    this.size = 24.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final isDarkMode = themeMode.isDark;

    return InkWell(
      onTap: () {
        ref.read(themeProvider.notifier).toggleTheme();
      },
      borderRadius: BorderRadius.circular(30),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              isDarkMode
                  ? Colors.black.withValues(alpha: 77) // 0.3 * 255 ≈ 77
                  : Colors.white.withValues(alpha: 230), // 0.9 * 255 ≈ 230
          borderRadius: BorderRadius.circular(30),
          border: Border.all(
            color: isDarkMode ? NeonTheme.neonCyan : NeonTheme.neonPurple,
            width: 1.5,
          ),
          boxShadow:
              isDarkMode
                  ? NeonTheme.neonShadow(NeonTheme.neonCyan, intensity: 0.5)
                  : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isDarkMode ? FontAwesomeIcons.sun : FontAwesomeIcons.moon,
              size: size,
              color: isDarkMode ? NeonTheme.neonCyan : NeonTheme.neonPurple,
            ),
            if (showLabel) ...[
              const SizedBox(width: 8),
              Text(
                isDarkMode ? 'Mode clair' : 'Mode sombre',
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black87,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
