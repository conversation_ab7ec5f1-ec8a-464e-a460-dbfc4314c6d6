import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/messaging/telegram_service.dart';
import '../../services/messaging/telegram_webhook_service.dart';
import '../../theme/neon_theme.dart';

class TelegramWebhookScreen extends ConsumerStatefulWidget {
  const TelegramWebhookScreen({super.key});

  @override
  ConsumerState<TelegramWebhookScreen> createState() =>
      _TelegramWebhookScreenState();
}

class _TelegramWebhookScreenState extends ConsumerState<TelegramWebhookScreen> {
  final _formKey = GlobalKey<FormState>();
  final _webhookUrlController = TextEditingController();
  final _hostController = TextEditingController(text: '0.0.0.0');
  final _portController = TextEditingController(text: '8443');
  final _pathController = TextEditingController(text: '/telegram/webhook');

  bool _isLoading = false;
  bool _isServerRunning = false;
  Map<String, dynamic>? _webhookInfo;

  @override
  void initState() {
    super.initState();
    _loadWebhookInfo();
  }

  @override
  void dispose() {
    _webhookUrlController.dispose();
    _hostController.dispose();
    _portController.dispose();
    _pathController.dispose();
    super.dispose();
  }

  // Charger les informations du webhook
  Future<void> _loadWebhookInfo() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final telegramService = ref.read(telegramServiceProvider);
      final webhookInfo = await telegramService.getWebhookInfo();

      if (!mounted) return;

      setState(() {
        _webhookInfo = webhookInfo;
        if (webhookInfo != null && webhookInfo['url'] != null) {
          _webhookUrlController.text = webhookInfo['url'];
          _isServerRunning = webhookInfo['url'].toString().isNotEmpty;
        }
      });
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Configurer le webhook
  void _setupWebhook() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final webhookUrl = _webhookUrlController.text;
    final host = _hostController.text;
    final port = int.parse(_portController.text);
    final path = _pathController.text;

    final telegramWebhookService = ref.read(telegramWebhookServiceProvider);

    telegramWebhookService
        .startWebhookServer(
          host: host,
          port: port,
          path: path,
          webhookUrl: webhookUrl,
        )
        .then((success) {
          if (!mounted) return;

          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Webhook configuré avec succès'),
                backgroundColor: Colors.green,
              ),
            );

            // Mettre à jour la configuration Telegram
            final telegramConfigNotifier = ref.read(
              telegramConfigProvider.notifier,
            );
            final currentConfig = ref.read(telegramConfigProvider);
            telegramConfigNotifier
                .updateConfig(currentConfig.copyWith(webhookUrl: webhookUrl))
                .then((_) {
                  // Recharger les informations du webhook
                  _loadWebhookInfo();
                });
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Échec de la configuration du webhook'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        })
        .catchError((e) {
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
          );
          setState(() {
            _isLoading = false;
          });
        });
  }

  // Supprimer le webhook
  void _deleteWebhook() {
    setState(() {
      _isLoading = true;
    });

    final telegramWebhookService = ref.read(telegramWebhookServiceProvider);

    telegramWebhookService
        .stopWebhookServer()
        .then((success) {
          if (!mounted) return;

          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Webhook supprimé avec succès'),
                backgroundColor: Colors.green,
              ),
            );

            // Mettre à jour la configuration Telegram
            final telegramConfigNotifier = ref.read(
              telegramConfigProvider.notifier,
            );
            final currentConfig = ref.read(telegramConfigProvider);
            telegramConfigNotifier
                .updateConfig(currentConfig.copyWith(webhookUrl: null))
                .then((_) {
                  // Recharger les informations du webhook
                  _loadWebhookInfo();
                });
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Échec de la suppression du webhook'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() {
              _isLoading = false;
            });
          }
        })
        .catchError((e) {
          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
          );
          setState(() {
            _isLoading = false;
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration du Webhook Telegram'),
        backgroundColor: Colors.black,
        foregroundColor: NeonTheme.primaryAccent,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(
                  color: NeonTheme.primaryAccent,
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 16),
                    _buildWebhookForm(),
                  ],
                ),
              ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      color: Colors.black.withAlpha(153), // 0.6 * 255 ≈ 153
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Colors.cyan.withAlpha(77),
          width: 1,
        ), // 0.3 * 255 ≈ 77
      ),
      elevation: 4,
      shadowColor: Colors.cyan.withAlpha(77), // 0.3 * 255 ≈ 77
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations sur le Webhook',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            if (_webhookInfo != null) ...[
              _buildInfoRow('URL', _webhookInfo!['url'] ?? 'Non configuré'),
              _buildInfoRow(
                'Dernier code d\'erreur',
                _webhookInfo!['last_error_code']?.toString() ?? 'Aucun',
              ),
              _buildInfoRow(
                'Dernier message d\'erreur',
                _webhookInfo!['last_error_message'] ?? 'Aucun',
              ),
              _buildInfoRow(
                'Nombre maximum de connexions',
                _webhookInfo!['max_connections']?.toString() ?? 'Non défini',
              ),
              _buildInfoRow(
                'Mises à jour autorisées',
                (_webhookInfo!['allowed_updates'] as List?)?.join(', ') ??
                    'Toutes',
              ),
            ] else
              const Text(
                'Aucune information disponible',
                style: TextStyle(color: Colors.white70),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(color: Colors.white70)),
          ),
        ],
      ),
    );
  }

  Widget _buildWebhookForm() {
    return Form(
      key: _formKey,
      child: Card(
        color: Colors.black.withAlpha(153), // 0.6 * 255 ≈ 153
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: Colors.cyan.withAlpha(77), // 0.3 * 255 ≈ 77
            width: 1,
          ),
        ),
        elevation: 4,
        shadowColor: Colors.cyan.withAlpha(77), // 0.3 * 255 ≈ 77
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Configuration du Webhook',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _webhookUrlController,
                decoration: const InputDecoration(
                  labelText: 'URL du Webhook (https://...)',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer une URL';
                  }
                  if (!value.startsWith('https://')) {
                    return 'L\'URL doit commencer par https://';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _hostController,
                decoration: const InputDecoration(
                  labelText: 'Hôte du serveur',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer un hôte';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _portController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Port du serveur',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer un port';
                  }
                  final port = int.tryParse(value);
                  if (port == null || port <= 0 || port > 65535) {
                    return 'Port invalide (1-65535)';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _pathController,
                decoration: const InputDecoration(
                  labelText: 'Chemin du webhook',
                  labelStyle: TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.cyan, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.red, width: 2),
                  ),
                ),
                style: const TextStyle(color: Colors.white),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer un chemin';
                  }
                  if (!value.startsWith('/')) {
                    return 'Le chemin doit commencer par /';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: _isServerRunning ? null : () => _setupWebhook(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyan,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Configurer'),
                  ),
                  ElevatedButton(
                    onPressed: _isServerRunning ? () => _deleteWebhook() : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Supprimer'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
