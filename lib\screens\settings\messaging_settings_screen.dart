import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/channel_config.dart';
import '../../services/messaging/messaging_service.dart';
import '../../services/messaging/telegram_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class MessagingSettingsScreen extends ConsumerStatefulWidget {
  const MessagingSettingsScreen({super.key});

  @override
  ConsumerState<MessagingSettingsScreen> createState() =>
      _MessagingSettingsScreenState();
}

class _MessagingSettingsScreenState
    extends ConsumerState<MessagingSettingsScreen> {
  bool _isLoading = true;
  final Map<MessageChannel, Map<String, TextEditingController>> _controllers =
      {};
  final Map<MessageChannel, bool> _useAI = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Charger les configurations des canaux
      await ref.read(channelConfigsProvider.notifier).loadChannelConfigs();

      // Initialiser les contrôleurs
      final configs = ref.read(channelConfigsProvider);

      for (final config in configs) {
        _controllers[config.channel] = {
          'apiKey': TextEditingController(text: config.apiKey ?? ''),
          'apiSecret': TextEditingController(text: config.apiSecret ?? ''),
          'phoneNumber': TextEditingController(text: config.phoneNumber ?? ''),
          'accountId': TextEditingController(text: config.accountId ?? ''),
          'pageId': TextEditingController(text: config.pageId ?? ''),
        };

        _useAI[config.channel] = config.useAI;
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des configurations: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    // Disposer les contrôleurs
    for (final controllers in _controllers.values) {
      for (final controller in controllers.values) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final configs = ref.watch(channelConfigsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration de la messagerie'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Container(
                decoration: const BoxDecoration(
                  gradient: NeonTheme.secondaryGradient,
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Canaux de messagerie',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Configurez les différents canaux de messagerie pour communiquer avec vos clients.',
                        style: TextStyle(color: Colors.white),
                      ),
                      const SizedBox(height: 24),

                      // WhatsApp
                      _buildChannelCard(
                        MessageChannel.whatsapp,
                        'WhatsApp Business',
                        'Intégrez WhatsApp Business pour communiquer avec vos clients via WhatsApp.',
                        FontAwesomeIcons.whatsapp,
                        Colors.green,
                        configs.firstWhere(
                          (c) => c.channel == MessageChannel.whatsapp,
                        ),
                      ),

                      // Facebook Messenger
                      _buildChannelCard(
                        MessageChannel.facebook,
                        'Facebook Messenger',
                        'Intégrez Facebook Messenger pour communiquer avec vos clients via Facebook.',
                        FontAwesomeIcons.facebookMessenger,
                        Colors.blue,
                        configs.firstWhere(
                          (c) => c.channel == MessageChannel.facebook,
                        ),
                      ),

                      // SMS
                      _buildChannelCard(
                        MessageChannel.sms,
                        'SMS',
                        'Intégrez un service SMS pour communiquer avec vos clients via SMS.',
                        FontAwesomeIcons.commentSms,
                        Colors.orange,
                        configs.firstWhere(
                          (c) => c.channel == MessageChannel.sms,
                        ),
                      ),

                      // Email
                      _buildChannelCard(
                        MessageChannel.email,
                        'Email',
                        'Intégrez un service d\'email pour communiquer avec vos clients via email.',
                        FontAwesomeIcons.envelope,
                        Colors.red,
                        configs.firstWhere(
                          (c) => c.channel == MessageChannel.email,
                        ),
                      ),

                      // Telegram
                      NeonCard(
                        color: const Color(
                          0xFF0088cc,
                        ), // Couleur officielle de Telegram
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.telegram,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                  const SizedBox(width: 16),
                                  const Text(
                                    'Telegram',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Spacer(),
                                  Consumer(
                                    builder: (context, ref, child) {
                                      final isConfigured = ref.watch(
                                        isTelegramConfiguredProvider,
                                      );
                                      final config = ref.watch(
                                        telegramConfigProvider,
                                      );
                                      return Switch(
                                        value: config.enabled,
                                        onChanged: (value) {
                                          if (isConfigured) {
                                            ref
                                                .read(
                                                  telegramConfigProvider
                                                      .notifier,
                                                )
                                                .updateConfig(
                                                  config.copyWith(
                                                    enabled: value,
                                                  ),
                                                );
                                          } else if (value) {
                                            context.push('/settings/telegram');
                                          }
                                        },
                                        activeColor: const Color(0xFF0088cc),
                                        activeTrackColor: Colors.white
                                            .withValues(alpha: 128),
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Intégrez Telegram pour communiquer avec vos clients via Telegram.',
                                style: TextStyle(
                                  color: Colors.white.withValues(
                                    alpha: 230,
                                  ), // 0.9 * 255 ≈ 230
                                ),
                              ),
                              const SizedBox(height: 16),
                              Center(
                                child: NeonButton(
                                  text: 'Configurer Telegram',
                                  icon: FontAwesomeIcons.gear,
                                  color: const Color(0xFF0088cc),
                                  onPressed: () {
                                    context.push('/settings/telegram');
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Bouton de sauvegarde
                      Center(
                        child: NeonButton(
                          text: 'Enregistrer les modifications',
                          icon: FontAwesomeIcons.floppyDisk,
                          color: NeonTheme.neonGreen,
                          onPressed: _saveConfigurations,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildChannelCard(
    MessageChannel channel,
    String title,
    String description,
    IconData icon,
    Color color,
    ChannelConfig config,
  ) {
    return NeonCard(
      color: color,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: config.isEnabled,
                  onChanged: (value) {
                    final notifier = ref.read(channelConfigsProvider.notifier);
                    notifier.updateChannelConfig(
                      config.copyWith(isEnabled: value),
                    );
                  },
                  activeColor: color,
                  activeTrackColor: Colors.white.withValues(
                    alpha: 128,
                  ), // 0.5 * 255 ≈ 128
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 230), // 0.9 * 255 ≈ 230
              ),
            ),
            const SizedBox(height: 16),

            // Formulaire de configuration
            if (config.isEnabled)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(color: Colors.white30),
                  const SizedBox(height: 16),

                  // Champs spécifiques à chaque canal
                  ..._buildChannelFields(channel),

                  const SizedBox(height: 16),

                  // Option pour utiliser l'IA
                  Row(
                    children: [
                      const Text(
                        'Utiliser l\'IA pour les réponses automatiques',
                        style: TextStyle(color: Colors.white),
                      ),
                      const Spacer(),
                      Switch(
                        value: _useAI[channel] ?? false,
                        onChanged: (value) {
                          setState(() {
                            _useAI[channel] = value;
                          });
                        },
                        activeColor: color,
                        activeTrackColor: Colors.white.withValues(
                          alpha: 128,
                        ), // 0.5 * 255 ≈ 128
                      ),
                    ],
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildChannelFields(MessageChannel channel) {
    final controllers = _controllers[channel]!;

    switch (channel) {
      case MessageChannel.whatsapp:
        return [
          NeonTextField(
            controller: controllers['apiKey']!,
            labelText: 'Clé API',
            hintText: 'Entrez votre clé API WhatsApp Business',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['phoneNumber']!,
            labelText: 'Numéro de téléphone',
            hintText: 'Entrez votre numéro de téléphone WhatsApp Business',
          ),
          const SizedBox(height: 16),
          Center(
            child: NeonButton(
              text: 'Configuration avancée',
              icon: FontAwesomeIcons.gear,
              color: Colors.green,
              onPressed: () {
                context.push('/settings/whatsapp');
              },
            ),
          ),
        ];

      case MessageChannel.facebook:
        return [
          NeonTextField(
            controller: controllers['apiKey']!,
            labelText: 'Clé API',
            hintText: 'Entrez votre clé API Facebook',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['pageId']!,
            labelText: 'ID de la page',
            hintText: 'Entrez l\'ID de votre page Facebook',
          ),
        ];

      case MessageChannel.sms:
        return [
          NeonTextField(
            controller: controllers['accountId']!,
            labelText: 'ID du compte',
            hintText: 'Entrez votre ID de compte Twilio',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['apiKey']!,
            labelText: 'Clé API',
            hintText: 'Entrez votre clé API Twilio',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['apiSecret']!,
            labelText: 'Secret API',
            hintText: 'Entrez votre secret API Twilio',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['phoneNumber']!,
            labelText: 'Numéro de téléphone',
            hintText: 'Entrez votre numéro de téléphone Twilio',
          ),
        ];

      case MessageChannel.email:
        return [
          NeonTextField(
            controller: controllers['apiKey']!,
            labelText: 'Clé API',
            hintText: 'Entrez votre clé API SendGrid',
          ),
          const SizedBox(height: 16),
          NeonTextField(
            controller: controllers['phoneNumber']!,
            labelText: 'Email d\'expédition',
            hintText: 'Entrez votre email d\'expédition',
          ),
        ];

      default:
        return [];
    }
  }

  Future<void> _saveConfigurations() async {
    try {
      final notifier = ref.read(channelConfigsProvider.notifier);
      final configs = ref.read(channelConfigsProvider);

      for (final config in configs) {
        final channel = config.channel;
        final controllers = _controllers[channel]!;

        final updatedConfig = config.copyWith(
          apiKey: controllers['apiKey']?.text,
          apiSecret: controllers['apiSecret']?.text,
          phoneNumber: controllers['phoneNumber']?.text,
          accountId: controllers['accountId']?.text,
          pageId: controllers['pageId']?.text,
          useAI: _useAI[channel] ?? false,
        );

        notifier.updateChannelConfig(updatedConfig);
      }

      await notifier.saveChannelConfigs();

      // Réinitialiser le service de messagerie
      final messagingService = ref.read(messagingServiceProvider);
      await messagingService.initialize(configs);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configurations enregistrées avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement des configurations: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
