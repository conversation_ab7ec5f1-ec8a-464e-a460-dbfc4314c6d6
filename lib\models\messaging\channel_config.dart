import 'dart:convert';
import 'message.dart';

class ChannelConfig {
  final MessageChannel channel;
  final bool isEnabled;
  final String? apiKey;
  final String? apiSecret;
  final String? phoneNumber;
  final String? accountId;
  final String? pageId;
  final bool useAI;
  final Map<String, dynamic>? additionalConfig;

  ChannelConfig({
    required this.channel,
    required this.isEnabled,
    this.apiKey,
    this.apiSecret,
    this.phoneNumber,
    this.accountId,
    this.pageId,
    this.useAI = false,
    this.additionalConfig,
  });

  ChannelConfig copyWith({
    MessageChannel? channel,
    bool? isEnabled,
    String? apiKey,
    String? apiSecret,
    String? phoneNumber,
    String? accountId,
    String? pageId,
    bool? useAI,
    Map<String, dynamic>? additionalConfig,
  }) {
    return ChannelConfig(
      channel: channel ?? this.channel,
      isEnabled: isEnabled ?? this.isEnabled,
      apiKey: apiKey ?? this.apiKey,
      apiSecret: apiSecret ?? this.apiSecret,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      accountId: accountId ?? this.accountId,
      pageId: pageId ?? this.pageId,
      useAI: useAI ?? this.useAI,
      additionalConfig: additionalConfig ?? this.additionalConfig,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'channel': channel.index,
      'isEnabled': isEnabled,
      'apiKey': apiKey,
      'apiSecret': apiSecret,
      'phoneNumber': phoneNumber,
      'accountId': accountId,
      'pageId': pageId,
      'useAI': useAI,
      'additionalConfig': additionalConfig,
    };
  }

  factory ChannelConfig.fromMap(Map<String, dynamic> map) {
    return ChannelConfig(
      channel: MessageChannel.values[map['channel']],
      isEnabled: map['isEnabled'] ?? false,
      apiKey: map['apiKey'],
      apiSecret: map['apiSecret'],
      phoneNumber: map['phoneNumber'],
      accountId: map['accountId'],
      pageId: map['pageId'],
      useAI: map['useAI'] ?? false,
      additionalConfig: map['additionalConfig'],
    );
  }

  String toJson() => json.encode(toMap());

  factory ChannelConfig.fromJson(String source) => ChannelConfig.fromMap(json.decode(source));

  @override
  String toString() {
    return 'ChannelConfig(channel: $channel, isEnabled: $isEnabled, apiKey: $apiKey, apiSecret: $apiSecret, phoneNumber: $phoneNumber, accountId: $accountId, pageId: $pageId, useAI: $useAI, additionalConfig: $additionalConfig)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is ChannelConfig &&
      other.channel == channel &&
      other.isEnabled == isEnabled &&
      other.apiKey == apiKey &&
      other.apiSecret == apiSecret &&
      other.phoneNumber == phoneNumber &&
      other.accountId == accountId &&
      other.pageId == pageId &&
      other.useAI == useAI;
  }

  @override
  int get hashCode {
    return channel.hashCode ^
      isEnabled.hashCode ^
      apiKey.hashCode ^
      apiSecret.hashCode ^
      phoneNumber.hashCode ^
      accountId.hashCode ^
      pageId.hashCode ^
      useAI.hashCode;
  }
}
