# Guide d'installation et de configuration de PocketBase pour NCRM

Ce guide vous aidera à installer et configurer PocketBase comme base de données pour votre application NCRM.

## 1. Téléchargement et installation de PocketBase

1. Téléchargez la dernière version de PocketBase depuis le site officiel : [https://pocketbase.io/docs/](https://pocketbase.io/docs/)
2. Extrayez le fichier téléchargé dans un dossier de votre choix (par exemple, `ncrm-backend`)
3. Ouvrez un terminal et naviguez vers ce dossier

## 2. Démarrage de PocketBase

Exécutez PocketBase avec la commande suivante :

```bash
# Sur Windows
pocketbase.exe serve

# Sur macOS/Linux
./pocketbase serve
```

PocketBase démarrera et sera accessible à l'adresse : http://127.0.0.1:8090

## 3. Configuration initiale

1. Accédez à http://127.0.0.1:8090/_/ pour ouvrir l'interface d'administration
2. Créez un compte administrateur
3. Vous serez redirigé vers le tableau de bord d'administration

## 4. Création des collections

Vous devez créer les collections suivantes dans PocketBase :

### Collection `users` (gérée automatiquement par PocketBase)
- Champs standard : email, password, name, avatar

### Collection `contacts`
- Créez une nouvelle collection nommée `contacts`
- Ajoutez les champs suivants :
  - `name` (text, required)
  - `phone` (text)
  - `email` (text)
  - `company` (text)
  - `avatar` (file)
  - `notes` (text)

### Collection `messages`
- Créez une nouvelle collection nommée `messages`
- Ajoutez les champs suivants :
  - `contact_id` (relation → contacts, required)
  - `content` (text, required)
  - `is_user` (boolean, required)
  - `timestamp` (date, required)
  - `media_url` (text)
  - `media_type` (text)
  - `channel` (text) - 'whatsapp', 'facebook', 'sms', 'app'

### Collection `tasks`
- Créez une nouvelle collection nommée `tasks`
- Ajoutez les champs suivants :
  - `title` (text, required)
  - `description` (text)
  - `contact_id` (relation → contacts)
  - `due_date` (date, required)
  - `status` (text, required) - 'pending', 'in_progress', 'completed'
  - `priority` (text, required) - 'low', 'medium', 'high'

### Collection `opportunities`
- Créez une nouvelle collection nommée `opportunities`
- Ajoutez les champs suivants :
  - `title` (text, required)
  - `contact_id` (relation → contacts, required)
  - `value` (number, required)
  - `status` (text, required) - 'lead', 'proposal', 'negotiation', 'won', 'lost'
  - `notes` (text)

### Collection `knowledge_base`
- Créez une nouvelle collection nommée `knowledge_base`
- Ajoutez les champs suivants :
  - `title` (text, required)
  - `content` (text, required)
  - `category` (text, required)
  - `tags` (json)

## 5. Configuration des règles d'accès

Pour chaque collection, configurez les règles d'accès suivantes :

1. Accès en lecture pour les utilisateurs authentifiés
2. Accès en écriture pour les utilisateurs authentifiés
3. Accès en lecture/écriture pour les administrateurs

## 6. Configuration de l'application Flutter

1. Assurez-vous que PocketBase est en cours d'exécution
2. Modifiez le fichier `lib/config/pocketbase_config.dart` si nécessaire pour correspondre à votre configuration
3. Exécutez l'application Flutter

## 7. Dépannage

### PocketBase ne démarre pas
- Vérifiez que le port 8090 n'est pas déjà utilisé
- Essayez d'exécuter PocketBase avec un port différent : `pocketbase.exe serve --http=127.0.0.1:8091`

### Problèmes de connexion
- Vérifiez que l'URL de PocketBase est correcte dans `lib/config/pocketbase_config.dart`
- Assurez-vous que PocketBase est en cours d'exécution
- Vérifiez les règles d'accès dans l'interface d'administration de PocketBase

### Erreurs d'authentification
- Vérifiez que vous avez créé un utilisateur dans la collection `users`
- Assurez-vous que les informations d'identification sont correctes

## 8. Sauvegarde et restauration

### Sauvegarde
- La base de données PocketBase est stockée dans un fichier `pb_data` dans le dossier où vous avez extrait PocketBase
- Pour sauvegarder, copiez simplement ce dossier

### Restauration
- Arrêtez PocketBase
- Remplacez le dossier `pb_data` par votre sauvegarde
- Redémarrez PocketBase
