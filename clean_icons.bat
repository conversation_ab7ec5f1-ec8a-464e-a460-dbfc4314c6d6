@echo off
echo Suppression des dossiers mipmap...
cd android\app\src\main\res
if exist mipmap-mdpi rmdir /s /q mipmap-mdpi
if exist mipmap-hdpi rmdir /s /q mipmap-hdpi
if exist mipmap-xhdpi rmdir /s /q mipmap-xhdpi
if exist mipmap-xxhdpi rmdir /s /q mipmap-xxhdpi
if exist mipmap-xxxhdpi rmdir /s /q mipmap-xxxhdpi
echo Dossiers supprimés avec succès.
echo Création du dossier mipmap-anydpi-v26 si nécessaire...
if not exist mipmap-anydpi-v26 mkdir mipmap-anydpi-v26
echo Script terminé.
cd ..\..\..\..\..
