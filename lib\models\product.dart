import 'dart:convert';

enum StockStatus { critical, low, normal }

class Product {
  final String id;
  final String name;
  final String categoryId;
  final String? categoryName; // Pour faciliter l'affichage
  final double price;
  final int quantity;
  final String? description;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  Product({
    required this.id,
    required this.name,
    required this.categoryId,
    this.categoryName,
    required this.price,
    required this.quantity,
    this.description,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  // Obtenir le statut du stock
  StockStatus get stockStatus {
    if (quantity <= 0) {
      return StockStatus.critical;
    } else if (quantity <= 5) {
      return StockStatus.low;
    } else {
      return StockStatus.normal;
    }
  }

  // Obtenir la couleur du statut du stock
  String getStatusLabel() {
    switch (stockStatus) {
      case StockStatus.critical:
        return 'Critique';
      case StockStatus.low:
        return 'Faible';
      case StockStatus.normal:
        return 'Normal';
    }
  }

  Product copyWith({
    String? id,
    String? name,
    String? categoryId,
    String? categoryName,
    double? price,
    int? quantity,
    String? description,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'price': price,
      'quantity': quantity,
      'description': description,
      'imageUrl': imageUrl,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      categoryId: map['categoryId'] ?? '',
      categoryName: map['categoryName'],
      price: map['price']?.toDouble() ?? 0.0,
      quantity: map['quantity']?.toInt() ?? 0,
      description: map['description'],
      imageUrl: map['imageUrl'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt']),
    );
  }

  String toJson() => json.encode(toMap());

  factory Product.fromJson(String source) =>
      Product.fromMap(json.decode(source));

  @override
  String toString() {
    return 'Product(id: $id, name: $name, categoryId: $categoryId, categoryName: $categoryName, price: $price, quantity: $quantity, description: $description, imageUrl: $imageUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Product &&
        other.id == id &&
        other.name == name &&
        other.categoryId == categoryId &&
        other.categoryName == categoryName &&
        other.price == price &&
        other.quantity == quantity &&
        other.description == description &&
        other.imageUrl == imageUrl &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        categoryId.hashCode ^
        categoryName.hashCode ^
        price.hashCode ^
        quantity.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }
}
