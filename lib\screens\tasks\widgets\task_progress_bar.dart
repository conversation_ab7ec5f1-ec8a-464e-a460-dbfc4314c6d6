import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/task/task.dart';
import '../../../providers/theme_provider.dart';

/// Widget qui affiche une barre de progression pour une tâche
/// montrant l'avancement vers l'échéance
class TaskProgressBar extends ConsumerWidget {
  final Task task;
  final double height;
  final double width;

  const TaskProgressBar({
    super.key,
    required this.task,
    this.height = 8.0,
    this.width = double.infinity,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Récupérer le thème actuel
    final appTheme = ref.watch(themeProvider);

    // Calculer le pourcentage de progression
    final progress = _calculateProgress();

    // Déterminer les couleurs en fonction de la progression
    final startColor = _getStartColor(appTheme);
    final endColor = _getEndColor(progress);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Texte d'information sur l'échéance
        Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Text(
            _getProgressText(),
            style: TextStyle(
              color:
                  ref.watch(isDarkThemeProvider)
                      ? _getTextColor(progress)
                      : ref.watch(primaryTextColorProvider),
              fontSize: 12,
              fontWeight: progress > 0.8 ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),

        // Barre de progression
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(height / 2),
          ),
          child: Stack(
            children: [
              // Fond de la barre
              Container(
                width: width,
                height: height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(height / 2),
                  color: Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
                ),
              ),

              // Barre de progression
              Container(
                width: width * progress,
                height: height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(height / 2),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [startColor, endColor],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Calcule le pourcentage de progression de la tâche vers l'échéance
  /// Retourne une valeur entre 0.0 et 1.0
  double _calculateProgress() {
    // Si la tâche est terminée, la progression est de 100%
    if (task.status == TaskStatus.completed) {
      return 1.0;
    }

    // Si la tâche est annulée, la progression est de 0%
    if (task.status == TaskStatus.cancelled) {
      return 0.0;
    }

    // Si la tâche est en retard, la progression est de 100%
    if (task.isOverdue) {
      return 1.0;
    }

    // Calculer le temps écoulé depuis la création de la tâche
    final now = DateTime.now();
    final creationDate = task.createdAt;
    final dueDate = task.dueDate;

    // Durée totale de la tâche
    final totalDuration = dueDate.difference(creationDate).inMinutes;
    if (totalDuration <= 0) {
      return 1.0; // Éviter la division par zéro
    }

    // Temps écoulé
    final elapsedDuration = now.difference(creationDate).inMinutes;

    // Calculer le pourcentage
    double progress = elapsedDuration / totalDuration;

    // Limiter entre 0 et 1
    return progress.clamp(0.0, 1.0);
  }

  /// Retourne la couleur de départ de la barre de progression
  Color _getStartColor(AppThemeMode appTheme) {
    return appTheme.primaryColor;
  }

  /// Retourne la couleur de fin de la barre de progression en fonction de la progression
  Color _getEndColor(double progress) {
    if (progress < 0.5) {
      return Colors.green; // Début de la tâche
    } else if (progress < 0.8) {
      return Colors.orange; // Milieu de la tâche
    } else {
      return Colors.red; // Proche de l'échéance
    }
  }

  /// Retourne la couleur du texte en fonction de la progression
  Color _getTextColor(double progress) {
    if (progress < 0.5) {
      return Colors.green; // Début de la tâche
    } else if (progress < 0.8) {
      return Colors.orange; // Milieu de la tâche
    } else {
      return Colors.red; // Proche de l'échéance
    }
  }

  /// Retourne le texte d'information sur l'échéance
  String _getProgressText() {
    if (task.status == TaskStatus.completed) {
      return 'Tâche terminée';
    }

    if (task.status == TaskStatus.cancelled) {
      return 'Tâche annulée';
    }

    if (task.isOverdue) {
      return 'En retard de ${task.daysOverdue} jour${task.daysOverdue > 1 ? 's' : ''}';
    }

    if (task.daysRemaining > 0) {
      return 'Échéance dans ${task.daysRemaining} jour${task.daysRemaining > 1 ? 's' : ''}';
    } else if (task.hoursRemaining > 0) {
      return 'Échéance dans ${task.hoursRemaining} heure${task.hoursRemaining > 1 ? 's' : ''}';
    } else {
      return 'Échéance aujourd\'hui';
    }
  }
}
