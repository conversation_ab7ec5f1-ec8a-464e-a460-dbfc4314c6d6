// Modèle d'élément de facture

class InvoiceItem {
  final String id;
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double total;
  final String? description;
  final double? tax;
  final double? discount;

  InvoiceItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.total,
    this.description,
    this.tax,
    this.discount,
  });

  // Créer une copie avec des modifications
  InvoiceItem copyWith({
    String? id,
    String? productId,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? total,
    String? description,
    double? tax,
    double? discount,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      total: total ?? this.total,
      description: description ?? this.description,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
    );
  }

  // Conversion depuis JSON
  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'] as String,
      productId: json['product_id'] as String,
      productName: json['product_name'] as String,
      quantity: json['quantity'] as int,
      unitPrice: (json['unit_price'] as num).toDouble(),
      total: (json['total'] as num).toDouble(),
      description: json['description'] as String?,
      tax: json['tax'] != null ? (json['tax'] as num).toDouble() : null,
      discount:
          json['discount'] != null
              ? (json['discount'] as num).toDouble()
              : null,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'product_name': productName,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total': total,
      'description': description,
      'tax': tax,
      'discount': discount,
    };
  }

  @override
  String toString() {
    return 'InvoiceItem{id: $id, productName: $productName, quantity: $quantity, total: $total}';
  }
}
