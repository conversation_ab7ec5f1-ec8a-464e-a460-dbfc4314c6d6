import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../models/task/task.dart';
import '../../services/task/task_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class TaskCreateScreen extends ConsumerStatefulWidget {
  final String? parentTaskId;

  const TaskCreateScreen({super.key, this.parentTaskId});

  @override
  ConsumerState<TaskCreateScreen> createState() => _TaskCreateScreenState();
}

class _TaskCreateScreenState extends ConsumerState<TaskCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();

  DateTime _dueDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _dueTime = TimeOfDay.now();
  TaskPriority _priority = TaskPriority.medium;
  String? _assignedToId;
  bool _hasReminder = false;
  DateTime? _reminderTime;
  bool _isRecurring = false;
  String _recurrencePattern = 'daily';
  int _occurrences = 5;

  bool _isLoading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final teamMembers = ref.watch(teamMembersProvider);
    final parentTask =
        widget.parentTaskId != null
            ? ref
                .watch(tasksProvider)
                .firstWhere((task) => task.id == widget.parentTaskId)
            : null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          parentTask != null ? 'Ajouter une sous-tâche' : 'Créer une tâche',
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (parentTask != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(
                        alpha: 77,
                      ), // 0.3 * 255 ≈ 77
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: NeonTheme.neonPurple),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Tâche parente:',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          parentTask.title,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),

                // Titre
                NeonTextField(
                  controller: _titleController,
                  labelText: 'Titre',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un titre';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                NeonTextField(
                  controller: _descriptionController,
                  labelText: 'Description',
                  maxLines: 5,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer une description';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Date d'échéance
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDueDate(context),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Date d\'échéance',
                            labelStyle: const TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: NeonTheme.neonPurple,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.black.withValues(
                              alpha: 153,
                            ), // 0.6 * 255 ≈ 153
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                DateFormat('dd/MM/yyyy').format(_dueDate),
                                style: const TextStyle(color: Colors.white),
                              ),
                              const Icon(
                                Icons.calendar_today,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDueTime(context),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'Heure',
                            labelStyle: const TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(color: Colors.white),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: const BorderSide(
                                color: NeonTheme.neonPurple,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.black.withValues(
                              alpha: 153,
                            ), // 0.6 * 255 ≈ 153
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _dueTime.format(context),
                                style: const TextStyle(color: Colors.white),
                              ),
                              const Icon(
                                Icons.access_time,
                                color: Colors.white,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Priorité
                const Text(
                  'Priorité',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildPriorityOption(
                      'Basse',
                      TaskPriority.low,
                      Colors.green,
                    ),
                    const SizedBox(width: 16),
                    _buildPriorityOption(
                      'Moyenne',
                      TaskPriority.medium,
                      Colors.orange,
                    ),
                    const SizedBox(width: 16),
                    _buildPriorityOption(
                      'Haute',
                      TaskPriority.high,
                      Colors.red,
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Assigné à
                const Text(
                  'Assigné à',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<String?>(
                  value: _assignedToId,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.white),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: NeonTheme.neonPurple,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.black.withValues(
                      alpha: 153,
                    ), // 0.6 * 255 ≈ 153
                  ),
                  dropdownColor: Colors.black,
                  style: const TextStyle(color: Colors.white),
                  items: [
                    const DropdownMenuItem<String?>(
                      value: null,
                      child: Text('Non assigné'),
                    ),
                    ...teamMembers.map((member) {
                      return DropdownMenuItem<String?>(
                        value: member.id,
                        child: Text(member.name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _assignedToId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Tags
                NeonTextField(
                  controller: _tagsController,
                  labelText: 'Tags (séparés par des virgules)',
                  hintText: 'Ex: urgent, client, design',
                ),
                const SizedBox(height: 16),

                // Rappel
                SwitchListTile(
                  title: const Text(
                    'Définir un rappel',
                    style: TextStyle(color: Colors.white),
                  ),
                  value: _hasReminder,
                  onChanged: (value) {
                    setState(() {
                      _hasReminder = value;
                      if (value) {
                        _reminderTime = _dueDate.subtract(
                          const Duration(hours: 24),
                        );
                      } else {
                        _reminderTime = null;
                      }
                    });
                  },
                  activeColor: NeonTheme.neonPurple,
                  activeTrackColor: Colors.white.withValues(
                    alpha: 128,
                  ), // 0.5 * 255 ≈ 128
                ),
                if (_hasReminder)
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 8),
                    child: Row(
                      children: [
                        const Text(
                          'Rappel:',
                          style: TextStyle(color: Colors.white),
                        ),
                        const SizedBox(width: 8),
                        DropdownButton<int>(
                          value:
                              _reminderTime != null
                                  ? _dueDate.difference(_reminderTime!).inHours
                                  : 24,
                          dropdownColor: Colors.black,
                          style: const TextStyle(color: Colors.white),
                          items: const [
                            DropdownMenuItem(
                              value: 1,
                              child: Text('1 heure avant'),
                            ),
                            DropdownMenuItem(
                              value: 2,
                              child: Text('2 heures avant'),
                            ),
                            DropdownMenuItem(
                              value: 4,
                              child: Text('4 heures avant'),
                            ),
                            DropdownMenuItem(
                              value: 24,
                              child: Text('1 jour avant'),
                            ),
                            DropdownMenuItem(
                              value: 48,
                              child: Text('2 jours avant'),
                            ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _reminderTime = _dueDate.subtract(
                                  Duration(hours: value),
                                );
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 16),

                // Récurrence
                SwitchListTile(
                  title: const Text(
                    'Tâche récurrente',
                    style: TextStyle(color: Colors.white),
                  ),
                  value: _isRecurring,
                  onChanged: (value) {
                    setState(() {
                      _isRecurring = value;
                    });
                  },
                  activeColor: NeonTheme.neonPurple,
                  activeTrackColor: Colors.white.withValues(
                    alpha: 128,
                  ), // 0.5 * 255 ≈ 128
                ),
                if (_isRecurring)
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Text(
                              'Fréquence:',
                              style: TextStyle(color: Colors.white),
                            ),
                            const SizedBox(width: 8),
                            DropdownButton<String>(
                              value: _recurrencePattern,
                              dropdownColor: Colors.black,
                              style: const TextStyle(color: Colors.white),
                              items: const [
                                DropdownMenuItem(
                                  value: 'daily',
                                  child: Text('Quotidienne'),
                                ),
                                DropdownMenuItem(
                                  value: 'weekly',
                                  child: Text('Hebdomadaire'),
                                ),
                                DropdownMenuItem(
                                  value: 'biweekly',
                                  child: Text('Bi-hebdomadaire'),
                                ),
                                DropdownMenuItem(
                                  value: 'monthly',
                                  child: Text('Mensuelle'),
                                ),
                                DropdownMenuItem(
                                  value: 'quarterly',
                                  child: Text('Trimestrielle'),
                                ),
                                DropdownMenuItem(
                                  value: 'yearly',
                                  child: Text('Annuelle'),
                                ),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _recurrencePattern = value;
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Text(
                              'Occurrences:',
                              style: TextStyle(color: Colors.white),
                            ),
                            const SizedBox(width: 8),
                            DropdownButton<int>(
                              value: _occurrences,
                              dropdownColor: Colors.black,
                              style: const TextStyle(color: Colors.white),
                              items: const [
                                DropdownMenuItem(value: 2, child: Text('2')),
                                DropdownMenuItem(value: 3, child: Text('3')),
                                DropdownMenuItem(value: 5, child: Text('5')),
                                DropdownMenuItem(value: 10, child: Text('10')),
                                DropdownMenuItem(value: 20, child: Text('20')),
                              ],
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _occurrences = value;
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 24),

                // Bouton de création
                Center(
                  child: NeonButton(
                    text: 'Créer la tâche',
                    icon: FontAwesomeIcons.plus,
                    color: NeonTheme.neonGreen,
                    isLoading: _isLoading,
                    onPressed: _createTask,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityOption(
    String label,
    TaskPriority priority,
    Color color,
  ) {
    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _priority = priority;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color:
                _priority == priority
                    ? color.withValues(alpha: 77) // 0.3 * 255 ≈ 77
                    : Colors.black.withValues(alpha: 77), // 0.3 * 255 ≈ 77
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  _priority == priority
                      ? color
                      : Colors.white.withValues(alpha: 77), // 0.3 * 255 ≈ 77
              width: _priority == priority ? 2 : 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                priority == TaskPriority.high
                    ? Icons.arrow_upward
                    : priority == TaskPriority.medium
                    ? Icons.remove
                    : Icons.arrow_downward,
                color: _priority == priority ? color : Colors.white,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: _priority == priority ? color : Colors.white,
                  fontWeight:
                      _priority == priority
                          ? FontWeight.bold
                          : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dueDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: NeonTheme.neonPurple,
              onPrimary: Colors.white,
              surface: Colors.black,
              onSurface: Colors.white,
            ),
            // dialogBackgroundColor: Colors.black,
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          _dueTime.hour,
          _dueTime.minute,
        );

        // Mettre à jour le rappel si nécessaire
        if (_hasReminder && _reminderTime != null) {
          final difference = _dueDate.difference(_reminderTime!);
          _reminderTime = _dueDate.subtract(difference);
        }
      });
    }
  }

  Future<void> _selectDueTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _dueTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: NeonTheme.neonPurple,
              onPrimary: Colors.white,
              surface: Colors.black,
              onSurface: Colors.white,
            ),
            // dialogBackgroundColor: Colors.black,
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _dueTime) {
      setState(() {
        _dueTime = picked;
        _dueDate = DateTime(
          _dueDate.year,
          _dueDate.month,
          _dueDate.day,
          _dueTime.hour,
          _dueTime.minute,
        );

        // Mettre à jour le rappel si nécessaire
        if (_hasReminder && _reminderTime != null) {
          final difference = _dueDate.difference(_reminderTime!);
          _reminderTime = _dueDate.subtract(difference);
        }
      });
    }
  }

  Future<void> _createTask() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final taskService = ref.read(taskServiceProvider);
        final tasksNotifier = ref.read(tasksProvider.notifier);

        // Extraire les tags
        final tags =
            _tagsController.text.isEmpty
                ? <String>[]
                : _tagsController.text
                    .split(',')
                    .map((tag) => tag.trim())
                    .toList();

        // Trouver le nom de l'assigné
        String? assignedToName;
        if (_assignedToId != null) {
          final teamMembers = ref.read(teamMembersProvider);
          try {
            final assignedMember = teamMembers.firstWhere(
              (member) => member.id == _assignedToId,
            );
            assignedToName = assignedMember.name;
          } catch (e) {
            // Si le membre n'est pas trouvé, ne pas assigner
            _assignedToId = null;
            assignedToName = null;
          }
        }

        if (_isRecurring) {
          // Créer des tâches récurrentes
          final tasks = await taskService.createRecurringTasks(
            title: _titleController.text,
            description: _descriptionController.text,
            startDate: _dueDate,
            priority: _priority,
            recurrencePattern: _recurrencePattern,
            occurrences: _occurrences,
            tags: tags,
            assignedToId: _assignedToId,
            assignedToName: assignedToName,
            hasReminder: _hasReminder,
            reminderHoursBefore:
                _hasReminder && _reminderTime != null
                    ? _dueDate.difference(_reminderTime!).inHours
                    : 24,
          );

          // Ajouter les tâches
          for (final task in tasks) {
            if (widget.parentTaskId != null) {
              tasksNotifier.addSubtask(widget.parentTaskId!, task);
            } else {
              tasksNotifier.addTask(task);
            }
          }
        } else {
          // Créer une tâche simple
          final task = await taskService.createTask(
            title: _titleController.text,
            description: _descriptionController.text,
            dueDate: _dueDate,
            priority: _priority,
            tags: tags,
            assignedToId: _assignedToId,
            assignedToName: assignedToName,
            hasReminder: _hasReminder,
            reminderTime: _reminderTime,
            isRecurring: false,
            parentTaskId: widget.parentTaskId,
          );

          // Ajouter la tâche
          if (widget.parentTaskId != null) {
            tasksNotifier.addSubtask(widget.parentTaskId!, task);
          } else {
            tasksNotifier.addTask(task);
          }
        }

        if (mounted) {
          context.pop();
        }
      } catch (e) {
        debugPrint('Erreur lors de la création de la tâche: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}

