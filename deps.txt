Dart SDK 3.7.2
Flutter SDK 3.29.2
ncrm 1.0.0+1
├── crypto 3.0.6
│   └── typed_data 1.4.0
│       └── collection...
├── csv 5.1.1
├── cupertino_icons 1.0.8
├── file_picker 10.1.9
│   ├── cross_file 0.3.4+2
│   │   ├── meta...
│   │   └── web...
│   ├── ffi 2.1.4
│   ├── flutter...
│   ├── flutter_plugin_android_lifecycle 2.0.28
│   │   └── flutter...
│   ├── flutter_web_plugins 0.0.0
│   │   ├── characters...
│   │   ├── collection...
│   │   ├── flutter...
│   │   ├── material_color_utilities...
│   │   ├── meta...
│   │   └── vector_math...
│   ├── path...
│   ├── plugin_platform_interface 2.1.8
│   │   └── meta...
│   ├── web 1.1.1
│   └── win32 5.13.0
│       └── ffi...
├── fl_chart 1.0.0
│   ├── equatable 2.0.7
│   │   ├── collection...
│   │   └── meta...
│   ├── flutter...
│   └── vector_math...
├── flutter 0.0.0
│   ├── characters 1.4.0
│   ├── collection 1.19.1
│   ├── material_color_utilities 0.11.1
│   │   └── collection...
│   ├── meta 1.16.0
│   ├── sky_engine 0.0.0
│   └── vector_math 2.1.4
├── flutter_contacts 1.1.9+2
│   └── flutter...
├── flutter_dotenv 5.2.1
│   └── flutter...
├── flutter_launcher_icons 0.13.1
│   ├── args 2.7.0
│   ├── checked_yaml 2.0.3
│   │   ├── json_annotation...
│   │   ├── source_span...
│   │   └── yaml...
│   ├── cli_util 0.4.2
│   │   ├── meta...
│   │   └── path...
│   ├── image...
│   ├── json_annotation...
│   ├── path...
│   └── yaml 3.1.3
│       ├── collection...
│       ├── source_span...
│       └── string_scanner...
├── flutter_lints 5.0.0
│   └── lints 5.1.1
├── flutter_local_notifications 16.3.3
│   ├── clock...
│   ├── flutter...
│   ├── flutter_local_notifications_linux 4.0.1
│   │   ├── dbus 0.7.11
│   │   │   ├── args...
│   │   │   ├── ffi...
│   │   │   ├── meta...
│   │   │   └── xml...
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── flutter_local_notifications_platform_interface...
│   │   ├── path...
│   │   └── xdg_directories...
│   ├── flutter_local_notifications_platform_interface 7.2.0
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   └── timezone...
├── flutter_pdfview 1.4.0+1
│   └── flutter...
├── flutter_riverpod 2.6.1
│   ├── collection...
│   ├── flutter...
│   ├── meta...
│   ├── riverpod 2.6.1
│   │   ├── collection...
│   │   ├── meta...
│   │   ├── stack_trace...
│   │   └── state_notifier...
│   └── state_notifier 1.0.0
│       └── meta...
├── flutter_test 0.0.0
│   ├── async...
│   ├── boolean_selector 2.1.2
│   │   ├── source_span...
│   │   └── string_scanner...
│   ├── characters...
│   ├── clock...
│   ├── collection...
│   ├── fake_async 1.3.2
│   │   ├── clock...
│   │   └── collection...
│   ├── flutter...
│   ├── leak_tracker 10.0.8
│   │   ├── clock...
│   │   ├── collection...
│   │   ├── meta...
│   │   ├── path...
│   │   └── vm_service...
│   ├── leak_tracker_flutter_testing 3.0.9
│   │   ├── flutter...
│   │   ├── leak_tracker...
│   │   ├── leak_tracker_testing...
│   │   ├── matcher...
│   │   └── meta...
│   ├── leak_tracker_testing 3.0.1
│   │   ├── leak_tracker...
│   │   ├── matcher...
│   │   └── meta...
│   ├── matcher 0.12.17
│   │   ├── async...
│   │   ├── meta...
│   │   ├── stack_trace...
│   │   ├── term_glyph...
│   │   └── test_api...
│   ├── material_color_utilities...
│   ├── meta...
│   ├── path...
│   ├── source_span 1.10.1
│   │   ├── collection...
│   │   ├── path...
│   │   └── term_glyph...
│   ├── stack_trace...
│   ├── stream_channel...
│   ├── string_scanner 1.4.1
│   │   └── source_span...
│   ├── term_glyph 1.2.2
│   ├── test_api 0.7.4
│   │   ├── async...
│   │   ├── boolean_selector...
│   │   ├── collection...
│   │   ├── meta...
│   │   ├── source_span...
│   │   ├── stack_trace...
│   │   ├── stream_channel...
│   │   ├── string_scanner...
│   │   └── term_glyph...
│   ├── vector_math...
│   └── vm_service 14.3.1
├── font_awesome_flutter 10.8.0
│   └── flutter...
├── go_router 15.1.2
│   ├── collection...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── logging 1.3.0
│   └── meta...
├── google_generative_ai 0.3.3
│   └── http...
├── http 1.4.0
│   ├── async 2.12.0
│   │   ├── collection...
│   │   └── meta...
│   ├── http_parser 4.1.2
│   │   ├── collection...
│   │   ├── source_span...
│   │   ├── string_scanner...
│   │   └── typed_data...
│   ├── meta...
│   └── web...
├── image_picker 1.1.2
│   ├── flutter...
│   ├── image_picker_android 0.8.12+23
│   │   ├── flutter...
│   │   ├── flutter_plugin_android_lifecycle...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_for_web 3.0.6
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── image_picker_platform_interface...
│   │   ├── mime...
│   │   └── web...
│   ├── image_picker_ios 0.8.12+2
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_linux 0.2.1+2
│   │   ├── file_selector_linux 0.9.3+2
│   │   │   ├── cross_file...
│   │   │   ├── file_selector_platform_interface...
│   │   │   └── flutter...
│   │   ├── file_selector_platform_interface 2.6.2
│   │   │   ├── cross_file...
│   │   │   ├── flutter...
│   │   │   ├── http...
│   │   │   └── plugin_platform_interface...
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_macos 0.2.1+2
│   │   ├── file_selector_macos 0.9.4+2
│   │   │   ├── cross_file...
│   │   │   ├── file_selector_platform_interface...
│   │   │   └── flutter...
│   │   ├── file_selector_platform_interface...
│   │   ├── flutter...
│   │   └── image_picker_platform_interface...
│   ├── image_picker_platform_interface 2.10.1
│   │   ├── cross_file...
│   │   ├── flutter...
│   │   ├── http...
│   │   └── plugin_platform_interface...
│   └── image_picker_windows 0.2.1+1
│       ├── file_selector_platform_interface...
│       ├── file_selector_windows 0.9.3+4
│       │   ├── cross_file...
│       │   ├── file_selector_platform_interface...
│       │   └── flutter...
│       ├── flutter...
│       └── image_picker_platform_interface...
├── intl 0.20.2
│   ├── clock 1.1.2
│   ├── meta...
│   └── path...
├── package_info_plus 8.3.0
│   ├── clock...
│   ├── ffi...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── http...
│   ├── meta...
│   ├── package_info_plus_platform_interface 3.2.0
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   ├── path...
│   ├── web...
│   └── win32...
├── path 1.9.1
├── path_provider 2.1.5
│   ├── flutter...
│   ├── path_provider_android 2.2.17
│   │   ├── flutter...
│   │   └── path_provider_platform_interface...
│   ├── path_provider_foundation 2.4.1
│   │   ├── flutter...
│   │   └── path_provider_platform_interface...
│   ├── path_provider_linux 2.2.1
│   │   ├── ffi...
│   │   ├── flutter...
│   │   ├── path...
│   │   ├── path_provider_platform_interface...
│   │   └── xdg_directories 1.1.0
│   │       ├── meta...
│   │       └── path...
│   ├── path_provider_platform_interface 2.1.2
│   │   ├── flutter...
│   │   ├── platform 3.1.6
│   │   └── plugin_platform_interface...
│   └── path_provider_windows 2.3.0
│       ├── ffi...
│       ├── flutter...
│       ├── path...
│       └── path_provider_platform_interface...
├── pdf 3.11.3
│   ├── archive 4.0.7
│   │   ├── crypto...
│   │   ├── path...
│   │   └── posix 6.0.2
│   │       ├── ffi...
│   │       ├── meta...
│   │       └── path...
│   ├── barcode 2.2.9
│   │   ├── meta...
│   │   └── qr 3.0.2
│   │       └── meta...
│   ├── bidi 2.0.13
│   ├── crypto...
│   ├── image 4.5.4
│   │   ├── archive...
│   │   ├── meta...
│   │   └── xml...
│   ├── meta...
│   ├── path_parsing 1.1.0
│   │   ├── meta...
│   │   └── vector_math...
│   ├── vector_math...
│   └── xml 6.5.0
│       ├── collection...
│       ├── meta...
│       └── petitparser 6.1.0
│           ├── collection...
│           └── meta...
├── permission_handler 11.4.0
│   ├── flutter...
│   ├── meta...
│   ├── permission_handler_android 12.1.0
│   │   ├── flutter...
│   │   └── permission_handler_platform_interface...
│   ├── permission_handler_apple 9.4.7
│   │   ├── flutter...
│   │   └── permission_handler_platform_interface...
│   ├── permission_handler_html 0.1.3+5
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── permission_handler_platform_interface...
│   │   └── web...
│   ├── permission_handler_platform_interface 4.3.0
│   │   ├── flutter...
│   │   ├── meta...
│   │   └── plugin_platform_interface...
│   └── permission_handler_windows 0.2.1
│       ├── flutter...
│       └── permission_handler_platform_interface...
├── pocketbase 0.22.0
│   ├── http...
│   └── json_annotation 4.9.0
│       └── meta...
├── printing 5.14.2
│   ├── ffi...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── http...
│   ├── image...
│   ├── meta...
│   ├── pdf...
│   ├── pdf_widget_wrapper 1.0.4
│   │   ├── flutter...
│   │   └── pdf...
│   ├── plugin_platform_interface...
│   └── web...
├── share_plus 7.2.2
│   ├── cross_file...
│   ├── ffi...
│   ├── file 7.0.1
│   │   ├── meta...
│   │   └── path...
│   ├── flutter...
│   ├── flutter_web_plugins...
│   ├── meta...
│   ├── mime 1.0.6
│   ├── share_plus_platform_interface 3.4.0
│   │   ├── cross_file...
│   │   ├── flutter...
│   │   ├── meta...
│   │   ├── mime...
│   │   ├── path_provider...
│   │   ├── plugin_platform_interface...
│   │   └── uuid...
│   ├── url_launcher_linux 3.2.1
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_platform_interface 2.3.2
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── url_launcher_web 2.4.1
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── url_launcher_platform_interface...
│   │   └── web...
│   ├── url_launcher_windows 3.1.4
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   └── win32...
├── shared_preferences 2.5.3
│   ├── flutter...
│   ├── shared_preferences_android 2.4.10
│   │   ├── flutter...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_foundation 2.5.4
│   │   ├── flutter...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_linux 2.4.1
│   │   ├── file...
│   │   ├── flutter...
│   │   ├── path...
│   │   ├── path_provider_linux...
│   │   ├── path_provider_platform_interface...
│   │   └── shared_preferences_platform_interface...
│   ├── shared_preferences_platform_interface 2.4.1
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   ├── shared_preferences_web 2.4.3
│   │   ├── flutter...
│   │   ├── flutter_web_plugins...
│   │   ├── shared_preferences_platform_interface...
│   │   └── web...
│   └── shared_preferences_windows 2.4.1
│       ├── file...
│       ├── flutter...
│       ├── path...
│       ├── path_provider_platform_interface...
│       ├── path_provider_windows...
│       └── shared_preferences_platform_interface...
├── shelf 1.4.2
│   ├── async...
│   ├── collection...
│   ├── http_parser...
│   ├── path...
│   ├── stack_trace 1.12.1
│   │   └── path...
│   └── stream_channel 2.1.4
│       └── async...
├── shelf_router 1.1.4
│   ├── http_methods 1.1.1
│   ├── meta...
│   └── shelf...
├── supabase_flutter 2.9.0
│   ├── app_links 6.4.0
│   │   ├── app_links_linux 1.0.3
│   │   │   ├── app_links_platform_interface...
│   │   │   ├── flutter...
│   │   │   └── gtk 2.1.0
│   │   │       ├── ffi...
│   │   │       ├── flutter...
│   │   │       └── meta...
│   │   ├── app_links_platform_interface 2.0.2
│   │   │   ├── flutter...
│   │   │   └── plugin_platform_interface...
│   │   ├── app_links_web 1.0.4
│   │   │   ├── app_links_platform_interface...
│   │   │   ├── flutter...
│   │   │   ├── flutter_web_plugins...
│   │   │   └── web...
│   │   └── flutter...
│   ├── async...
│   ├── crypto...
│   ├── flutter...
│   ├── http...
│   ├── logging...
│   ├── meta...
│   ├── path_provider...
│   ├── shared_preferences...
│   ├── supabase 2.7.0
│   │   ├── functions_client 2.4.2
│   │   │   ├── http...
│   │   │   ├── logging...
│   │   │   └── yet_another_json_isolate...
│   │   ├── gotrue 2.12.0
│   │   │   ├── collection...
│   │   │   ├── crypto...
│   │   │   ├── http...
│   │   │   ├── jwt_decode 0.3.1
│   │   │   ├── logging...
│   │   │   ├── meta...
│   │   │   ├── retry 3.1.2
│   │   │   ├── rxdart...
│   │   │   └── web...
│   │   ├── http...
│   │   ├── logging...
│   │   ├── postgrest 2.4.2
│   │   │   ├── http...
│   │   │   ├── logging...
│   │   │   ├── meta...
│   │   │   └── yet_another_json_isolate...
│   │   ├── realtime_client 2.5.0
│   │   │   ├── collection...
│   │   │   ├── http...
│   │   │   ├── logging...
│   │   │   ├── meta...
│   │   │   └── web_socket_channel 3.0.3
│   │   │       ├── async...
│   │   │       ├── crypto...
│   │   │       ├── stream_channel...
│   │   │       ├── web...
│   │   │       └── web_socket 1.0.1
│   │   │           └── web...
│   │   ├── rxdart 0.28.0
│   │   ├── storage_client 2.4.0
│   │   │   ├── http...
│   │   │   ├── http_parser...
│   │   │   ├── logging...
│   │   │   ├── meta...
│   │   │   ├── mime...
│   │   │   └── retry...
│   │   └── yet_another_json_isolate 2.1.0
│   │       └── async...
│   ├── url_launcher...
│   └── web...
├── table_calendar 3.2.0
│   ├── flutter...
│   ├── intl...
│   └── simple_gesture_detector 0.2.1
│       └── flutter...
├── timezone 0.9.4
│   └── path...
├── url_launcher 6.3.1
│   ├── flutter...
│   ├── url_launcher_android 6.3.16
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_ios 6.3.3
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_linux...
│   ├── url_launcher_macos 3.2.2
│   │   ├── flutter...
│   │   └── url_launcher_platform_interface...
│   ├── url_launcher_platform_interface...
│   ├── url_launcher_web...
│   └── url_launcher_windows...
├── uuid 4.5.1
│   ├── crypto...
│   ├── fixnum 1.1.1
│   ├── meta...
│   └── sprintf 7.0.0
├── video_player 2.9.5
│   ├── flutter...
│   ├── html 0.15.6
│   │   ├── csslib 1.0.2
│   │   │   └── source_span...
│   │   └── source_span...
│   ├── video_player_android 2.8.3
│   │   ├── flutter...
│   │   └── video_player_platform_interface...
│   ├── video_player_avfoundation 2.7.1
│   │   ├── flutter...
│   │   └── video_player_platform_interface...
│   ├── video_player_platform_interface 6.3.0
│   │   ├── flutter...
│   │   └── plugin_platform_interface...
│   └── video_player_web 2.3.5
│       ├── flutter...
│       ├── flutter_web_plugins...
│       ├── video_player_platform_interface...
│       └── web...
└── youtube_player_flutter 9.1.1
    ├── flutter...
    └── flutter_inappwebview 6.1.5
        ├── flutter...
        ├── flutter_inappwebview_android 1.1.3
        │   ├── flutter...
        │   └── flutter_inappwebview_platform_interface...
        ├── flutter_inappwebview_ios 1.1.2
        │   ├── flutter...
        │   └── flutter_inappwebview_platform_interface...
        ├── flutter_inappwebview_macos 1.1.2
        │   ├── flutter...
        │   └── flutter_inappwebview_platform_interface...
        ├── flutter_inappwebview_platform_interface 1.3.0+1
        │   ├── flutter...
        │   ├── flutter_inappwebview_internal_annotations 1.2.0
        │   └── plugin_platform_interface...
        ├── flutter_inappwebview_web 1.1.2
        │   ├── flutter...
        │   ├── flutter_inappwebview_platform_interface...
        │   ├── flutter_web_plugins...
        │   └── web...
        └── flutter_inappwebview_windows 0.6.0
            ├── flutter...
            └── flutter_inappwebview_platform_interface...
