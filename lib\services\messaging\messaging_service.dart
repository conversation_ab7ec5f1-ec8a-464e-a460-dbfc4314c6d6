import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/contact.dart';
import '../../models/messaging/channel_config.dart';
import '../../models/messaging/conversation.dart';
import '../../models/messaging/telegram_config.dart';
import '../../models/ai/ai_message.dart';
import '../../constants/telegram_constants.dart';
import '../ai/ai_service.dart';
import '../customer/customer_service.dart';
import '../notification_service.dart';
import '../supabase_service.dart';
import 'whatsapp_service.dart';
import 'facebook_service.dart';
import 'sms_service.dart';
import 'email_service.dart';
import 'telegram_service.dart';

// Provider pour le service de messagerie
final messagingServiceProvider = Provider<MessagingService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  return MessagingService(aiService);
});

// Provider pour les messages
final messagesProvider = StateNotifierProvider<MessagesNotifier, List<Message>>(
  (ref) {
    return MessagesNotifier();
  },
);

// Provider pour les contacts
final contactsProvider = StateNotifierProvider<ContactsNotifier, List<Contact>>(
  (ref) {
    return ContactsNotifier();
  },
);

// Provider pour le contact sélectionné
final selectedContactProvider = StateProvider<Contact?>((ref) => null);

// Provider pour le canal sélectionné
final selectedChannelProvider = StateProvider<MessageChannel?>((ref) => null);

// Provider pour la plateforme de messagerie sélectionnée
final selectedPlatformProvider = StateProvider<String?>((ref) => null);

// Provider pour les messages filtrés par contact et canal
final filteredMessagesProvider = Provider.family<List<Message>, FilterParams>((
  ref,
  params,
) {
  final messages = ref.watch(messagesProvider);

  return messages.where((message) {
    bool matchesContact =
        params.contactId == null || message.contactId == params.contactId;
    bool matchesChannel =
        params.channel == null || message.channel == params.channel;
    return matchesContact && matchesChannel;
  }).toList();
});

// Provider pour le nombre de messages non lus
final unreadCountProvider = Provider<int>((ref) {
  final messages = ref.watch(messagesProvider);
  return messages
      .where(
        (message) => !message.isUser && message.status != MessageStatus.read,
      )
      .length;
});

// Provider pour le nombre de messages non lus par contact
final unreadCountByContactProvider = Provider.family<int, String>((
  ref,
  contactId,
) {
  final messages = ref.watch(messagesProvider);
  return messages
      .where(
        (message) =>
            message.contactId == contactId &&
            !message.isUser &&
            message.status != MessageStatus.read,
      )
      .length;
});

// Provider pour les configurations des canaux
final channelConfigsProvider =
    StateNotifierProvider<ChannelConfigsNotifier, List<ChannelConfig>>((ref) {
      return ChannelConfigsNotifier();
    });

// Classe pour les paramètres de filtrage
class FilterParams {
  final String? contactId;
  final MessageChannel? channel;

  FilterParams({this.contactId, this.channel});
}

// Notifier pour les messages
class MessagesNotifier extends StateNotifier<List<Message>> {
  MessagesNotifier() : super([]);

  Future<void> loadMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList('messages');

      if (messagesJson != null) {
        state = messagesJson.map((json) => Message.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des messages: $e');
    }
  }

  Future<void> saveMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = state.map((message) => message.toJson()).toList();
      await prefs.setStringList('messages', messagesJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des messages: $e');
    }
  }

  void addMessage(Message message) {
    state = [message, ...state];
    saveMessages();
  }

  void updateMessageStatus(String messageId, MessageStatus status) {
    state =
        state.map((message) {
          if (message.id == messageId) {
            return message.copyWith(status: status);
          }
          return message;
        }).toList();
    saveMessages();
  }

  void markAsRead(String contactId, [MessageChannel? channel]) {
    state =
        state.map((message) {
          if (message.contactId == contactId &&
              !message.isUser &&
              message.status != MessageStatus.read &&
              (channel == null || message.channel == channel)) {
            return message.copyWith(status: MessageStatus.read);
          }
          return message;
        }).toList();
    saveMessages();
  }

  void deleteMessage(String messageId) {
    state = state.where((message) => message.id != messageId).toList();
    saveMessages();
  }
}

// Notifier pour les contacts
class ContactsNotifier extends StateNotifier<List<Contact>> {
  ContactsNotifier() : super([]);

  Future<void> loadContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactsJson = prefs.getStringList('contacts');

      if (contactsJson != null) {
        state = contactsJson.map((json) => Contact.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des contacts: $e');
    }
  }

  Future<void> saveContacts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final contactsJson = state.map((contact) => contact.toJson()).toList();
      await prefs.setStringList('contacts', contactsJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des contacts: $e');
    }
  }

  void addContact(Contact contact) {
    state = [contact, ...state];
    saveContacts();
  }

  void updateContact(Contact contact) {
    state =
        state.map((c) {
          if (c.id == contact.id) {
            return contact;
          }
          return c;
        }).toList();
    saveContacts();
  }

  void updateUnreadCount(String contactId, int count) {
    state =
        state.map((contact) {
          if (contact.id == contactId) {
            return contact.copyWith(unreadCount: count);
          }
          return contact;
        }).toList();
    saveContacts();
  }

  void deleteContact(String contactId) {
    state = state.where((contact) => contact.id != contactId).toList();
    saveContacts();
  }
}

// Notifier pour les configurations des canaux
class ChannelConfigsNotifier extends StateNotifier<List<ChannelConfig>> {
  ChannelConfigsNotifier() : super([]);

  Future<void> loadChannelConfigs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configsJson = prefs.getStringList('channelConfigs');

      if (configsJson != null) {
        state =
            configsJson.map((json) => ChannelConfig.fromJson(json)).toList();
      } else {
        // Initialiser avec des configurations par défaut
        state = [
          ChannelConfig(channel: MessageChannel.whatsapp, isEnabled: false),
          ChannelConfig(channel: MessageChannel.facebook, isEnabled: false),
          ChannelConfig(channel: MessageChannel.sms, isEnabled: false),
          ChannelConfig(channel: MessageChannel.email, isEnabled: false),
          ChannelConfig(
            channel: MessageChannel.telegram,
            isEnabled: true,
            apiKey: defaultTelegramToken,
          ),
          ChannelConfig(channel: MessageChannel.app, isEnabled: true),
        ];
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des configurations: $e');
    }
  }

  Future<void> saveChannelConfigs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configsJson = state.map((config) => config.toJson()).toList();
      await prefs.setStringList('channelConfigs', configsJson);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des configurations: $e');
    }
  }

  void updateChannelConfig(ChannelConfig config) {
    state =
        state.map((c) {
          if (c.channel == config.channel) {
            return config;
          }
          return c;
        }).toList();
    saveChannelConfigs();
  }
}

// Service principal de messagerie
class MessagingService {
  final AIService _aiService;
  final WhatsAppService _whatsAppService;
  final FacebookService _facebookService;
  final SMSService _smsService;
  final EmailService _emailService;
  TelegramService? _telegramService;

  // Listes en mémoire pour les conversations
  final List<Map<String, dynamic>> _conversations = [];

  MessagingService(this._aiService)
    : _whatsAppService = WhatsAppService(supabaseService: SupabaseService()),
      _facebookService = FacebookService(),
      _smsService = SMSService(CustomerService.empty(), NotificationService()),
      _emailService = EmailService();

  // Méthodes pour la compatibilité avec les nouveaux services

  // Récupérer une conversation par son ID
  Future<Conversation?> getConversationById(String id) async {
    try {
      final conversationMap = _conversations.firstWhere(
        (conversation) => conversation['id'] == id,
      );
      return Conversation.fromJson(conversationMap);
    } catch (e) {
      return null;
    }
  }

  // Récupérer toutes les conversations
  Future<List<Conversation>> getConversations() async {
    return _conversations.map((c) => Conversation.fromJson(c)).toList();
  }

  // Récupérer les conversations d'un participant
  Future<List<Conversation>> getConversationsByParticipantId(
    String participantId,
  ) async {
    return _conversations
        .where(
          (conversation) => conversation['participant_id'] == participantId,
        )
        .map((c) => Conversation.fromJson(c))
        .toList();
  }

  // Mettre à jour une conversation
  Future<void> updateConversation(Conversation conversation) async {
    final conversationMap = conversation.toJson();
    final index = _conversations.indexWhere(
      (c) => c['id'] == conversationMap['id'],
    );
    if (index == -1) {
      _conversations.add(conversationMap);
    } else {
      _conversations[index] = conversationMap;
    }
  }

  // Récupérer les messages d'une conversation
  Future<List<Message>> getMessagesByConversationId(
    String conversationId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList('messages') ?? [];

      final messages =
          messagesJson
              .map((json) => Message.fromJson(json))
              .where((message) => message.conversationId == conversationId)
              .toList();

      return messages;
    } catch (e) {
      debugPrint('Erreur lors de la récupération des messages: $e');
      return [];
    }
  }

  // Récupérer tous les messages (pour la synchronisation)
  Future<List<Message>> getMessages() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList('messages') ?? [];
      return messagesJson.map((json) => Message.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des messages: $e');
      return [];
    }
  }

  // Ajouter un message (pour la synchronisation)
  Future<void> addMessage(Message message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final messagesJson = prefs.getStringList('messages') ?? [];
      messagesJson.add(message.toJson());
      await prefs.setStringList('messages', messagesJson);
    } catch (e) {
      debugPrint('Erreur lors de l\'ajout du message: $e');
    }
  }

  // Initialiser les services
  Future<void> initialize(List<ChannelConfig> configs) async {
    for (var config in configs) {
      switch (config.channel) {
        case MessageChannel.whatsapp:
          if (config.isEnabled) {
            await _whatsAppService.initialize(config);
          }
          break;
        case MessageChannel.facebook:
          if (config.isEnabled) {
            await _facebookService.initialize(config);
          }
          break;
        case MessageChannel.sms:
          if (config.isEnabled) {
            await _smsService.initialize(config);
          }
          break;
        case MessageChannel.email:
          if (config.isEnabled) {
            await _emailService.initialize(config);
          }
          break;
        case MessageChannel.telegram:
          if (config.isEnabled) {
            // Initialiser le service Telegram seulement s'il n'existe pas déjà
            if (_telegramService == null) {
              final apiToken = config.apiKey?.isNotEmpty == true 
                  ? config.apiKey! 
                  : defaultTelegramToken;
              _telegramService = TelegramService(
                TelegramConfig(
                  apiToken: apiToken,
                  lastUpdateId: 0,
                  enabled: true,
                ),
              );
            }
          }
          break;
        case MessageChannel.instagram:
          // À implémenter plus tard
          break;
        case MessageChannel.internal:
          // Pas besoin d'initialisation
          break;
        case MessageChannel.app:
          // Pas besoin d'initialisation
          break;
      }
    }
  }

  // Envoyer un message
  Future<Message> sendMessage({
    required String contactId,
    required String content,
    required MessageChannel channel,
    String? mediaUrl,
    MessageType? mediaType,
    String? mediaName,
    int? mediaSize,
    int? replyToId,
    bool isAIGenerated =
        false, // Ajout du paramètre pour identifier les messages IA
  }) async {
    final message = Message(
      id: const Uuid().v4(),
      contactId: contactId,
      content: content,
      isUser: !isAIGenerated, // Si généré par l'IA, isUser est false
      timestamp: DateTime.now(),
      mediaUrl: mediaUrl,
      mediaType: mediaType,
      mediaName: mediaName,
      mediaSize: mediaSize,
      channel: channel,
      status: MessageStatus.sending, // Statut initial lors de la création
      isAIGenerated: isAIGenerated, // Utiliser le paramètre
      replyToId: replyToId,
    );

    try {
      switch (channel) {
        case MessageChannel.whatsapp:
          await _whatsAppService.sendMessage(
            to: message.contactId,
            content: message.content,
          );
          break;
        case MessageChannel.facebook:
          await _facebookService.sendMessage(message);
          break;
        case MessageChannel.sms:
          await _smsService.sendMessage(message);
          break;
        case MessageChannel.email:
          await _emailService.sendMessage(message);
          break;
        case MessageChannel.telegram:
          final success = _telegramService != null 
          ? await _telegramService!.sendMessageObject(message)
          : false;
          if (!success) {
            return message.copyWith(status: MessageStatus.failed);
          }
          break;
        case MessageChannel.app:
          // Pas besoin d'envoyer à un service externe
          break;
        case MessageChannel.instagram:
          // À implémenter plus tard
          break;
        case MessageChannel.internal:
          // Pas besoin d'envoyer à un service externe
          break;
      }
      // Si l'envoi au service externe n'a pas échoué (ou pas d'envoi externe nécessaire),
      // retourner le message avec le statut actuel (qui pourrait être mis à jour par le service spécifique)
      // Pour simplifier, on retourne le message tel quel, le statut sera mis à jour par des listeners ou callbacks.
      // Ou, si l'envoi est synchrone et réussi, on pourrait mettre à MessageStatus.sent ici.
      // Pour l'instant, on le laisse à .sending, en supposant que le statut final sera géré ailleurs.
      return message; // Retourner le message avec statut .sending ou .failed si applicable
    } catch (e) {
      debugPrint(
        'Erreur lors de l'
        'envoi du message: $e',
      );
      return message.copyWith(status: MessageStatus.failed);
    }
  }

  // Générer une réponse avec l'IA
  Future<Message> generateAIResponse({
    required String contactId,
    required String userMessage,
    required MessageChannel channel,
  }) async {
    try {
      final aiMessages = [
        AIMessage(role: AIMessageRole.user, content: userMessage),
      ];

      final response = await _aiService.sendMessage(aiMessages);

      return Message(
        id: const Uuid().v4(),
        contactId: contactId,
        content: response,
        isUser: false,
        timestamp: DateTime.now(),
        channel: channel,
        status: MessageStatus.sent,
        isAIGenerated: true,
      );
    } catch (e) {
      debugPrint('Erreur lors de la génération de la réponse IA: $e');
      return Message(
        id: const Uuid().v4(),
        contactId: contactId,
        content:
            'Désolé, je rencontre des difficultés techniques. Veuillez réessayer plus tard.',
        isUser: false,
        timestamp: DateTime.now(),
        channel: channel,
        status: MessageStatus.failed,
        isAIGenerated: true,
      );
    }
  }
}
