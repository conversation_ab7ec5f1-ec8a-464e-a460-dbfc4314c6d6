import 'dart:convert';

class TeamMember {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final String role;
  final bool isActive;
  final List<String>? permissions;

  TeamMember({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    required this.role,
    required this.isActive,
    this.permissions,
  });

  TeamMember copyWith({
    String? id,
    String? name,
    String? email,
    String? avatarUrl,
    String? role,
    bool? isActive,
    List<String>? permissions,
  }) {
    return TeamMember(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      permissions: permissions ?? this.permissions,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatarUrl': avatarUrl,
      'role': role,
      'isActive': isActive,
      'permissions': permissions,
    };
  }

  factory TeamMember.fromMap(Map<String, dynamic> map) {
    return TeamMember(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      avatarUrl: map['avatarUrl'],
      role: map['role'] ?? '',
      isActive: map['isActive'] ?? true,
      permissions: map['permissions'] != null ? List<String>.from(map['permissions']) : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory TeamMember.fromJson(String source) => TeamMember.fromMap(json.decode(source));

  @override
  String toString() {
    return 'TeamMember(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is TeamMember &&
      other.id == id &&
      other.name == name &&
      other.email == email &&
      other.avatarUrl == avatarUrl &&
      other.role == role &&
      other.isActive == isActive;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      name.hashCode ^
      email.hashCode ^
      avatarUrl.hashCode ^
      role.hashCode ^
      isActive.hashCode;
  }
}
