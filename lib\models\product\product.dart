// Modèle de produit

// Statut du stock
enum StockStatus {
  inStock, // En stock
  lowStock, // Stock bas
  outOfStock, // Rupture de stock
  discontinued, // Discontinué
  preOrder, // Précommande
}

class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final String category;
  final int quantity;
  final StockStatus stockStatus;
  final String? sku;
  final String? barcode;
  final List<String> images;
  final Map<String, dynamic>? attributes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double? costPrice;
  final double? weight;
  final String? dimensions;
  final int popularity;
  final Map<String, dynamic>? metadata;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    required this.quantity,
    required this.stockStatus,
    this.sku,
    this.barcode,
    required this.images,
    this.attributes,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.costPrice,
    this.weight,
    this.dimensions,
    required this.popularity,
    this.metadata,
  });

  // Créer une copie avec des modifications
  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? category,
    int? quantity,
    StockStatus? stockStatus,
    String? sku,
    String? barcode,
    List<String>? images,
    Map<String, dynamic>? attributes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? costPrice,
    double? weight,
    String? dimensions,
    int? popularity,
    Map<String, dynamic>? metadata,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      stockStatus: stockStatus ?? this.stockStatus,
      sku: sku ?? this.sku,
      barcode: barcode ?? this.barcode,
      images: images ?? this.images,
      attributes: attributes ?? this.attributes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      costPrice: costPrice ?? this.costPrice,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      popularity: popularity ?? this.popularity,
      metadata: metadata ?? this.metadata,
    );
  }

  // Conversion depuis JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      category: json['category'] as String,
      quantity: json['quantity'] as int,
      stockStatus: StockStatus.values.firstWhere(
        (e) => e.name == json['stock_status'],
        orElse: () => StockStatus.inStock,
      ),
      sku: json['sku'] as String?,
      barcode: json['barcode'] as String?,
      images: List<String>.from(json['images'] as List),
      attributes: json['attributes'] as Map<String, dynamic>?,
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      costPrice:
          json['cost_price'] != null
              ? (json['cost_price'] as num).toDouble()
              : null,
      weight:
          json['weight'] != null ? (json['weight'] as num).toDouble() : null,
      dimensions: json['dimensions'] as String?,
      popularity: json['popularity'] as int? ?? 0,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Conversion vers JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'quantity': quantity,
      'stock_status': stockStatus.name,
      'sku': sku,
      'barcode': barcode,
      'images': images,
      'attributes': attributes,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'cost_price': costPrice,
      'weight': weight,
      'dimensions': dimensions,
      'popularity': popularity,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'Product{id: $id, name: $name, price: $price, quantity: $quantity}';
  }
}
