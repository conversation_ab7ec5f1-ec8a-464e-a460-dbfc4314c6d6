import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../services/sync_service.dart';

class SyncSettingsScreen extends ConsumerStatefulWidget {
  const SyncSettingsScreen({super.key});

  @override
  ConsumerState<SyncSettingsScreen> createState() => _SyncSettingsScreenState();
}

class _SyncSettingsScreenState extends ConsumerState<SyncSettingsScreen> {
  bool _isSyncing = false;
  bool _autoSync = true;
  String _syncInterval = '15 minutes';
  DateTime _lastSyncTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadSyncSettings();
  }

  void _loadSyncSettings() {
    final syncService = ref.read(syncServiceProvider);
    setState(() {
      _lastSyncTime = syncService.lastSyncTime;
    });
  }

  Future<void> _syncNow() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      final syncService = ref.read(syncServiceProvider);
      await syncService.syncAll();

      setState(() {
        _lastSyncTime = syncService.lastSyncTime;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Synchronisation terminée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la synchronisation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres de synchronisation'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.cyan,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSyncStatusCard(),
            const SizedBox(height: 16),
            _buildSyncSettingsCard(),
            const SizedBox(height: 16),
            _buildSyncDataCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatusCard() {
    final formatter = DateFormat('dd/MM/yyyy HH:mm:ss');
    final lastSyncFormatted =
        _lastSyncTime.year > 2000 ? formatter.format(_lastSyncTime) : 'Jamais';

    return Card(
      color: Colors.black.withAlpha(153),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.cyan.withAlpha(77), width: 1),
      ),
      elevation: 4,
      shadowColor: Colors.cyan.withAlpha(77),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'État de la synchronisation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Dernière synchronisation:',
                  style: TextStyle(color: Colors.white70),
                ),
                Text(
                  lastSyncFormatted,
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSyncing ? null : _syncNow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child:
                    _isSyncing
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                            SizedBox(width: 10),
                            Text('Synchronisation en cours...'),
                          ],
                        )
                        : const Text('Synchroniser maintenant'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncSettingsCard() {
    return Card(
      color: Colors.black.withAlpha(153),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.cyan.withAlpha(77), width: 1),
      ),
      elevation: 4,
      shadowColor: Colors.cyan.withAlpha(77),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Paramètres de synchronisation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text(
                'Synchronisation automatique',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                'Synchroniser automatiquement les données',
                style: TextStyle(color: Colors.white70),
              ),
              value: _autoSync,
              onChanged: (value) {
                setState(() {
                  _autoSync = value;
                });
              },
              activeColor: Colors.cyan,
            ),
            const Divider(color: Colors.white24),
            ListTile(
              title: const Text(
                'Intervalle de synchronisation',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: Text(
                _syncInterval,
                style: const TextStyle(color: Colors.white70),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white70,
              ),
              onTap: () {
                _showIntervalDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncDataCard() {
    return Card(
      color: Colors.black.withAlpha(153),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.cyan.withAlpha(77), width: 1),
      ),
      elevation: 4,
      shadowColor: Colors.cyan.withAlpha(77),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Données à synchroniser',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            CheckboxListTile(
              title: const Text(
                'Clients',
                style: TextStyle(color: Colors.white),
              ),
              value: true,
              onChanged: (value) {},
              activeColor: Colors.cyan,
            ),
            CheckboxListTile(
              title: const Text(
                'Tâches',
                style: TextStyle(color: Colors.white),
              ),
              value: true,
              onChanged: (value) {},
              activeColor: Colors.cyan,
            ),
            CheckboxListTile(
              title: const Text(
                'Produits',
                style: TextStyle(color: Colors.white),
              ),
              value: true,
              onChanged: (value) {},
              activeColor: Colors.cyan,
            ),
            CheckboxListTile(
              title: const Text(
                'Factures',
                style: TextStyle(color: Colors.white),
              ),
              value: true,
              onChanged: (value) {},
              activeColor: Colors.cyan,
            ),
            CheckboxListTile(
              title: const Text(
                'Messages',
                style: TextStyle(color: Colors.white),
              ),
              value: true,
              onChanged: (value) {},
              activeColor: Colors.cyan,
            ),
          ],
        ),
      ),
    );
  }

  void _showIntervalDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.black,
          title: const Text(
            'Intervalle de synchronisation',
            style: TextStyle(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildIntervalOption('5 minutes'),
              _buildIntervalOption('15 minutes'),
              _buildIntervalOption('30 minutes'),
              _buildIntervalOption('1 heure'),
              _buildIntervalOption('4 heures'),
              _buildIntervalOption('12 heures'),
              _buildIntervalOption('24 heures'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Annuler',
                style: TextStyle(color: Colors.cyan),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildIntervalOption(String interval) {
    return ListTile(
      title: Text(interval, style: const TextStyle(color: Colors.white)),
      leading: Radio<String>(
        value: interval,
        groupValue: _syncInterval,
        onChanged: (value) {
          setState(() {
            _syncInterval = value!;
          });
          Navigator.of(context).pop();
        },
        activeColor: Colors.cyan,
      ),
    );
  }
}
