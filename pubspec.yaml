name: ncrm
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  csv: ^5.1.1
  file_picker: ^10.1.0 # Mise à jour vers une version compatible avec Flutter récent

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # PocketBase client
  pocketbase: ^0.22.0

  # State management
  flutter_riverpod: ^2.4.9

  # Utilities
  http: ^1.4.0
  shared_preferences: ^2.2.2
  intl: ^0.20.2
  path_provider: ^2.1.1
  path: ^1.9.0 # Ajout de la dépendance path

  # UI
  font_awesome_flutter: ^10.6.0
  go_router: ^15.1.2

  # Fonctionnalités supplémentaires
  # Ancienne version de file_picker commentée
  flutter_contacts: ^1.1.7+1  # Ajout pour la gestion des contacts
  permission_handler: ^11.3.0
  share_plus: ^7.2.2
  uuid: ^4.5.1
  flutter_dotenv: ^5.2.1
  fl_chart: ^1.0.0
  flutter_pdfview: ^1.4.0+1
  pdf: ^3.11.3
  printing: ^5.12.0
  # flutter_local_notifications: ^19.1.0  # Temporairement désactivé pour Windows
  supabase_flutter: ^2.9.0
  table_calendar: ^3.2.0
  image_picker: ^1.0.7
  crypto: ^3.0.3 # Ajout de la dépendance crypto

  # Serveur webhook
  shelf: ^1.4.1
  shelf_router: ^1.1.4

  # SMS et messagerie
  # flutter_sms: ^2.3.3 # Désactivé temporairement pour la compilation
  # telephony: ^0.2.0 # Désactivé temporairement pour la compilation
  url_launcher: ^6.2.5
  flutter_local_notifications: ^16.3.2
  # notification_permissions: ^0.6.1 # Désactivé temporairement pour la compilation
  timezone: ^0.9.2

  # IA Générative
  google_generative_ai: ^0.3.0

  # Lecteur Vidéo
  # youtube_player_flutter: ^9.0.0 # Désactivé temporairement pour Windows (problème avec flutter_inappwebview)
  video_player: ^2.8.6 # Lecture de vidéos locales et en ligne

  # Gestion des versions
  package_info_plus: ^8.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/logo/logo.png"
  adaptive_icon_background: "#A8A4D2" # Couleur lavande du thème
  adaptive_icon_foreground: "assets/images/logo/logo.png"
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
