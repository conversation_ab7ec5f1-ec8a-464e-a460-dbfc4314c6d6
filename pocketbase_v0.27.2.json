{"collections": [{"name": "contacts", "schema": [{"name": "name", "type": "text", "required": true}, {"name": "email", "type": "email", "required": false}, {"name": "phone", "type": "text", "required": false}, {"name": "company", "type": "text", "required": false}, {"name": "notes", "type": "text", "required": false}, {"name": "avatar", "type": "file", "required": false, "options": {"maxSelect": 1, "maxSize": 5242880, "mimeTypes": ["image/jpeg", "image/png", "image/gif"], "thumbs": ["100x100"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\""}, {"name": "messages", "schema": [{"name": "contact", "type": "relation", "required": true, "options": {"collectionId": "contacts", "cascadeDelete": true, "maxSelect": 1}}, {"name": "content", "type": "text", "required": true}, {"name": "is_user", "type": "bool", "required": true}, {"name": "timestamp", "type": "date", "required": true}, {"name": "media_url", "type": "text", "required": false}, {"name": "media_type", "type": "select", "required": false, "options": {"values": ["image", "video", "audio", "document"]}}, {"name": "channel", "type": "select", "required": false, "options": {"values": ["app", "whatsapp", "facebook", "sms", "email"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\""}, {"name": "tasks", "schema": [{"name": "title", "type": "text", "required": true}, {"name": "description", "type": "text", "required": false}, {"name": "contact", "type": "relation", "required": false, "options": {"collectionId": "contacts", "cascadeDelete": false, "maxSelect": 1}}, {"name": "due_date", "type": "date", "required": true}, {"name": "status", "type": "select", "required": true, "options": {"values": ["pending", "in_progress", "completed"]}}, {"name": "priority", "type": "select", "required": true, "options": {"values": ["low", "medium", "high"]}}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\""}, {"name": "knowledge_base", "schema": [{"name": "title", "type": "text", "required": true}, {"name": "content", "type": "text", "required": true}, {"name": "category", "type": "text", "required": true}, {"name": "tags", "type": "json", "required": false}], "listRule": "@request.auth.id != \"\"", "viewRule": "@request.auth.id != \"\"", "createRule": "@request.auth.id != \"\"", "updateRule": "@request.auth.id != \"\"", "deleteRule": "@request.auth.id != \"\""}]}