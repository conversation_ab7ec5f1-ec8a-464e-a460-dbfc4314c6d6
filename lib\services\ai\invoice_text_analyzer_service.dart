import 'dart:convert';
import 'package:http/http.dart' as http;

class InvoiceTextAnalyzerService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _apiKey = 'your-api-key-here';

  static Future<Map<String, dynamic>> analyzeText(String text) async {
    return await analyzeInvoiceText(text);
  }

  static Future<Map<String, dynamic>> analyzeInvoiceText(String text) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content':
                  'You are an AI assistant that analyzes invoice text and extracts key information.',
            },
            {
              'role': 'user',
              'content':
                  'Analyze this invoice text and extract key information: $text',
            },
          ],
          'max_tokens': 1000,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'analysis': data['choices'][0]['message']['content'],
        };
      } else {
        return {
          'success': false,
          'error': 'API request failed with status: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {'success': false, 'error': 'Error analyzing invoice: $e'};
    }
  }

  static Map<String, dynamic> _extractSpecialConditions(String text) {
    final patterns = [
      RegExp(r'\*Note\*\s*\n?\s*([^\n]+)', caseSensitive: false),
      RegExp(r'\*Message\*\s*:\s*\n\s*([^\n*]+)', caseSensitive: false),
      RegExp(
        r'(?:Message|Note|Conditions?)\s*:\s*([^\n]+)',
        caseSensitive: false,
      ),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        return {'found': true, 'condition': match.group(1)!.trim()};
      }
    }

    return {'found': false, 'condition': null};
  }

  static Future<Map<String, dynamic>> extractInvoiceData(String text) async {
    try {
      // Extract basic information using regex patterns
      final invoiceNumber = _extractInvoiceNumber(text);
      final date = _extractDate(text);
      final amount = _extractAmount(text);
      final specialConditions = _extractSpecialConditions(text);

      return {
        'success': true,
        'data': {
          'invoiceNumber': invoiceNumber,
          'date': date,
          'amount': amount,
          'specialConditions': specialConditions,
        },
      };
    } catch (e) {
      return {'success': false, 'error': 'Error extracting invoice data: $e'};
    }
  }

  static String? _extractInvoiceNumber(String text) {
    final patterns = [
      RegExp(r'Invoice\s*#?\s*:?\s*([A-Z0-9-]+)', caseSensitive: false),
      RegExp(r'Facture\s*#?\s*:?\s*([A-Z0-9-]+)', caseSensitive: false),
      RegExp(r'N°\s*:?\s*([A-Z0-9-]+)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }
    return null;
  }

  static String? _extractDate(String text) {
    final patterns = [
      RegExp(
        r'Date\s*:?\s*(\d{1,2}[/\-.]\d{1,2}[/\-.]\d{2,4})',
        caseSensitive: false,
      ),
      RegExp(r'(\d{1,2}[/\-.]\d{1,2}[/\-.]\d{2,4})'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }
    return null;
  }

  static String? _extractAmount(String text) {
    final patterns = [
      RegExp(
        r'Total\s*:?\s*([€$]?\s*\d+[.,]\d{2}\s*[€$]?)',
        caseSensitive: false,
      ),
      RegExp(
        r'Montant\s*:?\s*([€$]?\s*\d+[.,]\d{2}\s*[€$]?)',
        caseSensitive: false,
      ),
      RegExp(r'([€$]\s*\d+[.,]\d{2})'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        return match.group(1);
      }
    }
    return null;
  }
}
