import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../providers/theme_provider.dart';
import '../../theme/neon_theme.dart';
import '../../theme/tz1_theme.dart';
import '../../theme/tz2_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

class ThemeSettingsScreen extends ConsumerWidget {
  const ThemeSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres du thème'),
        // backgroundColor et foregroundColor sont maintenant gérés par le thème global
      ),
      body: Container(
        decoration: BoxDecoration(gradient: ref.watch(mainGradientProvider)),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Choisissez votre thème',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Personnalisez l\'apparence de l\'application selon vos préférences.',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 24),

              // Thème Unique
              _buildThemeCard(
                context,
                ref,
                AppThemeMode.unique,
                'Thème Unique',
                'Thème sombre inspiré de l\'écran de messages',
                FontAwesomeIcons.palette,
                NeonTheme.primaryAccent,
                currentTheme,
              ),

              const SizedBox(height: 16),

              // Thème TZ-1
              _buildThemeCard(
                context,
                ref,
                AppThemeMode.tz1,
                'Thème TZ-1',
                'Thème clair avec palette de couleurs spécifiques',
                FontAwesomeIcons.solidCircle,
                TZ1Theme.lavenderActive,
                currentTheme,
              ),

              const SizedBox(height: 16),

              // Thème TZ-2
              _buildThemeCard(
                context,
                ref,
                AppThemeMode.tz2,
                'Thème TZ-2',
                'Thème lavande avec dégradés',
                FontAwesomeIcons.solidCircle,
                TZ2Theme.cloudyViolet,
                currentTheme,
              ),

              const SizedBox(height: 16),

              // Thème Creamy Beans
              _buildThemeCard(
                context,
                ref,
                AppThemeMode.creamyBeans,
                'Creamy Beans',
                'Thème chaleureux aux tons de café',
                FontAwesomeIcons.mugSaucer, // Icône appropriée
                AppThemeMode
                    .creamyBeans
                    .primaryColor, // Accès direct à la couleur primaire via l'extension
                currentTheme,
              ),

              const SizedBox(height: 16),

              // Thème Orix Purple Split
              _buildThemeCard(
                context,
                ref,
                AppThemeMode.orixPurpleSplit,
                'Orix Purple Split',
                'Thème moderne et dynamique violet et saumon',
                FontAwesomeIcons.solidCreditCard, // Icône appropriée
                AppThemeMode
                    .orixPurpleSplit
                    .primaryColor, // Accès direct à la couleur primaire via l'extension
                currentTheme,
              ),

              const SizedBox(height: 24),

              // Aperçu du thème actuel
              NeonCard(
                color: currentTheme.primaryColor,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Aperçu du thème actuel',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Exemple de bouton
                      Row(
                        children: [
                          NeonButton(
                            text: 'Bouton principal',
                            icon: FontAwesomeIcons.check,
                            color: currentTheme.primaryColor,
                            onPressed: () {},
                          ),
                          const SizedBox(width: 16),
                          NeonButton(
                            text: 'Bouton secondaire',
                            icon: FontAwesomeIcons.xmark,
                            color: currentTheme.secondaryColor,
                            onPressed: () {},
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Exemple de texte
                      Text(
                        'Titre de niveau 1',
                        style: TextStyle(
                          color:
                              currentTheme.isDark
                                  ? Colors.white
                                  : Colors.black87,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Titre de niveau 2',
                        style: TextStyle(
                          color:
                              currentTheme.isDark
                                  ? Colors.white
                                  : Colors.black87,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Ceci est un exemple de texte pour montrer comment le texte apparaît dans ce thème. Le texte doit être lisible et agréable à lire.',
                        style: TextStyle(
                          color:
                              currentTheme.isDark
                                  ? Colors.white70
                                  : Colors.black54,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Bouton pour accéder au gestionnaire de thèmes
              Center(
                child: NeonButton(
                  text: 'Gestionnaire de thèmes',
                  icon: FontAwesomeIcons.palette,
                  onPressed: () => context.push('/settings/theme_manager'),
                  color: NeonTheme.neonCyan,
                ),
              ),

              const SizedBox(height: 8),
              const Center(
                child: Text(
                  'Importez, exportez et gérez vos thèmes personnalisés',
                  style: TextStyle(color: Colors.white70),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeCard(
    BuildContext context,
    WidgetRef ref,
    AppThemeMode themeMode,
    String title,
    String description,
    IconData icon,
    Color color,
    AppThemeMode currentTheme,
  ) {
    final isSelected = currentTheme == themeMode;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color:
          isSelected
              ? color.withValues(alpha: 51)
              : Colors.black.withValues(
                alpha: 77,
              ), // 0.2 * 255 ≈ 51, 0.3 * 255 ≈ 77
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isSelected
                  ? color
                  : Colors.white.withValues(alpha: 26), // 0.1 * 255 ≈ 26
          width: isSelected ? 2 : 1,
        ),
      ),
      elevation: isSelected ? 8 : 2,
      shadowColor:
          isSelected
              ? color.withValues(alpha: 128)
              : Colors.black.withValues(alpha: 128), // 0.5 * 255 ≈ 128
      child: InkWell(
        onTap: () {
          ref.read(themeProvider.notifier).setTheme(themeMode);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 51), // 0.2 * 255 ≈ 51
                  shape: BoxShape.circle,
                  border: Border.all(color: color),
                  boxShadow:
                      isSelected
                          ? NeonTheme.neonShadow(color, intensity: 0.7)
                          : null,
                ),
                child: Icon(icon, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: isSelected ? color : Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(FontAwesomeIcons.circleCheck, color: color, size: 24),
            ],
          ),
        ),
      ),
    );
  }
}
