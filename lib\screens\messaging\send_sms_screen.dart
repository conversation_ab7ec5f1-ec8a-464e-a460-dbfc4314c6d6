import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/customer/customer.dart';

import '../../services/messaging/sms_service.dart';
import '../../services/customer/customer_service.dart';
import '../../theme/neon_theme.dart';

class SendSmsScreen extends ConsumerStatefulWidget {
  final String? initialPhoneNumber;
  final String? customerId;
  final String? customerName;

  const SendSmsScreen({
    super.key,
    this.initialPhoneNumber,
    this.customerId,
    this.customerName,
  });

  @override
  ConsumerState<SendSmsScreen> createState() => _SendSmsScreenState();
}

class _SendSmsScreenState extends ConsumerState<SendSmsScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isSending = false;
  Customer? _selectedCustomer;
  List<Customer> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialPhoneNumber != null) {
      _phoneController.text = widget.initialPhoneNumber!;
    }
    if (widget.customerId != null && widget.customerName != null) {
      _selectedCustomer = Customer(
        id: widget.customerId!,
        name: widget.customerName!,
        phone: widget.initialPhoneNumber,
        createdAt: DateTime.now(),
        lastContact: DateTime.now(),
        status: CustomerStatus.customer,
        tags: [],
      );
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _searchCustomers(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    final customerService = ref.read(customerServiceProvider);
    final customers = await customerService.getCustomers();

    final results =
        customers.where((customer) {
          final nameMatch = customer.name.toLowerCase().contains(
            query.toLowerCase(),
          );
          final phoneMatch =
              customer.phone != null && customer.phone!.contains(query);
          return nameMatch || phoneMatch;
        }).toList();

    setState(() {
      _searchResults = results;
      _isSearching = false;
    });
  }

  void _selectCustomer(Customer customer) {
    setState(() {
      _selectedCustomer = customer;
      _phoneController.text = customer.phone ?? '';
      _searchResults = [];
    });
    FocusScope.of(context).unfocus();
  }

  Future<void> _sendSms() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSending = true;
    });

    try {
      final smsService = ref.read(smsServiceProvider);
      final success = await smsService.sendSMS(
        phoneNumber: _phoneController.text,
        message: _messageController.text,
        contactId: _selectedCustomer?.id ?? _phoneController.text,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('SMS envoyé avec succès'),
              backgroundColor: Colors.green,
            ),
          );
          _messageController.clear();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Échec de l\'envoi du SMS'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Envoyer un SMS'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Champ de recherche de client
                TextFormField(
                  decoration: InputDecoration(
                    labelText: 'Rechercher un client',
                    prefixIcon: const Icon(Icons.search, color: Colors.white70),
                    suffixIcon:
                        _selectedCustomer != null
                            ? IconButton(
                              icon: const Icon(
                                Icons.clear,
                                color: Colors.white70,
                              ),
                              onPressed: () {
                                setState(() {
                                  _selectedCustomer = null;
                                  _phoneController.clear();
                                  _searchResults = [];
                                });
                              },
                            )
                            : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.cyan),
                    ),
                    labelStyle: const TextStyle(color: Colors.white70),
                  ),
                  style: const TextStyle(color: Colors.white),
                  onChanged: _searchCustomers,
                  enabled: _selectedCustomer == null,
                ),

                // Résultats de recherche
                if (_searchResults.isNotEmpty)
                  Container(
                    height: 200,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(77),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.cyan.withAlpha(77)),
                    ),
                    child: ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final customer = _searchResults[index];
                        return ListTile(
                          title: Text(
                            customer.name,
                            style: const TextStyle(color: Colors.white),
                          ),
                          subtitle: Text(
                            customer.phone ?? 'Pas de numéro',
                            style: const TextStyle(color: Colors.white70),
                          ),
                          onTap: () => _selectCustomer(customer),
                        );
                      },
                    ),
                  ),

                if (_isSearching)
                  const Center(
                    child: CircularProgressIndicator(color: Colors.cyan),
                  ),

                const SizedBox(height: 16),

                // Affichage du client sélectionné
                if (_selectedCustomer != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.black.withAlpha(77),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.cyan.withAlpha(77)),
                    ),
                    child: Row(
                      children: [
                        const CircleAvatar(
                          backgroundColor: Colors.cyan,
                          child: Icon(Icons.person, color: Colors.black),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _selectedCustomer!.name,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (_selectedCustomer!.phone != null)
                                Text(
                                  _selectedCustomer!.phone!,
                                  style: const TextStyle(color: Colors.white70),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                // Champ numéro de téléphone
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: 'Numéro de téléphone',
                    prefixIcon: const Icon(Icons.phone, color: Colors.white70),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.cyan.withAlpha(77)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.cyan),
                    ),
                    labelStyle: const TextStyle(color: Colors.white70),
                  ),
                  style: const TextStyle(color: Colors.white),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez entrer un numéro de téléphone';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Champ message
                Expanded(
                  child: TextFormField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      labelText: 'Message',
                      alignLabelWithHint: true,
                      prefixIcon: const Icon(
                        Icons.message,
                        color: Colors.white70,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.cyan.withAlpha(77),
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.cyan.withAlpha(77),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.cyan),
                      ),
                      labelStyle: const TextStyle(color: Colors.white70),
                    ),
                    style: const TextStyle(color: Colors.white),
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Veuillez entrer un message';
                      }
                      return null;
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // Bouton d'envoi
                ElevatedButton.icon(
                  onPressed: _isSending ? null : _sendSms,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyan,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon:
                      _isSending
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.black,
                              strokeWidth: 2,
                            ),
                          )
                          : const Icon(FontAwesomeIcons.paperPlane),
                  label: Text(_isSending ? 'Envoi en cours...' : 'Envoyer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
