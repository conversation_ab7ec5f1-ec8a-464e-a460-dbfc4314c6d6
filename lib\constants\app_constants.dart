/// Constantes de l'application
class AppConstants {
  // Rôles utilisateur
  static const String userRoleAdmin = 'admin';
  static const String userRoleSupervisor = 'supervisor';
  static const String userRoleAgent = 'agent';

  // Statuts des tâches
  static const String taskStatusTodo = 'todo';
  static const String taskStatusInProgress = 'in_progress';
  static const String taskStatusDone = 'done';
  static const String taskStatusCancelled = 'cancelled';

  // Priorités des tâches
  static const String taskPriorityLow = 'low';
  static const String taskPriorityMedium = 'medium';
  static const String taskPriorityHigh = 'high';
  static const String taskPriorityCritical = 'critical';

  // Statuts des opportunités
  static const String opportunityStatusNew = 'new';
  static const String opportunityStatusQualified = 'qualified';
  static const String opportunityStatusProposal = 'proposal';
  static const String opportunityStatusNegotiation = 'negotiation';
  static const String opportunityStatusWon = 'won';
  static const String opportunityStatusLost = 'lost';

  // Types de contacts
  static const String contactTypeCustomer = 'customer';
  static const String contactTypeProspect = 'prospect';
  static const String contactTypePartner = 'partner';
  static const String contactTypeSupplier = 'supplier';

  // Formats de date
  static const String dateFormatFull = 'dd/MM/yyyy HH:mm';
  static const String dateFormatDay = 'dd/MM/yyyy';
  static const String dateFormatTime = 'HH:mm';
  static const String dateFormatMonth = 'MMMM yyyy';

  // Limites
  static const int maxTasksPerPage = 20;
  static const int maxContactsPerPage = 20;
  static const int maxOpportunitiesPerPage = 20;
  static const int maxMessagesPerPage = 50;
  static const int maxNotificationsPerPage = 20;

  // Délais
  static const int autoSaveDelaySeconds = 5;
  static const int refreshDataIntervalMinutes = 5;
  static const int sessionTimeoutMinutes = 60;

  // Tailles
  static const double maxMobileWidth = 600;
  static const double maxTabletWidth = 1200;
  static const double sidebarWidthExpanded = 240;
  static const double sidebarWidthCollapsed = 80;
  static const double appBarHeight = 60;
  static const double bottomNavBarHeight = 60;

  // Animations
  static const int animationDurationMs = 300;
  static const int pageTransitionDurationMs = 200;
  static const int splashScreenDurationMs = 2000;

  // API
  static const String apiBaseUrl = 'https://api.example.com';
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  static const int retryDelayMs = 1000;

  // Cache
  static const int cacheDurationHours = 24;
  static const int maxCacheSizeMb = 50;

  // Fichiers
  static const int maxUploadSizeMb = 10;
  static const List<String> allowedImageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp'
  ];
  static const List<String> allowedDocumentExtensions = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'ppt',
    'pptx',
    'txt'
  ];

  // Messages d'erreur
  static const String errorNetworkMessage =
      'Erreur de connexion. Veuillez vérifier votre connexion internet.';
  static const String errorServerMessage =
      'Erreur serveur. Veuillez réessayer plus tard.';
  static const String errorAuthMessage =
      'Erreur d\'authentification. Veuillez vous reconnecter.';
  static const String errorPermissionMessage =
      'Vous n\'avez pas les permissions nécessaires pour effectuer cette action.';
  static const String errorUnknownMessage =
      'Une erreur inconnue est survenue. Veuillez réessayer.';
}
