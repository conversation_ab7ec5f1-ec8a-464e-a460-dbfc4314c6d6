import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';
import '../models/user_role.dart';
import 'auth_service.dart';

// Provider pour le service utilisateur
final userServiceProvider = Provider<UserService>((ref) {
  return UserService(ref);
});

// Provider pour la liste des utilisateurs
final usersProvider = FutureProvider<List<User>>((ref) async {
  final userService = ref.watch(userServiceProvider);
  return userService.getUsers();
});

// Provider pour la liste des rôles
final userRolesProvider = FutureProvider<List<UserRole>>((ref) async {
  final userService = ref.watch(userServiceProvider);
  return userService.getRoles();
});

// Provider pour vérifier si l'utilisateur actuel est administrateur
final isAdminProvider = Provider<bool>((ref) {
  final currentUser = ref.watch(currentUserProvider);
  return currentUser?.role == 'admin';
});

// Provider pour vérifier si l'utilisateur actuel a une permission spécifique
final hasPermissionProvider = Provider.family<bool, String>((ref, permission) {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return false;

  final userService = ref.watch(userServiceProvider);
  return userService.hasPermission(currentUser, permission);
});

class UserService {
  final Ref _ref;

  UserService(this._ref);

  // Obtenir la liste des utilisateurs
  Future<List<User>> getUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getStringList('users') ?? [];

      return usersJson.map((json) => User.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des utilisateurs: $e');
      return [];
    }
  }

  // Ajouter un utilisateur
  Future<bool> addUser(User user) async {
    try {
      final users = await getUsers();

      // Vérifier si l'email existe déjà
      if (users.any((u) => u.email == user.email)) {
        return false;
      }

      users.add(user);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'users',
        users.map((user) => user.toJson()).toList(),
      );

      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'ajout de l\'utilisateur: $e');
      return false;
    }
  }

  // Mettre à jour un utilisateur
  Future<bool> updateUser(User user) async {
    try {
      debugPrint('Début de la mise à jour de l\'utilisateur: ${user.id}');
      debugPrint('Nouvel avatar: ${user.avatar}');

      final users = await getUsers();
      debugPrint('Nombre d\'utilisateurs trouvés: ${users.length}');

      final index = users.indexWhere((u) => u.id == user.id);
      debugPrint('Index de l\'utilisateur: $index');

      if (index == -1) {
        debugPrint('Utilisateur non trouvé dans la liste');
        return false;
      }

      // Sauvegarder l'ancien avatar pour comparaison
      final oldAvatar = users[index].avatar;
      debugPrint('Ancien avatar: $oldAvatar');

      users[index] = user;
      debugPrint('Utilisateur mis à jour dans la liste');

      final prefs = await SharedPreferences.getInstance();
      final userJsonList = users.map((u) => u.toJson()).toList();

      // Vérifier que la sérialisation fonctionne correctement
      debugPrint(
        'Vérification de la sérialisation de l\'utilisateur mis à jour',
      );
      final updatedUserJson = userJsonList[index];
      final deserializedUser = User.fromJson(updatedUserJson);
      debugPrint(
        'Avatar après sérialisation/désérialisation: ${deserializedUser.avatar}',
      );

      await prefs.setStringList('users', userJsonList);
      debugPrint('Liste d\'utilisateurs sauvegardée dans SharedPreferences');

      // Mettre à jour l'utilisateur actuel si nécessaire
      final currentUser = _ref.read(currentUserProvider);
      if (currentUser != null && currentUser.id == user.id) {
        debugPrint('Mise à jour de l\'utilisateur actuel');

        // Mettre à jour l'utilisateur dans le service d'authentification
        await _ref.read(authServiceProvider).updateCurrentUser(user);

        // Mettre à jour l'utilisateur dans le provider
        _ref.read(currentUserProvider.notifier).updateUser(user);

        // Vérifier que l'utilisateur actuel a bien été mis à jour
        final updatedCurrentUser = _ref.read(currentUserProvider);
        debugPrint(
          'Avatar de l\'utilisateur actuel après mise à jour: ${updatedCurrentUser?.avatar}',
        );
      }

      return true;
    } catch (e, stackTrace) {
      debugPrint('Erreur lors de la mise à jour de l\'utilisateur: $e');
      debugPrint('Stack trace: $stackTrace');
      return false;
    }
  }

  // Supprimer un utilisateur
  Future<bool> deleteUser(String userId) async {
    try {
      final users = await getUsers();
      final newUsers = users.where((u) => u.id != userId).toList();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'users',
        newUsers.map((user) => user.toJson()).toList(),
      );

      return true;
    } catch (e) {
      debugPrint('Erreur lors de la suppression de l\'utilisateur: $e');
      return false;
    }
  }

  // Obtenir un utilisateur par ID
  Future<User?> getUserById(String id) async {
    try {
      final users = await getUsers();
      return users.firstWhere((u) => u.id == id);
    } catch (e) {
      return null;
    }
  }

  // Obtenir un utilisateur par email
  Future<User?> getUserByEmail(String email) async {
    try {
      final users = await getUsers();
      return users.firstWhere((u) => u.email == email);
    } catch (e) {
      return null;
    }
  }

  // Obtenir la liste des rôles
  Future<List<UserRole>> getRoles() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rolesJson = prefs.getStringList('user_roles');

      if (rolesJson == null || rolesJson.isEmpty) {
        // Créer les rôles par défaut si aucun n'existe
        final defaultRoles = _createDefaultRoles();
        await saveRoles(defaultRoles);
        return defaultRoles;
      }

      return rolesJson.map((json) => UserRole.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Erreur lors de la récupération des rôles: $e');
      return _createDefaultRoles();
    }
  }

  // Créer les rôles par défaut
  List<UserRole> _createDefaultRoles() {
    final now = DateTime.now();
    return [
      UserRole(
        id: const Uuid().v4(),
        name: 'Administrateur',
        type: UserRoleType.admin,
        permissions: AppPermissions.getPermissionsForRole(UserRoleType.admin),
        description: 'Accès complet à toutes les fonctionnalités',
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: const Uuid().v4(),
        name: 'Gestionnaire',
        type: UserRoleType.manager,
        permissions: AppPermissions.getPermissionsForRole(UserRoleType.manager),
        description:
            'Accès à la plupart des fonctionnalités sauf la configuration système',
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: const Uuid().v4(),
        name: 'Employé',
        type: UserRoleType.employee,
        permissions: AppPermissions.getPermissionsForRole(
          UserRoleType.employee,
        ),
        description: 'Accès aux fonctionnalités quotidiennes',
        createdAt: now,
        updatedAt: now,
      ),
      UserRole(
        id: const Uuid().v4(),
        name: 'Utilisateur',
        type: UserRoleType.user,
        permissions: AppPermissions.getPermissionsForRole(UserRoleType.user),
        description: 'Accès limité en lecture seule',
        createdAt: now,
        updatedAt: now,
      ),
    ];
  }

  // Sauvegarder les rôles
  Future<bool> saveRoles(List<UserRole> roles) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'user_roles',
        roles.map((role) => role.toJson()).toList(),
      );
      return true;
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des rôles: $e');
      return false;
    }
  }

  // Ajouter un rôle
  Future<bool> addRole(UserRole role) async {
    try {
      final roles = await getRoles();
      roles.add(role);
      return saveRoles(roles);
    } catch (e) {
      debugPrint('Erreur lors de l\'ajout du rôle: $e');
      return false;
    }
  }

  // Mettre à jour un rôle
  Future<bool> updateRole(UserRole role) async {
    try {
      final roles = await getRoles();
      final index = roles.indexWhere((r) => r.id == role.id);

      if (index == -1) return false;

      roles[index] = role;
      return saveRoles(roles);
    } catch (e) {
      debugPrint('Erreur lors de la mise à jour du rôle: $e');
      return false;
    }
  }

  // Supprimer un rôle
  Future<bool> deleteRole(String roleId) async {
    try {
      final roles = await getRoles();
      final newRoles = roles.where((r) => r.id != roleId).toList();
      return saveRoles(newRoles);
    } catch (e) {
      debugPrint('Erreur lors de la suppression du rôle: $e');
      return false;
    }
  }

  // Vérifier si un utilisateur a une permission spécifique
  bool hasPermission(User user, String permission) {
    // L'administrateur a toutes les permissions
    if (user.role == 'admin') return true;

    // TODO: Implémenter la vérification des permissions basée sur les rôles
    // Pour l'instant, on utilise une logique simplifiée
    switch (user.role) {
      case 'admin':
        return true;
      case 'manager':
        return permission != AppPermissions.manageRoles &&
            permission != AppPermissions.deleteUser &&
            permission != AppPermissions.configureAI &&
            permission != AppPermissions.editSettings;
      case 'employee':
        return permission.startsWith('view_') ||
            permission == AppPermissions.createProduct ||
            permission == AppPermissions.editProduct ||
            permission == AppPermissions.createInvoice ||
            permission == AppPermissions.sendMessages ||
            permission == AppPermissions.useAI;
      default:
        return permission.startsWith('view_');
    }
  }
}
