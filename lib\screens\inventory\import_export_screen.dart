import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import '../../models/product.dart';
import '../../models/product_category.dart';
import '../../services/inventory_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_card.dart';

// Provider pour les produits
final productsProvider = FutureProvider<List<Product>>((ref) async {
  final inventoryService = ref.read(inventoryServiceProvider);
  return inventoryService.getProducts();
});

// Provider pour les catégories
final categoriesProvider = FutureProvider<List<ProductCategory>>((ref) async {
  final inventoryService = ref.read(inventoryServiceProvider);
  return inventoryService.getCategories();
});

class ImportExportScreen extends ConsumerStatefulWidget {
  const ImportExportScreen({super.key});

  @override
  ConsumerState<ImportExportScreen> createState() => _ImportExportScreenState();
}

class _ImportExportScreenState extends ConsumerState<ImportExportScreen> {
  bool _isExporting = false;
  bool _isImporting = false;
  String _statusMessage = '';
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(productsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Import / Export'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(gradient: NeonTheme.secondaryGradient),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NeonCard(
                color: NeonTheme.neonTurquoise,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Exportation des Données',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Exportez vos données d\'inventaire au format CSV pour les utiliser dans Excel ou d\'autres applications.',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NeonButton(
                          text: 'Télécharger Modèle',
                          icon: FontAwesomeIcons.fileArrowDown,
                          color: Colors.blue,
                          onPressed: _downloadTemplate,
                        ),
                        const SizedBox(width: 16),
                        NeonButton(
                          text: 'Exporter les Produits',
                          icon: FontAwesomeIcons.fileExport,
                          color: NeonTheme.neonTurquoise,
                          isLoading: _isExporting,
                          onPressed: () {
                            productsAsync.when(
                              data: (products) {
                                categoriesAsync.when(
                                  data: (categories) {
                                    _exportProducts(products, categories);
                                  },
                                  loading: () {},
                                  error: (_, __) {},
                                );
                              },
                              loading: () {},
                              error: (_, __) {},
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              NeonCard(
                color: NeonTheme.neonPurple,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Importation des Données',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Importez des données d\'inventaire à partir d\'un fichier CSV. Le fichier doit contenir les colonnes suivantes : nom, catégorie, quantité, prix.',
                      style: TextStyle(color: Colors.white),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NeonButton(
                          text: 'Importer des Produits',
                          icon: FontAwesomeIcons.fileImport,
                          color: NeonTheme.neonPurple,
                          isLoading: _isImporting,
                          onPressed: () {
                            categoriesAsync.when(
                              data: (categories) {
                                _importProducts(categories);
                              },
                              loading: () {},
                              error: (_, __) {},
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              if (_statusMessage.isNotEmpty) ...[
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color:
                        _hasError
                            ? Colors.red.withAlpha(77)
                            : Colors.green.withAlpha(77),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _hasError ? Colors.red : Colors.green,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _hasError
                            ? Icons.error_outline
                            : Icons.check_circle_outline,
                        color: _hasError ? Colors.red : Colors.green,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          _statusMessage,
                          style: TextStyle(
                            color: _hasError ? Colors.red : Colors.green,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _exportProducts(
    List<Product> products,
    List<ProductCategory> categories,
  ) async {
    setState(() {
      _isExporting = true;
      _statusMessage = '';
      _hasError = false;
    });

    try {
      // Préparer les données pour l'export
      final List<List<dynamic>> rows = [];

      // Ajouter l'en-tête selon le modèle fourni
      rows.add([
        'Name',
        'Category',
        'Quantity',
        'Price',
        'Description',
        'Image URL',
      ]);

      // Ajouter les produits
      for (final product in products) {
        final category = categories.firstWhere(
          (c) => c.id == product.categoryId,
          orElse:
              () => ProductCategory(
                id: '',
                name: 'Inconnue',
                price: 0,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
        );

        rows.add([
          product.name,
          category.name,
          product.quantity,
          product.price,
          product.description ?? '',
          product.imageUrl ?? '',
        ]);
      }

      // Convertir en CSV
      const csvConverter = ListToCsvConverter();
      final csv = csvConverter.convert(rows);

      // Sauvegarder le fichier
      final directory = await getApplicationDocumentsDirectory();
      final path =
          '${directory.path}/inventory_export_${DateTime.now().millisecondsSinceEpoch}.csv';
      final file = File(path);
      await file.writeAsString(csv);

      setState(() {
        _isExporting = false;
        _statusMessage = 'Exportation réussie ! Fichier sauvegardé à : $path';
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _isExporting = false;
        _statusMessage = 'Erreur lors de l\'exportation : $e';
        _hasError = true;
      });
    }
  }

  Future<void> _importProducts(List<ProductCategory> categories) async {
    setState(() {
      _isImporting = true;
      _statusMessage = '';
      _hasError = false;
    });

    try {
      // Sélectionner le fichier
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result == null || result.files.isEmpty) {
        setState(() {
          _isImporting = false;
          _statusMessage = 'Aucun fichier sélectionné.';
          _hasError = true;
        });
        return;
      }

      final file = File(result.files.first.path!);
      final content = await file.readAsString();

      // Convertir le CSV en liste
      const csvConverter = CsvToListConverter();
      final rows = csvConverter.convert(content);

      if (rows.isEmpty || rows.length < 2) {
        setState(() {
          _isImporting = false;
          _statusMessage =
              'Le fichier est vide ou ne contient pas de données valides.';
          _hasError = true;
        });
        return;
      }

      // Vérifier l'en-tête
      final header = rows[0];
      if (header.length < 4 ||
          !header.contains('Name') ||
          !header.contains('Category') ||
          !header.contains('Quantity') ||
          !header.contains('Price')) {
        setState(() {
          _isImporting = false;
          _statusMessage =
              'Le fichier ne contient pas les colonnes requises (Name, Category, Quantity, Price).';
          _hasError = true;
        });
        return;
      }

      // Indices des colonnes
      final nameIndex = header.indexOf('Name');
      final categoryIndex = header.indexOf('Category');
      final quantityIndex = header.indexOf('Quantity');
      final priceIndex = header.indexOf('Price');
      final descriptionIndex =
          header.contains('Description') ? header.indexOf('Description') : -1;
      final imageUrlIndex =
          header.contains('Image URL') ? header.indexOf('Image URL') : -1;

      // Importer les produits
      final inventoryService = ref.read(inventoryServiceProvider);
      int importedCount = 0;

      for (int i = 1; i < rows.length; i++) {
        final row = rows[i];
        if (row.length <= nameIndex ||
            row.length <= categoryIndex ||
            row.length <= quantityIndex ||
            row.length <= priceIndex) {
          continue;
        }

        final name = row[nameIndex].toString();
        final categoryName = row[categoryIndex].toString();
        final quantity = int.tryParse(row[quantityIndex].toString()) ?? 0;
        final price = double.tryParse(row[priceIndex].toString()) ?? 0.0;
        final description =
            descriptionIndex >= 0 && row.length > descriptionIndex
                ? row[descriptionIndex].toString()
                : null;
        final imageUrl =
            imageUrlIndex >= 0 && row.length > imageUrlIndex
                ? row[imageUrlIndex].toString()
                : null;

        // Trouver ou créer la catégorie
        ProductCategory? category;
        try {
          category = categories.firstWhere(
            (c) => c.name.toLowerCase() == categoryName.toLowerCase(),
          );
        } catch (e) {
          category = null;
        }

        if (category == null) {
          category = ProductCategory(
            id: const Uuid().v4(),
            name: categoryName,
            price: price,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await inventoryService.addCategory(category);
          categories.add(category);
        }

        // Créer le produit
        final product = Product(
          id: const Uuid().v4(),
          name: name,
          categoryId: category.id,
          categoryName: category.name,
          price: price,
          quantity: quantity,
          description: description,
          imageUrl: imageUrl,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await inventoryService.addProduct(product);
        importedCount++;
      }

      // Rafraîchir les données
      ref.refresh(productsProvider); // ignore: unused_result
      ref.refresh(categoriesProvider); // ignore: unused_result

      setState(() {
        _isImporting = false;
        _statusMessage =
            'Importation réussie ! $importedCount produits importés.';
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _isImporting = false;
        _statusMessage = 'Erreur lors de l\'importation : $e';
        _hasError = true;
      });
    }
  }

  Future<void> _downloadTemplate() async {
    setState(() {
      _statusMessage = '';
      _hasError = false;
    });

    try {
      // Préparer les données pour le modèle
      final List<List<dynamic>> rows = [];

      // Ajouter l'en-tête
      rows.add([
        'Name',
        'Category',
        'Quantity',
        'Price',
        'Description',
        'Image URL',
      ]);

      // Ajouter quelques exemples
      rows.add([
        'iPhone 16 Pro Max',
        'Vip-1',
        '7',
        '6000',
        'Dernier modèle Apple',
        'https://example.com/iphone.jpg',
      ]);

      rows.add([
        'iPhone 16 Pro',
        'Vip-1',
        '20',
        '6000',
        'Modèle Pro standard',
        '',
      ]);

      // Convertir en CSV
      const csvConverter = ListToCsvConverter();
      final csv = csvConverter.convert(rows);

      // Sauvegarder le fichier
      final directory = await getApplicationDocumentsDirectory();
      final path = '${directory.path}/inventory_template.csv';
      final file = File(path);
      await file.writeAsString(csv);

      setState(() {
        _statusMessage =
            'Modèle téléchargé avec succès ! Fichier sauvegardé à : $path';
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Erreur lors du téléchargement du modèle : $e';
        _hasError = true;
      });
    }
  }
}
