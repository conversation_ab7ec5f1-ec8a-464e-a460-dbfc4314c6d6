import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../models/ai/ai_message.dart';
import '../../models/messaging/message.dart';
import '../../models/messaging/conversation.dart';
// import '../../models/invoice/invoice.dart'; // Non utilisé
import '../../models/support/support_ticket.dart';
import '../ai/ai_service.dart';
import '../customer/customer_service.dart';
import '../messaging/messaging_service.dart';

// Provider pour le service d'assistance
final supportServiceProvider = Provider<SupportService>((ref) {
  final aiService = ref.watch(aiServiceProvider);
  final customerService = ref.watch(customerServiceProvider);
  final messagingService = ref.watch(messagingServiceProvider);

  return SupportService(
    aiService: aiService,
    customerService: customerService,
    messagingService: messagingService,
  );
});

class SupportService {
  final AIService aiService;
  final CustomerService customerService;
  final MessagingService messagingService;

  // Liste en mémoire des tickets de support (simulant une base de données)
  final List<SupportTicket> _tickets = [];

  SupportService({
    required this.aiService,
    required this.customerService,
    required this.messagingService,
  });

  // Analyser un message pour détecter une demande de support
  Future<bool> isSupportRequest(String message) async {
    final prompt = '''
    Analyse le message suivant et détermine s'il s'agit d'une demande de support, d'assistance, de SAV, de réclamation ou de suivi de commande.
    Réponds uniquement par "OUI" ou "NON".

    Message: "$message"
    ''';

    final response = await aiService.generateText([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    return response.toLowerCase().contains('oui');
  }

  // Déterminer le type de demande de support
  Future<SupportTicketType> detectSupportType(String message) async {
    final prompt = '''
    Analyse le message suivant et détermine le type de demande de support.
    Réponds uniquement avec l'une des catégories suivantes:
    - ORDER_TRACKING: suivi de commande ou livraison
    - RETURN_REFUND: retour ou remboursement
    - PRODUCT_ISSUE: problème avec un produit
    - TECHNICAL_SUPPORT: assistance technique
    - COMPLAINT: réclamation ou insatisfaction
    - GENERAL_INQUIRY: demande générale

    Message: "$message"
    ''';

    final response = await aiService.generateText([
      AIMessage(role: AIMessageRole.system, content: prompt),
    ]);

    if (response.toLowerCase().contains('order_tracking')) {
      return SupportTicketType.orderTracking;
    }
    if (response.toLowerCase().contains('return_refund')) {
      return SupportTicketType.returnRefund;
    }
    if (response.toLowerCase().contains('product_issue')) {
      return SupportTicketType.productIssue;
    }
    if (response.toLowerCase().contains('technical_support')) {
      return SupportTicketType.technicalSupport;
    }
    if (response.toLowerCase().contains('complaint')) {
      return SupportTicketType.complaint;
    }

    return SupportTicketType.generalInquiry;
  }

  // Extraire les informations de commande d'un message
  Future<String?> extractOrderId(String message) async {
    try {
      final prompt = '''
      Analyse le message suivant et extrait le numéro de commande ou de facture mentionné.
      Si aucun numéro n'est mentionné explicitement, réponds "AUCUN".

      Message: "$message"
      ''';

      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      if (response.toLowerCase().contains('aucun')) {
        return null;
      }

      return response.trim();
    } catch (e) {
      debugPrint('Erreur lors de l\'extraction du numéro de commande: $e');
      return null;
    }
  }

  // Créer un ticket de support à partir d'un message
  Future<SupportTicket?> createTicketFromMessage(
    Message message,
    Conversation conversation,
  ) async {
    try {
      // Vérifier si c'est une demande de support
      final isSupport = await isSupportRequest(message.content);
      if (!isSupport) return null;

      // Récupérer le client
      final customer = await customerService.getCustomerByContactId(
        message.sender,
      );
      if (customer == null) return null;

      // Déterminer le type de support
      final ticketType = await detectSupportType(message.content);

      // Extraire le numéro de commande si applicable
      String? orderId;
      if (ticketType == SupportTicketType.orderTracking ||
          ticketType == SupportTicketType.returnRefund ||
          ticketType == SupportTicketType.productIssue) {
        orderId = await extractOrderId(message.content);

        // Si aucun numéro de commande n'est trouvé, essayer de récupérer la dernière commande du client
        // if (orderId == null) { // Functionality removed
        //   final customerInvoices = await invoiceService.getInvoicesByCustomerId(
        //     customer.id,
        //   );
        //   if (customerInvoices.isNotEmpty) {
        //     // Trier par date décroissante
        //     customerInvoices.sort((a, b) => b.date.compareTo(a.date));
        //     orderId = customerInvoices.first.id;
        //   }
        // }
      }

      // Créer le ticket
      final ticket = SupportTicket(
        id: const Uuid().v4(),
        customerId: customer.id,
        customerName: customer.name,
        orderId: orderId,
        type: ticketType,
        status: SupportTicketStatus.open,
        priority: SupportTicketPriority.medium,
        subject: _generateTicketSubject(ticketType),
        description: message.content,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        assignedTo: null,
        conversationId: conversation.id,
      );

      // Enregistrer le ticket
      _tickets.add(ticket);

      return ticket;
    } catch (e) {
      debugPrint('Erreur lors de la création du ticket de support: $e');
      return null;
    }
  }

  // Générer un sujet pour le ticket en fonction du type
  String _generateTicketSubject(SupportTicketType type) {
    switch (type) {
      case SupportTicketType.orderTracking:
        return 'Suivi de commande';
      case SupportTicketType.returnRefund:
        return 'Demande de retour/remboursement';
      case SupportTicketType.productIssue:
        return 'Problème avec un produit';
      case SupportTicketType.technicalSupport:
        return 'Assistance technique';
      case SupportTicketType.complaint:
        return 'Réclamation';
      case SupportTicketType.generalInquiry:
        return 'Demande d\'information';
    }
  }

  // Générer une réponse automatique pour un ticket de support
  Future<String> generateSupportResponse(
    SupportTicket ticket,
    Message message,
  ) async {
    try {
      // Récupérer les informations de commande si applicable
      String orderInfo = '';
      // if (ticket.orderId != null) { // Functionality removed
      //   final order = await invoiceService.getInvoiceById(ticket.orderId!);
      //   if (order != null) {
      //     orderInfo = '''
      //     Informations sur la commande:
      //     - Numéro: ${order.id.substring(0, 8).toUpperCase()}
      //     - Date: ${_formatDate(order.date)}
      //     - Statut: ${_getStatusName(order.status)}
      //     - Montant: ${order.total} FCFA
      //     ''';
      //   }
      // }

      // Construire le prompt en fonction du type de ticket
      String prompt;

      switch (ticket.type) {
        case SupportTicketType.orderTracking:
          prompt = '''
          Tu es un assistant de service client pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

          $orderInfo

          Le client demande des informations sur le suivi de sa commande. Réponds de manière professionnelle et rassurante.
          Si la commande est en cours de traitement, indique les délais habituels de livraison.
          Si la commande a été expédiée, indique comment suivre le colis.

          Message du client: "${message.content}"
          ''';
          break;

        case SupportTicketType.returnRefund:
          prompt = '''
          Tu es un assistant de service client pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

          $orderInfo

          Le client souhaite effectuer un retour ou demander un remboursement. Explique la procédure de retour:
          1. Remplir le formulaire de retour
          2. Emballer le produit dans son emballage d'origine
          3. Envoyer le colis à l'adresse indiquée
          4. Le remboursement sera effectué sous 7 jours après réception

          Message du client: "${message.content}"
          ''';
          break;

        case SupportTicketType.productIssue:
          prompt = '''
          Tu es un assistant de service client pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

          $orderInfo

          Le client signale un problème avec un produit. Demande des précisions sur le problème et des photos si possible.
          Explique que nous prendrons en charge la réparation ou le remplacement si le produit est défectueux.

          Message du client: "${message.content}"
          ''';
          break;

        default:
          prompt = '''
          Tu es un assistant de service client pour HCP-DESIGN, une entreprise spécialisée dans les coques de téléphone personnalisées.

          $orderInfo

          Réponds à la demande du client de manière professionnelle et utile.
          Si tu ne peux pas résoudre le problème, propose de transférer la demande à un conseiller humain.

          Message du client: "${message.content}"
          ''';
          break;
      }

      // Générer la réponse
      final response = await aiService.generateText([
        AIMessage(role: AIMessageRole.system, content: prompt),
      ]);

      return response;
    } catch (e) {
      debugPrint('Erreur lors de la génération de la réponse de support: $e');
      return 'Je suis désolé, mais je rencontre des difficultés à traiter votre demande. Un conseiller va prendre en charge votre demande dans les plus brefs délais.';
    }
  }

  // Envoyer une réponse de support au client
  Future<void> sendSupportResponse(
    SupportTicket ticket,
    Conversation conversation,
    String response,
  ) async {
    try {
      // Envoyer le message
      await messagingService.sendMessage(
        contactId: conversation.participantId,
        content: response,
        channel: conversation.channel,
      );

      // Mettre à jour la conversation
      await messagingService.updateConversation(
        conversation.copyWith(
          lastMessage: response,
          lastMessageTime: DateTime.now(),
        ),
      );

      // Mettre à jour le ticket
      final updatedTicket = ticket.copyWith(updatedAt: DateTime.now());

      final index = _tickets.indexWhere((t) => t.id == ticket.id);
      if (index != -1) {
        _tickets[index] = updatedTicket;
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'envoi de la réponse de support: $e');
    }
  }

  // Récupérer tous les tickets
  Future<List<SupportTicket>> getTickets() async {
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
    return [..._tickets];
  }

  // Récupérer un ticket par son ID
  Future<SupportTicket?> getTicketById(String id) async {
    try {
      return _tickets.firstWhere((ticket) => ticket.id == id);
    } catch (e) {
      return null;
    }
  }

  // Récupérer les tickets d'un client
  Future<List<SupportTicket>> getTicketsByCustomerId(String customerId) async {
    return _tickets.where((ticket) => ticket.customerId == customerId).toList();
  }

  // Mettre à jour un ticket
  Future<void> updateTicket(SupportTicket ticket) async {
    final index = _tickets.indexWhere((t) => t.id == ticket.id);
    if (index == -1) {
      throw Exception('Ticket non trouvé');
    }

    _tickets[index] = ticket;
    await Future.delayed(const Duration(milliseconds: 300)); // Simuler un délai
  }

  // Méthode _formatDate supprimée car non utilisée

  // Obtenir le nom du statut d'une facture
  // String _getStatusName(InvoiceStatus status) { // Functionality removed
  //   switch (status) {
  //     case InvoiceStatus.draft:
  //       return 'Brouillon';
  //     case InvoiceStatus.pending:
  //       return 'En attente';
  //     case InvoiceStatus.paid:
  //       return 'Payée';
  //     case InvoiceStatus.overdue:
  //       return 'En retard';
  //     case InvoiceStatus.cancelled:
  //       return 'Annulée';
  //     case InvoiceStatus.refunded:
  //       return 'Remboursée';
  //     case InvoiceStatus.converted:
  //       return 'Convertie';
  //   }
  // }
}
