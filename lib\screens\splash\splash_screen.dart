import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/auth_service.dart';
import '../../providers/theme_provider.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  Future<void> _checkAuth() async {
    try {
      // Simuler un délai pour afficher l'écran de démarrage
      await Future.delayed(const Duration(seconds: 2));

      // Vérifier si le widget est toujours monté avant d'utiliser le contexte
      if (!mounted) return;

      final isLoggedIn = ref.read(authServiceProvider).isLoggedIn;
      if (isLoggedIn) {
        context.go('/dashboard');
      } else {
        context.go('/login');
      }
    } catch (e) {
      // Gérer les erreurs qui pourraient survenir pendant la vérification de l'authentification
      if (mounted) {
        setState(() {
          _errorMessage = 'Erreur lors du chargement: $e';
        });

        // Log l'erreur
        debugPrint('Erreur dans SplashScreen: $e');
      }
    }
  }

  // Méthode pour réessayer en cas d'erreur
  void _retry() {
    setState(() {
      _errorMessage = null;
    });
    _checkAuth();
  }

  @override
  Widget build(BuildContext context) {
    final appTheme = ref.watch(themeProvider);
    return Scaffold(
      body: Center(
        child:
            _errorMessage != null
                ? _buildErrorView()
                : _buildLoadingView(appTheme),
      ),
    );
  }

  Widget _buildLoadingView(dynamic appTheme) {
    final isDark = ref.watch(isDarkThemeProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 20),
        Text(
          'Chargement...',
          style: TextStyle(
            fontSize: 16,
            color:
                isDark
                    ? Colors.white.withValues(alpha: 204)
                    : Colors.black.withValues(alpha: 204),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.error_outline, color: Colors.red, size: 60),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Text(
            _errorMessage!,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.red),
          ),
        ),
        const SizedBox(height: 24),
        ElevatedButton(onPressed: _retry, child: const Text('Réessayer')),
      ],
    );
  }
}
