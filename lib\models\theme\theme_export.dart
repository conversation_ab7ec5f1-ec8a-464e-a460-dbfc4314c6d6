import 'dart:convert';
import 'package:flutter/material.dart';

/// Modèle pour l'exportation et l'importation de thèmes
class ThemeExport {
  final String name;
  final String description;
  final String version;
  final DateTime createdAt;
  final ThemeColors colors;
  final ThemeGradients gradients;
  final bool isDark;

  ThemeExport({
    required this.name,
    required this.description,
    this.version = '1.0',
    required this.createdAt,
    required this.colors,
    required this.gradients,
    required this.isDark,
  });

  /// Crée une copie de ce ThemeExport avec les propriétés spécifiées remplacées
  ThemeExport copyWith({
    String? name,
    String? description,
    String? version,
    DateTime? createdAt,
    ThemeColors? colors,
    ThemeGradients? gradients,
    bool? isDark,
  }) {
    return ThemeExport(
      name: name ?? this.name,
      description: description ?? this.description,
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      colors: colors ?? this.colors,
      gradients: gradients ?? this.gradients,
      isDark: isDark ?? this.isDark,
    );
  }

  /// Convertit le thème en Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'version': version,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'colors': colors.toMap(),
      'gradients': gradients.toMap(),
      'isDark': isDark,
    };
  }

  /// Crée un ThemeExport à partir d'une Map
  factory ThemeExport.fromMap(Map<String, dynamic> map) {
    return ThemeExport(
      name: map['name'] ?? 'Thème personnalisé',
      description: map['description'] ?? 'Thème importé',
      version: map['version'] ?? '1.0',
      createdAt: DateTime.fromMillisecondsSinceEpoch(
        map['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      ),
      colors: ThemeColors.fromMap(map['colors'] ?? {}),
      gradients: ThemeGradients.fromMap(map['gradients'] ?? {}),
      isDark: map['isDark'] ?? false,
    );
  }

  /// Convertit le thème en JSON
  String toJson() => json.encode(toMap());

  /// Crée un ThemeExport à partir d'une chaîne JSON
  factory ThemeExport.fromJson(String source) =>
      ThemeExport.fromMap(json.decode(source));

  /// Génère un ThemeData Flutter à partir de ce ThemeExport
  ThemeData toThemeData() {
    final brightness = isDark ? Brightness.dark : Brightness.light;

    return ThemeData(
      brightness: brightness,
      primaryColor: colors.primary,
      scaffoldBackgroundColor: colors.background,
      colorScheme: ColorScheme(
        brightness: brightness,
        primary: colors.primary,
        onPrimary: colors.onPrimary,
        secondary: colors.secondary,
        onSecondary: colors.onSecondary,
        error: colors.error,
        onError: colors.onError,
        // Utiliser les propriétés background et onBackground comme surface et onSurface
        surface: colors.background,
        onSurface: colors.onBackground,
        // Garder les propriétés surface et onSurface pour la rétrocompatibilité
        surfaceContainerHighest: colors.surface,
        onSurfaceVariant: colors.onSurface,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colors.surface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: colors.onSurface,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
        ),
        iconTheme: IconThemeData(color: colors.onSurface),
      ),
      cardTheme: CardTheme(
        color: colors.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: colors.border, width: 1),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors.primary,
          foregroundColor: colors.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: colors.primary),
      ),
      iconTheme: IconThemeData(color: colors.primary, size: 24),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: colors.inputFill,
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colors.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 14,
        ),
      ),
    );
  }
}

/// Modèle pour les couleurs d'un thème
class ThemeColors {
  final Color primary;
  final Color onPrimary;
  final Color secondary;
  final Color onSecondary;
  final Color background;
  final Color onBackground;
  final Color surface;
  final Color onSurface;
  final Color error;
  final Color onError;
  final Color border;
  final Color inputFill;

  ThemeColors({
    required this.primary,
    required this.onPrimary,
    required this.secondary,
    required this.onSecondary,
    required this.background,
    required this.onBackground,
    required this.surface,
    required this.onSurface,
    required this.error,
    required this.onError,
    required this.border,
    required this.inputFill,
  });

  /// Convertit les couleurs en Map
  Map<String, dynamic> toMap() {
    return {
      'primary': primary.toARGB32(),
      'onPrimary': onPrimary.toARGB32(),
      'secondary': secondary.toARGB32(),
      'onSecondary': onSecondary.toARGB32(),
      'background': background.toARGB32(),
      'onBackground': onBackground.toARGB32(),
      'surface': surface.toARGB32(),
      'onSurface': onSurface.toARGB32(),
      'error': error.toARGB32(),
      'onError': onError.toARGB32(),
      'border': border.toARGB32(),
      'inputFill': inputFill.toARGB32(),
    };
  }

  /// Crée un ThemeColors à partir d'une Map
  factory ThemeColors.fromMap(Map<String, dynamic> map) {
    return ThemeColors(
      primary: Color(map['primary'] ?? 0xFF6A5ACD),
      onPrimary: Color(map['onPrimary'] ?? 0xFFFFFFFF),
      secondary: Color(map['secondary'] ?? 0xFFBF00FF),
      onSecondary: Color(map['onSecondary'] ?? 0xFFFFFFFF),
      background: Color(map['background'] ?? 0xFF0F0F1E),
      onBackground: Color(map['onBackground'] ?? 0xFFFFFFFF),
      surface: Color(map['surface'] ?? 0xFF1A1A2E),
      onSurface: Color(map['onSurface'] ?? 0xFFFFFFFF),
      error: Color(map['error'] ?? 0xFFFF6F61),
      onError: Color(map['onError'] ?? 0xFFFFFFFF),
      border: Color(map['border'] ?? 0xFF2A2A3E),
      inputFill: Color(map['inputFill'] ?? 0xFF1A1A2E),
    );
  }
}

/// Modèle pour les dégradés d'un thème
class ThemeGradients {
  final List<int> mainGradientColors;
  final List<int> sidebarGradientColors;
  final List<int> cardGradientColors;
  final List<int> secondaryGradientColors;
  final List<int> activeGradientColors;

  ThemeGradients({
    required this.mainGradientColors,
    required this.sidebarGradientColors,
    required this.cardGradientColors,
    required this.secondaryGradientColors,
    required this.activeGradientColors,
  });

  /// Convertit les dégradés en Map
  Map<String, dynamic> toMap() {
    return {
      'mainGradientColors': mainGradientColors,
      'sidebarGradientColors': sidebarGradientColors,
      'cardGradientColors': cardGradientColors,
      'secondaryGradientColors': secondaryGradientColors,
      'activeGradientColors': activeGradientColors,
    };
  }

  /// Crée un ThemeGradients à partir d'une Map
  factory ThemeGradients.fromMap(Map<String, dynamic> map) {
    return ThemeGradients(
      mainGradientColors: List<int>.from(
        map['mainGradientColors'] ?? [0xFF0F0F1E, 0xFF6A5ACD],
      ),
      sidebarGradientColors: List<int>.from(
        map['sidebarGradientColors'] ?? [0xFF1A1A2E, 0xFF0F0F1E],
      ),
      cardGradientColors: List<int>.from(
        map['cardGradientColors'] ?? [0xFF1A1A2E, 0xFF252542],
      ),
      secondaryGradientColors: List<int>.from(
        map['secondaryGradientColors'] ?? [0xFF1A1A2E, 0xFF2A2A3E],
      ),
      activeGradientColors: List<int>.from(
        map['activeGradientColors'] ?? [0xFF6A5ACD, 0xFFBF00FF],
      ),
    );
  }

  /// Obtient le dégradé principal
  LinearGradient get mainGradient => _createGradient(mainGradientColors);

  /// Obtient le dégradé de la barre latérale
  LinearGradient get sidebarGradient => _createGradient(sidebarGradientColors);

  /// Obtient le dégradé des cartes
  LinearGradient get cardGradient => _createGradient(cardGradientColors);

  /// Obtient le dégradé secondaire
  LinearGradient get secondaryGradient =>
      _createGradient(secondaryGradientColors);

  /// Obtient le dégradé actif
  LinearGradient get activeGradient => _createGradient(activeGradientColors);

  /// Crée un dégradé à partir d'une liste de couleurs
  LinearGradient _createGradient(List<int> colors) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: colors.map((c) => Color(c)).toList(),
    );
  }
}
