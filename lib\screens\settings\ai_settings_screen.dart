import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import '../../models/ai/ai_config.dart';
import '../../models/ai/ai_provider.dart';
import '../../models/ai/ai_message.dart';
import '../../services/ai/ai_service.dart';
import '../../theme/neon_theme.dart';
import '../../widgets/neon_card.dart';
import '../../widgets/neon_button.dart';
import '../../widgets/neon_text_field.dart';

class AISettingsScreen extends ConsumerStatefulWidget {
  const AISettingsScreen({super.key});

  @override
  ConsumerState<AISettingsScreen> createState() => _AISettingsScreenState();
}

class _AISettingsScreenState extends ConsumerState<AISettingsScreen> {
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _organizationIdController =
      TextEditingController();
  final TextEditingController _systemPromptController = TextEditingController();

  AIProviderType _selectedProvider = AIProviderType.openai;
  String _selectedModel = '';
  double _temperature = 0.7;
  int _maxTokens = 1000;
  bool _enableAutoResponses = true;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadConfig();
  }

  Future<void> _loadConfig() async {
    final config = ref.read(aiConfigProvider);

    setState(() {
      _apiKeyController.text = config.apiKey;
      _organizationIdController.text = config.organizationId ?? '';
      _systemPromptController.text = config.systemPrompt;
      _selectedProvider = config.provider;
      _selectedModel = config.model;
      _temperature = config.temperature;
      _maxTokens = config.maxTokens;
      _enableAutoResponses = config.enableAutoResponses;
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _organizationIdController.dispose();
    _systemPromptController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration de l\'IA'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Container(
                decoration: const BoxDecoration(
                  gradient: NeonTheme.secondaryGradient,
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Configuration de l\'Assistant IA',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Configurez les paramètres de l\'assistant IA pour personnaliser son comportement.',
                        style: TextStyle(color: Colors.white),
                      ),
                      const SizedBox(height: 24),

                      // Sélection du fournisseur d'IA
                      NeonCard(
                        color: NeonTheme.neonPurple,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Fournisseur d\'IA',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              DropdownButtonFormField<AIProviderType>(
                                value: _selectedProvider,
                                decoration: InputDecoration(
                                  labelText: 'Fournisseur',
                                  labelStyle: const TextStyle(
                                    color: Colors.white,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: NeonTheme.neonPurple,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.black.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                ),
                                dropdownColor: Colors.black,
                                style: const TextStyle(color: Colors.white),
                                items:
                                    AIProviderType.values.map((provider) {
                                      return DropdownMenuItem<AIProviderType>(
                                        value: provider,
                                        child: Text(provider.displayName),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedProvider = value;
                                      _selectedModel = value.defaultModel;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              DropdownButtonFormField<String>(
                                value: _selectedModel,
                                decoration: InputDecoration(
                                  labelText: 'Modèle',
                                  labelStyle: const TextStyle(
                                    color: Colors.white,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: NeonTheme.neonPurple,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.black.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                ),
                                dropdownColor: Colors.black,
                                style: const TextStyle(color: Colors.white),
                                items:
                                    _selectedProvider.availableModels.map((
                                      model,
                                    ) {
                                      return DropdownMenuItem<String>(
                                        value: model,
                                        child: Text(model),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedModel = value;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),
                              NeonTextField(
                                controller: _apiKeyController,
                                labelText: 'Clé API',
                                obscureText: true,
                              ),
                              if (_selectedProvider.requiresOrganizationId) ...[
                                const SizedBox(height: 16),
                                NeonTextField(
                                  controller: _organizationIdController,
                                  labelText: 'ID d\'organisation',
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Paramètres de génération
                      NeonCard(
                        color: NeonTheme.neonCyan,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Paramètres de génération',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                'Température',
                                style: TextStyle(color: Colors.white),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Slider(
                                      value: _temperature,
                                      min: 0.0,
                                      max: 1.0,
                                      divisions: 10,
                                      activeColor: NeonTheme.neonCyan,
                                      inactiveColor: Colors.white.withValues(
                                        alpha: 77,
                                      ), // 0.3 * 255 ≈ 77
                                      onChanged: (value) {
                                        setState(() {
                                          _temperature = value;
                                        });
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Text(
                                    _temperature.toStringAsFixed(1),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                'Nombre maximum de tokens',
                                style: TextStyle(color: Colors.white),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Slider(
                                      value: _maxTokens.toDouble(),
                                      min: 100,
                                      max: 4000,
                                      divisions: 39,
                                      activeColor: NeonTheme.neonCyan,
                                      inactiveColor: Colors.white.withValues(
                                        alpha: 77,
                                      ), // 0.3 * 255 ≈ 77
                                      onChanged: (value) {
                                        setState(() {
                                          _maxTokens = value.toInt();
                                        });
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Text(
                                    _maxTokens.toString(),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  const Text(
                                    'Activer les réponses automatiques',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  const Spacer(),
                                  Switch(
                                    value: _enableAutoResponses,
                                    onChanged: (value) {
                                      setState(() {
                                        _enableAutoResponses = value;
                                      });
                                    },
                                    activeColor: NeonTheme.neonCyan,
                                    activeTrackColor: Colors.white.withValues(
                                      alpha: 128,
                                    ), // 0.5 * 255 ≈ 128
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Prompt système
                      NeonCard(
                        color: NeonTheme.neonGreen,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Prompt système',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'Ce texte définit le comportement de l\'IA et lui donne des instructions générales.',
                                style: TextStyle(color: Colors.white),
                              ),
                              const SizedBox(height: 16),
                              TextField(
                                controller: _systemPromptController,
                                maxLines: 8,
                                style: const TextStyle(color: Colors.white),
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: Colors.white,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: const BorderSide(
                                      color: NeonTheme.neonGreen,
                                      width: 2,
                                    ),
                                  ),
                                  filled: true,
                                  fillColor: Colors.black.withValues(
                                    alpha: 153,
                                  ), // 0.6 * 255 ≈ 153
                                ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      setState(() {
                                        _systemPromptController.text =
                                            AIConfig.defaultConfig()
                                                .systemPrompt;
                                      });
                                    },
                                    child: const Text(
                                      'Réinitialiser',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Bouton de sauvegarde
                      Center(
                        child: NeonButton(
                          text: 'Enregistrer les modifications',
                          icon: FontAwesomeIcons.floppyDisk,
                          color: NeonTheme.neonGreen,
                          onPressed: _saveConfig,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Bouton pour tester la configuration
                      Center(
                        child: NeonButton(
                          text: 'Tester la configuration',
                          icon: FontAwesomeIcons.vial,
                          color: NeonTheme.neonPurple,
                          onPressed: _testConfig,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Bouton pour voir les performances
                      Center(
                        child: NeonButton(
                          text: 'Voir les performances',
                          icon: FontAwesomeIcons.chartLine,
                          color: NeonTheme.neonCyan,
                          onPressed: () {
                            context.push('/settings/ai/performance');
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Future<void> _saveConfig() async {
    try {
      final config = AIConfig(
        provider: _selectedProvider,
        apiKey: _apiKeyController.text,
        organizationId:
            _organizationIdController.text.isEmpty
                ? null
                : _organizationIdController.text,
        model: _selectedModel,
        temperature: _temperature,
        maxTokens: _maxTokens,
        systemPrompt: _systemPromptController.text,
        enableAutoResponses: _enableAutoResponses,
      );

      ref.read(aiConfigProvider.notifier).updateConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration enregistrée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de l\'enregistrement de la configuration: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _testConfig() async {
    // Créer une configuration temporaire pour le test
    final config = AIConfig(
      provider: _selectedProvider,
      apiKey: _apiKeyController.text,
      organizationId:
          _organizationIdController.text.isEmpty
              ? null
              : _organizationIdController.text,
      model: _selectedModel,
      temperature: _temperature,
      maxTokens: _maxTokens,
      systemPrompt: _systemPromptController.text,
      enableAutoResponses: _enableAutoResponses,
    );

    // Créer un service IA temporaire avec cette configuration
    final aiService = AIService(config: config);

    // Afficher un indicateur de chargement
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test en cours...'),
          duration: Duration(seconds: 1),
        ),
      );
    }

    try {
      setState(() {
        _isLoading = true;
      });

      // Envoyer un message de test simple
      final testMessages = [
        AIMessage(role: AIMessageRole.system, content: config.systemPrompt),
        AIMessage(
          role: AIMessageRole.user,
          content:
              'Bonjour, ceci est un test de configuration. Réponds simplement "La configuration fonctionne correctement."',
        ),
      ];

      final response = await aiService.sendMessage(testMessages);

      setState(() {
        _isLoading = false;
      });

      // Afficher le résultat dans une boîte de dialogue
      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text(
                  'Résultat du test',
                  style: TextStyle(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'La configuration fonctionne !',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Réponse de l\'IA :',
                      style: TextStyle(color: Colors.white70),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: NeonTheme.neonPurple),
                      ),
                      child: Text(
                        response,
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
                backgroundColor: const Color(0xFF1A1A2E),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      'Fermer',
                      style: TextStyle(color: NeonTheme.neonCyan),
                    ),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      debugPrint('Erreur lors du test de la configuration: $e');

      // Afficher l'erreur dans une boîte de dialogue
      if (mounted) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text(
                  'Erreur de configuration',
                  style: TextStyle(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'La configuration a échoué :',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(
                          alpha: 153,
                        ), // 0.6 * 255 ≈ 153
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red),
                      ),
                      child: Text(
                        e.toString(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Vérifiez votre clé API et les autres paramètres, puis réessayez.',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
                backgroundColor: const Color(0xFF1A1A2E),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      'Fermer',
                      style: TextStyle(color: NeonTheme.neonCyan),
                    ),
                  ),
                ],
              ),
        );
      }
    }
  }
}
