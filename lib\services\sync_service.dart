import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import '../models/customer/customer.dart';
import '../models/task/task.dart';
import '../models/inventory/product.dart';
// import '../models/invoice.dart'; // Non utilisé
import '../models/messaging/message.dart';
import '../models/product_category.dart';
import 'supabase_service.dart';
import 'customer/customer_service.dart';
import 'task/task_service.dart';
import 'inventory/inventory_service.dart';
import 'messaging/messaging_service.dart';

// Provider pour le service de synchronisation
final syncServiceProvider = Provider<SyncService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final customerService = ref.watch(customerServiceProvider);
  final tasksNotifier = ref.watch(tasksProvider.notifier);
  final productsNotifier = ref.watch(productsProvider.notifier);
  final messagingService = ref.watch(messagingServiceProvider);

  return SyncService(
    supabaseService: supabaseService,
    customerService: customerService,
    tasksNotifier: tasksNotifier,
    productsNotifier: productsNotifier,
    messagingService: messagingService,
  );
});

class SyncService {
  final SupabaseService supabaseService;
  final CustomerService customerService;
  final TasksNotifier tasksNotifier;
  final ProductsNotifier productsNotifier;
  final MessagingService messagingService;

  // Contrôle de l'état de synchronisation
  bool _isSyncing = false;
  DateTime _lastSyncTime = DateTime.fromMillisecondsSinceEpoch(0);
  final List<RealtimeChannel> _channels = [];

  SyncService({
    required this.supabaseService,
    required this.customerService,
    required this.tasksNotifier,
    required this.productsNotifier,
    required this.messagingService,
  });

  // Getters pour l'état de synchronisation
  bool get isSyncing => _isSyncing;
  DateTime get lastSyncTime => _lastSyncTime;

  // Synchroniser toutes les données
  Future<void> syncAll() async {
    if (!supabaseService.isInitialized) {
      debugPrint('Supabase n\'est pas initialisé, impossible de synchroniser');
      return;
    }

    if (_isSyncing) {
      debugPrint('Synchronisation déjà en cours, opération ignorée');
      return;
    }

    _isSyncing = true;

    try {
      // Synchronisation bidirectionnelle
      await syncCustomers();
      await syncTasks();
      await syncProducts();
      await syncProductCategories();
      await syncMessages();

      // Mettre à jour la date de dernière synchronisation
      _lastSyncTime = DateTime.now();

      // Configurer les abonnements en temps réel si ce n'est pas déjà fait
      _setupRealtimeSubscriptions();

      debugPrint(
        'Synchronisation terminée avec succès: ${_lastSyncTime.toIso8601String()}',
      );
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation: $e');
    } finally {
      _isSyncing = false;
    }
  }

  // Synchroniser les clients
  Future<void> syncCustomers() async {
    try {
      // Récupérer les clients depuis Supabase
      final supabaseCustomers = await supabaseService.fetchCustomers();

      // Récupérer les clients locaux
      final localCustomers = await customerService.getCustomers();

      // Convertir les clients Supabase en objets Customer
      final remoteCustomers =
          supabaseCustomers.map((data) {
            return Customer(
              id: data['id'] ?? const Uuid().v4(),
              name: data['name'] ?? '',
              email: data['email'],
              phone: data['phone'],
              createdAt: DateTime.parse(
                data['created_at'] ?? DateTime.now().toIso8601String(),
              ),
              lastContact:
                  data['updated_at'] != null
                      ? DateTime.parse(data['updated_at'])
                      : DateTime.now(),
              status: _parseCustomerStatus(data['status'] ?? 'lead'),
              leadQualification: LeadQualification.cold,
              notes: data['notes'],
              tags: data['tags'] != null ? List<String>.from(data['tags']) : [],
            );
          }).toList();

      // Mettre à jour les clients locaux
      for (final remoteCustomer in remoteCustomers) {
        final existingCustomer = localCustomers.firstWhere(
          (c) => c.id == remoteCustomer.id,
          orElse: () => Customer.empty(),
        );

        if (existingCustomer.id.isEmpty) {
          // Nouveau client, l'ajouter localement
          await customerService.addCustomer(remoteCustomer);
        } else {
          // Client existant, le mettre à jour si nécessaire
          if (_isCustomerDifferent(existingCustomer, remoteCustomer)) {
            await customerService.updateCustomer(remoteCustomer);
          }
        }
      }

      debugPrint('Synchronisation des clients terminée');
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation des clients: $e');
    }
  }

  // Synchroniser les tâches
  Future<void> syncTasks() async {
    try {
      // Récupérer les tâches depuis Supabase
      final supabseTasks = await supabaseService.fetchTasks();

      // Récupérer les tâches locales via une méthode publique
      final localTasks = await tasksNotifier.getTasks();

      // Convertir les tâches Supabase en objets Task
      final remoteTasks =
          supabseTasks.map((data) {
            return Task(
              id: data['id'] ?? const Uuid().v4(),
              title: data['title'] ?? '',
              description: data['description'] ?? '',
              createdAt: DateTime.parse(
                data['created_at'] ?? DateTime.now().toIso8601String(),
              ),
              updatedAt:
                  data['updated_at'] != null
                      ? DateTime.parse(data['updated_at'])
                      : null,
              dueDate: DateTime.parse(
                data['due_date'] ?? DateTime.now().toIso8601String(),
              ),
              priority: _parseTaskPriority(data['priority'] ?? 'medium'),
              status: _parseTaskStatus(data['status'] ?? 'todo'),
              tags: data['tags'] != null ? List<String>.from(data['tags']) : [],
              assignedToId: data['assigned_to_id'],
              assignedToName: data['assigned_to_name'],
              hasReminder: data['has_reminder'] ?? false,
              reminderTime:
                  data['reminder_time'] != null
                      ? DateTime.parse(data['reminder_time'])
                      : null,
              isRecurring: data['is_recurring'] ?? false,
              recurrencePattern: data['recurrence_pattern'],
              parentTaskId: data['parent_task_id'],
            );
          }).toList();

      // Mettre à jour les tâches locales
      for (final remoteTask in remoteTasks) {
        final existingTaskIndex = localTasks.indexWhere(
          (t) => t.id == remoteTask.id,
        );

        if (existingTaskIndex == -1) {
          // Nouvelle tâche, l'ajouter localement
          tasksNotifier.addTask(remoteTask);
        } else {
          // Tâche existante, la mettre à jour si nécessaire
          final existingTask = localTasks[existingTaskIndex];
          if (_isTaskDifferent(existingTask, remoteTask)) {
            tasksNotifier.updateTask(remoteTask);
          }
        }
      }

      debugPrint('Synchronisation des tâches terminée');
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation des tâches: $e');
    }
  }

  // Synchroniser les produits
  Future<void> syncProducts() async {
    try {
      // Récupérer les produits depuis Supabase
      final supabaseProducts = await supabaseService.fetchProducts();

      // Récupérer les produits locaux via une méthode publique
      final localProducts = await productsNotifier.getProducts();

      // Convertir les produits Supabase en objets Product
      final remoteProducts =
          supabaseProducts.map((data) {
            return Product(
              id: data['id'] ?? const Uuid().v4(),
              name: data['name'] ?? '',
              description: data['description'] ?? '',
              price: double.parse(data['price']?.toString() ?? '0'),
              quantity: data['quantity'] ?? 0,
              category: data['category_name'] ?? '',
              imageUrl: data['image_url'],
              isActive: data['status'] != 'inactive',
              createdAt: DateTime.parse(
                data['created_at'] ?? DateTime.now().toIso8601String(),
              ),
              updatedAt: DateTime.parse(
                data['updated_at'] ?? DateTime.now().toIso8601String(),
              ),
            );
          }).toList();

      // Mettre à jour les produits locaux
      for (final remoteProduct in remoteProducts) {
        final existingProductIndex = localProducts.indexWhere(
          (p) => p.id == remoteProduct.id,
        );

        if (existingProductIndex == -1) {
          // Nouveau produit, l'ajouter localement
          await productsNotifier.addProduct(remoteProduct);
        } else {
          // Produit existant, le mettre à jour si nécessaire
          final existingProduct = localProducts[existingProductIndex];
          if (_isProductDifferent(existingProduct, remoteProduct)) {
            await productsNotifier.updateProduct(
              remoteProduct.id,
              remoteProduct,
            );
          }
        }
      }

      debugPrint('Synchronisation des produits terminée');
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation des produits: $e');
    }
  }

  // Synchroniser les catégories de produits
  Future<void> syncProductCategories() async {
    try {
      // Récupérer les catégories depuis Supabase
      final supabaseCategories = await supabaseService.fetchProductCategories();

      // Récupérer les catégories locales via SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getStringList('product_categories') ?? [];
      final localCategories =
          categoriesJson.map((json) => ProductCategory.fromJson(json)).toList();

      // Convertir les catégories Supabase en objets ProductCategory
      final remoteCategories =
          supabaseCategories.map((data) {
            return ProductCategory(
              id: data['id'] ?? const Uuid().v4(),
              name: data['name'] ?? '',
              price: double.parse(data['price']?.toString() ?? '0'),
              description: data['description'],
              createdAt: DateTime.parse(
                data['created_at'] ?? DateTime.now().toIso8601String(),
              ),
              updatedAt: DateTime.parse(
                data['updated_at'] ?? DateTime.now().toIso8601String(),
              ),
            );
          }).toList();

      // Mettre à jour les catégories locales
      List<ProductCategory> updatedCategories = [...localCategories];

      for (final remoteCategory in remoteCategories) {
        final existingCategoryIndex = updatedCategories.indexWhere(
          (c) => c.id == remoteCategory.id,
        );

        if (existingCategoryIndex == -1) {
          // Nouvelle catégorie, l'ajouter localement
          updatedCategories.add(remoteCategory);
        } else {
          // Catégorie existante, la mettre à jour si nécessaire
          final existingCategory = updatedCategories[existingCategoryIndex];
          if (_isCategoryDifferent(existingCategory, remoteCategory)) {
            updatedCategories[existingCategoryIndex] = remoteCategory;
          }
        }
      }

      // Sauvegarder les catégories mises à jour
      final updatedCategoriesJson =
          updatedCategories.map((category) => category.toJson()).toList();
      await prefs.setStringList('product_categories', updatedCategoriesJson);

      // Envoyer les catégories locales qui n'existent pas sur Supabase
      for (final localCategory in localCategories) {
        final existsOnServer = remoteCategories.any(
          (c) => c.id == localCategory.id,
        );
        if (!existsOnServer) {
          await supabaseService.insertProductCategory({
            'id': localCategory.id,
            'name': localCategory.name,
            'price': localCategory.price,
            'description': localCategory.description,
            'created_at': localCategory.createdAt.toIso8601String(),
            'updated_at': localCategory.updatedAt.toIso8601String(),
          });
        }
      }

      debugPrint('Synchronisation des catégories de produits terminée');
    } catch (e) {
      debugPrint(
        'Erreur lors de la synchronisation des catégories de produits: $e',
      );
    }
  }

  // Méthodes utilitaires
  CustomerStatus _parseCustomerStatus(String status) {
    switch (status.toLowerCase()) {
      case 'lead':
        return CustomerStatus.lead;
      case 'customer':
        return CustomerStatus.customer;
      case 'vip':
        return CustomerStatus.vip;
      case 'inactive':
        return CustomerStatus.inactive;
      case 'blocked':
        return CustomerStatus.blocked;
      default:
        return CustomerStatus.lead;
    }
  }

  TaskPriority _parseTaskPriority(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return TaskPriority.high;
      case 'medium':
        return TaskPriority.medium;
      case 'low':
        return TaskPriority.low;
      default:
        return TaskPriority.medium;
    }
  }

  TaskStatus _parseTaskStatus(String status) {
    switch (status.toLowerCase()) {
      case 'todo':
        return TaskStatus.todo;
      case 'in_progress':
        return TaskStatus.inProgress;
      case 'completed':
        return TaskStatus.completed;
      case 'cancelled':
        return TaskStatus.cancelled;
      default:
        return TaskStatus.todo;
    }
  }

  bool _isCustomerDifferent(Customer a, Customer b) {
    return a.name != b.name ||
        a.email != b.email ||
        a.phone != b.phone ||
        a.status != b.status ||
        a.notes != b.notes;
  }

  bool _isTaskDifferent(Task a, Task b) {
    return a.title != b.title ||
        a.description != b.description ||
        a.status != b.status ||
        a.priority != b.priority ||
        a.dueDate != b.dueDate ||
        a.assignedToId != b.assignedToId;
  }

  bool _isProductDifferent(Product a, Product b) {
    return a.name != b.name ||
        a.description != b.description ||
        a.price != b.price ||
        a.quantity != b.quantity ||
        a.category != b.category ||
        a.isActive != b.isActive;
  }

  bool _isCategoryDifferent(ProductCategory a, ProductCategory b) {
    return a.name != b.name ||
        a.price != b.price ||
        a.description != b.description;
  }

  // Synchroniser les messages
  Future<void> syncMessages() async {
    try {
      // Récupérer les messages depuis Supabase
      final supabaseMessages = await supabaseService.fetchMessages();

      // Récupérer les messages locaux
      final localMessages = await messagingService.getMessages();

      // Convertir les messages Supabase en objets Message
      final remoteMessages =
          supabaseMessages.map((data) {
            return Message.fromMap(data);
          }).toList();

      // Mettre à jour les messages locaux
      for (final remoteMessage in remoteMessages) {
        final existingMessageIndex = localMessages.indexWhere(
          (m) => m.id == remoteMessage.id,
        );

        if (existingMessageIndex == -1) {
          // Nouveau message, l'ajouter localement
          await messagingService.addMessage(remoteMessage);
        }
        // Nous ne mettons pas à jour les messages existants car ils sont immuables
      }

      // Envoyer les messages locaux qui n'existent pas sur Supabase
      for (final localMessage in localMessages) {
        final existsOnServer = remoteMessages.any(
          (m) => m.id == localMessage.id,
        );
        if (!existsOnServer) {
          await supabaseService.insertMessage(localMessage.toMap());
        }
      }

      debugPrint('Synchronisation des messages terminée');
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation des messages: $e');
    }
  }

  // Configurer les abonnements en temps réel
  void _setupRealtimeSubscriptions() {
    // Vérifier si Supabase est initialisé
    if (!supabaseService.isInitialized) {
      debugPrint(
        'Supabase n\'est pas initialisé, impossible de configurer les abonnements',
      );
      return;
    }

    // Vérifier si des abonnements sont déjà configurés
    if (_channels.isNotEmpty) {
      debugPrint('Les abonnements sont déjà configurés');
      return;
    }

    try {
      // Abonnement aux changements de clients
      final customersSubscription =
          supabaseService.client
              .channel('public:customers')
              .onPostgresChanges(
                event: PostgresChangeEvent.insert,
                schema: 'public',
                table: 'customers',
                callback: (payload) {
                  debugPrint('Nouveau client détecté: ${payload.newRecord}');
                  syncCustomers();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.update,
                schema: 'public',
                table: 'customers',
                callback: (payload) {
                  debugPrint('Client mis à jour détecté: ${payload.newRecord}');
                  syncCustomers();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.delete,
                schema: 'public',
                table: 'customers',
                callback: (payload) {
                  debugPrint('Client supprimé détecté: ${payload.oldRecord}');
                  syncCustomers();
                },
              )
              .subscribe();

      // Abonnement aux changements de tâches
      final tasksSubscription =
          supabaseService.client
              .channel('public:tasks')
              .onPostgresChanges(
                event: PostgresChangeEvent.insert,
                schema: 'public',
                table: 'tasks',
                callback: (payload) {
                  debugPrint('Nouvelle tâche détectée: ${payload.newRecord}');
                  syncTasks();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.update,
                schema: 'public',
                table: 'tasks',
                callback: (payload) {
                  debugPrint(
                    'Tâche mise à jour détectée: ${payload.newRecord}',
                  );
                  syncTasks();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.delete,
                schema: 'public',
                table: 'tasks',
                callback: (payload) {
                  debugPrint('Tâche supprimée détectée: ${payload.oldRecord}');
                  syncTasks();
                },
              )
              .subscribe();

      // Abonnement aux changements de produits
      final productsSubscription =
          supabaseService.client
              .channel('public:products')
              .onPostgresChanges(
                event: PostgresChangeEvent.insert,
                schema: 'public',
                table: 'products',
                callback: (payload) {
                  debugPrint('Nouveau produit détecté: ${payload.newRecord}');
                  syncProducts();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.update,
                schema: 'public',
                table: 'products',
                callback: (payload) {
                  debugPrint(
                    'Produit mis à jour détecté: ${payload.newRecord}',
                  );
                  syncProducts();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.delete,
                schema: 'public',
                table: 'products',
                callback: (payload) {
                  debugPrint('Produit supprimé détecté: ${payload.oldRecord}');
                  syncProducts();
                },
              )
              .subscribe();

      // Abonnement aux changements de catégories de produits
      final productCategoriesSubscription =
          supabaseService.client
              .channel('public:product_categories')
              .onPostgresChanges(
                event: PostgresChangeEvent.insert,
                schema: 'public',
                table: 'product_categories',
                callback: (payload) {
                  debugPrint(
                    'Nouvelle catégorie détectée: ${payload.newRecord}',
                  );
                  syncProductCategories();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.update,
                schema: 'public',
                table: 'product_categories',
                callback: (payload) {
                  debugPrint(
                    'Catégorie mise à jour détectée: ${payload.newRecord}',
                  );
                  syncProductCategories();
                },
              )
              .onPostgresChanges(
                event: PostgresChangeEvent.delete,
                schema: 'public',
                table: 'product_categories',
                callback: (payload) {
                  debugPrint(
                    'Catégorie supprimée détectée: ${payload.oldRecord}',
                  );
                  syncProductCategories();
                },
              )
              .subscribe();

      // Stocker les abonnements
      _channels.addAll([
        customersSubscription,
        tasksSubscription,
        productsSubscription,
        productCategoriesSubscription,
      ]);

      debugPrint('Abonnements en temps réel configurés avec succès');
    } catch (e) {
      debugPrint('Erreur lors de la configuration des abonnements: $e');
    }
  }

  // Annuler les abonnements en temps réel
  void cancelSubscriptions() {
    for (final channel in _channels) {
      channel.unsubscribe();
    }
    _channels.clear();
    debugPrint('Abonnements en temps réel annulés');
  }

  // Démarrer la synchronisation périodique
  void startPeriodicSync() {
    // Synchroniser immédiatement
    syncAll();

    // Configurer une synchronisation périodique toutes les 15 minutes
    Timer.periodic(const Duration(minutes: 15), (timer) {
      syncAll();
    });

    debugPrint('Synchronisation périodique démarrée');
  }
}
